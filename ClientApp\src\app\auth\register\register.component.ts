import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { SharedModule } from 'app/module/shared.module';
import { ConfirmPasswordValidator } from './confirm-password.validator';
import { LoginSocialComponent } from '../login-social/login-social.component';
import { SpinnerService } from 'app/service/spinner.service';
import { AuthenticationService } from '../service/authentication.service';
import { User } from 'app/dto/interface/user.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from 'app/service/toast.service';
import { TIMEZONE, timezoneMapping } from '../../utils/timezone';
import { AuthLayoutComponent } from 'app/layout/authLayout/authLayout.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
@Component({
  selector: 'app-register',
  standalone: true,
  imports: [SharedModule,
    MatFormFieldModule,
    RouterModule,
    // component
    LoginSocialComponent,
    AuthLayoutComponent,
    InnoFormInputComponent,
    InnoFormCheckboxComponent
  ],
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss'
})
export class RegisterComponent implements OnInit {
  public isShowPassword = false;
  listCompany: any[] = []
  public showPassword!: boolean;
  public registerForm!: UntypedFormGroup;
  with_button!: number;
  public businessId: string;

  private destroyRef = inject(DestroyRef);
  private router = inject(Router)
  private auth_services = inject(AuthenticationService)
  private spinnerService = inject(SpinnerService)
  private formBuilder = inject(UntypedFormBuilder)
  private _toastService = inject(ToastService)
  private activatedRoute = inject(ActivatedRoute);

  constructor(
  ) {
    this.registerForm = this.formBuilder.group({
      firstname: ["", Validators.compose([Validators.required])],
      lastname: ["", Validators.compose([Validators.required])],
      email: ["", Validators.compose([Validators.required, Validators.email])],
      password: ["", Validators.compose([Validators.required, Validators.minLength(6)])],
      confirmPassword: ["", Validators.compose([Validators.required])],
      company: [""],
      agree: [false, Validators.compose([Validators.required])],
    },
      {
        validator: ConfirmPasswordValidator("password", "confirmPassword")
      }
    );
  }

  ngOnInit() {
    if (this.auth_services.getAccessToken()) {
      this.router.navigate(['/']);
    }
    // this.activatedRoute.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
    //   if (params?.id) {
    //     this.businessId = params.id;
    //     this.auth_services.GetUserByBusinessId(params.id).subscribe({
    //       next: (val) => {
    //         if (val) {
    //           this.registerForm.patchValue({
    //             email: val.email,
    //             company: val.company
    //           });
    //           this.registerForm.get("email").disable();
    //           this.registerForm.get("company").disable();
    //         }
    //       }
    //     })
    //   }
    // });
  }

  handleToggleShowPassword() {
    this.isShowPassword = !this.isShowPassword;
  }

  get f() {
    return this.registerForm.controls as Record<string, FormControl>;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }


  onSubmit() {
    if (this.registerForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }

    this.spinnerService.show();
    const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const userZoneCode = timezoneMapping[userTimeZone] || userTimeZone;

    const userZone = TIMEZONE.find(zone => zone.Moment.includes(userZoneCode));

    let payload: User = {
      firstName: this.registerForm.controls["firstname"].value,
      lastName: this.registerForm.controls["lastname"].value,
      email: this.registerForm.controls["email"].value,
      timeZoneId: userZone?.Code,
      password: this.registerForm.controls["password"].value,
    }
    this.auth_services.Register(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.spinnerService.hide();
        this.auth_services.saveToken_cookie(res.accessToken, res.refreshToken)
        this.router.navigate(['/']);
        // if (this.registerForm.controls["company"].value && !this.businessId) {
        //   let userbusiness = {
        //     id: res.id,
        //     name: this.registerForm.controls["company"].value
        //   }
        //   this.userbusiness_services.CreateUserBusiness(userbusiness).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        //     next: (reponse) => {


        //     }
        //   })
        // }

      }
      else {
        this.spinnerService.hide();
        this._toastService.showError("Your account already exists", "Error")
      }
    }
    )
  }

}
