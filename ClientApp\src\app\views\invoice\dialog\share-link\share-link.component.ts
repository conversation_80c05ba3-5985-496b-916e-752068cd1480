import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnobookModalWrapperComponent } from './../../../../core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { SharedModule } from './../../../../module/shared.module';
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { environment } from 'environments/environment';
import { Clipboard } from '@angular/cdk/clipboard';

@Component({
  selector: 'app-share-link',
  standalone: true,
  imports: [SharedModule,
    CommonModule,
    InnobookModalWrapperComponent],
  templateUrl: './share-link.component.html',
  styleUrl: './share-link.component.scss'
})
export class ShareLinkComponent implements OnInit {
  isCopy: boolean = false;

  static getComponent(): typeof ShareLinkComponent {
    return ShareLinkComponent;
  }

  constructor(private clipboard: Clipboard, public dialogRef: MatDialogRef<ShareLinkComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {

  }
  linkShare: string = environment.HOST + "/link/"

  ngOnInit(): void {
    if (this.data) {
      this.linkShare = this.linkShare + this.data
    }
  }
  Copy() {
    this.isCopy = true
    this.clipboard.copy(this.linkShare);
  }
  closeDialog() {
    this.dialogRef.close();
  }
}
