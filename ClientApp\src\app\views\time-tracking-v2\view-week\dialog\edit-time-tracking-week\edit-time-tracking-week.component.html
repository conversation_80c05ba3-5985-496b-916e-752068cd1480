<app-inno-modal-wrapper title="Edit Time Tracking "
    (onClose)="handleClose()">

    @if(listTimeTracking.length==0) {
    <div class="w-full">
        <app-inno-empty-data
            title="No result" />
    </div>
    } @else {
    <div class="overflow-auto w-full pl-4 pr-4">
        <div class="selectProjectTableLayout">
            <div class="addBorderBottom w-full flex gap-[8px]">
                <!-- <div class="w-[16px] shrink-0">
                    <app-inno-form-checkbox
                        [checked]="listIndexInvoiceSelected.length === listInvoiceItem.length"
                        (onChange)="handleCheckedAll($event)" />
                </div> -->
                <p class="text-text-tertiary text-text-sm-semibold">
                    Description
                </p>
            </div>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                Project
            </p>

            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                Service
            </p>

            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
                Date
            </p>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
                Action
            </p>
        </div>
        @for( timeItem of listTimeTracking; track timeItem; let i =
        $index) {
        <div class="selectProjectTableLayout">

            <p class="addBorderBottom text-text-primary text-text-sm-regular">
                {{ timeItem.project?.projectName ??
                '-' }}
            </p>

            <p class="addBorderBottom text-text-primary text-text-sm-regular">
                {{ timeItem.service
                ?.serviceName??
                '-' }}
            </p>

            <p class="addBorderBottom text-text-primary text-text-sm-regular">
                {{ timeItem.endTime
                }}
            </p>

            <p
                class="addBorderBottom text-text-primary text-text-sm-regular text-right">
                {{ timeItem.date | date: _storeService.getdateFormat() }}
            </p>
            <div class="flex justify-end w-full">
                <button class="button-icon" (click)="handleEdit(timeItem)">
                    <img class="w-[20px]"
                        src="../../../assets/img/icon/ic_edit.svg"
                        alt="Icon">
                </button>
                <app-inno-popover [content]="contentPopover">
                    <button target class="button-icon">
                        <img class="w-[20px]"
                            src="../../../assets/img/icon/ic_three_dots_verticel.svg"
                            alt="Icon">
                    </button>
                </app-inno-popover>
                <ng-template #contentPopover>
                    <div class="flex w-[78px] flex-col">
                        <button
                            class="w-full h-[32px] text-text-sm-regular text-text-danger hover:bg-bg-secondary"
                            (click)="handleDelete(timeItem)">
                            Delete
                        </button>
                    </div>
                </ng-template>
            </div>

        </div>
        }
    </div>
    <app-inno-modal-footer
        (onCancel)="handleCancel()"
        (onSubmit)="handleSubmit()" />
    }

</app-inno-modal-wrapper>
