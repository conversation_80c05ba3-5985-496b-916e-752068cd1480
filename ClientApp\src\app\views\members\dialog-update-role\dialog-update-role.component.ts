import { UserBusinessService } from 'app/service/user-business.service';
import { SharedModule } from 'app/module/shared.module';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { Component, DestroyRef, inject, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { RoleMenu } from 'app/utils/menu-role';
import { RoleMember } from 'app/utils/role-member';
import { MENU_ITEMS } from 'app/utils/menu-items';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from 'app/service/toast.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-dialog-update-role',
  standalone: true,
  imports: [InnobookModalWrapperComponent, SharedModule],
  templateUrl: './dialog-update-role.component.html',
  styleUrl: './dialog-update-role.component.scss'
})
export class DialogUpdateRoleComponent implements OnInit {
  selected!: string;
  RoleMenu = RoleMenu
  ChossedMenu: any;
  destroyRef = inject(DestroyRef);
  RoleMember = RoleMember
  private userbusiness_services = inject(UserBusinessService)
  public MenuItem = MENU_ITEMS;
  private _toastService = inject(ToastService)
  private translate = inject(TranslateService)
  static getComponent(): typeof DialogUpdateRoleComponent {
    return DialogUpdateRoleComponent;
  }
  constructor(public dialogRef: MatDialogRef<DialogUpdateRoleComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {

  }
  SelectRole(value: any) {
    this.selected = value
    this.ChossedMenu = this.RoleMenu.filter(x => x.value == value);
  }


  Submit() {
    this.userbusiness_services.UpdateRoleMember(this.data?.user?.id, this.selected).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this._toastService.showSuccess(this.translate.instant('TOAST.Update'), this.translate.instant('TOAST.Success'));
          this.dialogRef.close(res);
        }
      }
    }
    )
  }
  ngOnInit(): void {
    this.selected = this.data?.role;
    this.ChossedMenu = this.RoleMenu.filter(x => x.value == this.selected);
  }
  closeDialog() {
    this.dialogRef.close();
  }
}
