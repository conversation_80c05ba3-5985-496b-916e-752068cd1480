import { UserBusinessService } from 'app/service/user-business.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AvatarModule } from 'ngx-avatars';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { UpdateRoleDialog } from '../../../service/dialog/update-role.dialog';
import { EditMemberDialog } from 'app/service/dialog/edit-profile-member.dialog';

@Component({
  selector: 'app-detail-member',
  standalone: true,
  imports: [SharedModule, RouterModule, AvatarModule],
  templateUrl: './detail-member.component.html',
  styleUrl: './detail-member.component.scss'
})
export class DetailMemberComponent implements OnInit {
  objectInfor!: UserBusiness;
  _id!: string;

  protected activatedRoute = inject(ActivatedRoute);
  public _storeService = inject(StoreService)
  private userbusiness_services = inject(UserBusinessService)
  private editMemberDialog = inject(EditMemberDialog)
  private updateRoleDialog = inject(UpdateRoleDialog)
  destroyRef = inject(DestroyRef);
  router = inject(Router);
  constructor() {

  }
  ngOnInit(): void {
    this.activatedRoute.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.id) {
        this._id = params?.id
        this.GetInforUser(params?.id);
      }

    });
  }
  GetInforUser(_id: string) {
    this.userbusiness_services.userBusinessById(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.objectInfor = res
      }

    })
  }
  handleEditMember(id: string) {
    const dialogRef = this.editMemberDialog.open(id);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetInforUser(this._id);
        }
      });
    })

  }

  OpenDialog() {
    const dialogRef = this.updateRoleDialog.open(this.objectInfor);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetInforUser(this._id);
        }
      });
    })

  }
}
