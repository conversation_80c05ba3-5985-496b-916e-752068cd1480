<div class="pageReset">
    <div class="wrapForm">
        <div class="text-center w-full text-lg font-bold pb-4">
            <span>Recover
                password</span>
        </div>
        <form [formGroup]="resetForm" (ngSubmit)="onSubmit()">

            <div class="form-group mb-3">

                <div class="relative w-full">
                    <input
                        type="password"
                        id="password"
                        formControlName="password"
                        class="w-full p-2.5 border text-sm bg-gray-50 border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 block "
                        placeholder="New password" />

                    <button
                        type="button"
                        (click)="togglePassword()"
                        class="absolute right-2 top-2 text-gray-600 hover:text-gray-900 focus:outline-none">

                        <span id="showIcon"
                            class="material-icons !hidden text-sm">
                            visibility
                        </span>

                        <span id="hideIcon" class="material-icons text-sm">
                            visibility_off
                        </span>
                    </button>
                </div>

                @if (
                (f['password'].dirty || f['password'].touched) &&
                f['password'].hasError('required')
                ) {
                <mat-error class="matError">Password is required</mat-error>
                }
                @if (
                (f['password'].dirty || f['password'].touched) &&
                f['password'].hasError('minlength')
                ) {
                <mat-error class="matError">Password minlength 6</mat-error>
                }
            </div>

            <div class="form-group mb-3">

                <div>

                    <input type="password" id="_confirmPassword"
                        formControlName="confirmPassword"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Confirm Password" required />
                </div>
                @if (
                (f['confirmPassword'].touched) &&
                f['confirmPassword'].hasError('required')
                ) {
                <mat-error class="matError">ConfirmPassword is
                    required</mat-error>
                }

                @if (
                (f['confirmPassword'].dirty ) &&
                f['confirmPassword'].hasError('confirmPasswordValidator')
                ) {
                <mat-error class="matError">Passsword and
                    Confirm Password didn't match.</mat-error>
                }

            </div>
            <button [disabled]="!resetForm.valid" type="submit"
                class="text-white w-full bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">
                Save</button>
        </form>

    </div>
</div>