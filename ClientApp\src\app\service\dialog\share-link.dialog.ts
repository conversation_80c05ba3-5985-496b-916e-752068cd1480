import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class ShareLinkDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/invoice/dialog/share-link/share-link.component'
    );

    return this.matDialog.open(
      importedModuleFile.ShareLinkComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      },
    );
  }
}
