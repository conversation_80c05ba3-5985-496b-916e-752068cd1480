.pageReset {
    width: 100vw;
    height: 100vh;
    background-image: url("../../../assets/img/bg_login.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: calc(100vw + 66px);
    transform: translateX(-66px);
    display: flex;
    justify-content: center;
    align-items: center;

    @media screen and (max-width: 860px) {
        transform: unset;
        width: 100vw;
    }

    p {
        margin-bottom: 0;
    }

    // .inputLabelerrors .mat-mdc-form-field-error {
    //   width: 100%;
    //   position: absolute;
    //   font-size: 13px;
    //   line-height: 18px;
    //   margin-top: 5px;
    //   font-weight: 400;
    //   bottom: -5px;
    //   left: 0;
    //   transform: translateY(100%);
    // }

    .linkRedirect {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #0089ef;
        text-decoration: none;
        cursor: pointer;
    }

    .linkRedirect:hover {
        text-decoration: underline;
    }

    .wrapForm {
        width: 90%;
        padding: 32px;
        background: #ffffff;
        border-radius: 16px;
        max-width: 420px;
    }

    .wrapForm .wrapLogo {
        height: auto;
        margin-left: auto;
        margin-right: auto;
        width: 100%;
        display: block;
        justify-content: center;
        align-items: center;
        background-color: #9199af;
    }

    .wrapForm .wrapLogo .logo {
        height: 100%;
        width: auto;
        object-fit: contain;
        background: #ffffff;
    }

}