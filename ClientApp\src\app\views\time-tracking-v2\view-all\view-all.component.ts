import { TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { Subscription } from 'rxjs';
import { DataService } from 'app/service/data.service';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { GroupDayTrackingComponent } from '../group-day-tracking/group-day-tracking.component';
import { formatDateFilter, getLabelDate } from 'app/helpers/common.helper';
import { InnoPaginationComponent } from 'app/component/inno-pagination/inno-pagination.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { TimeTrackingProvider } from '../time-tracking.provider';
import { TimetrackingService } from 'app/service/timetracking.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-view-all',
  templateUrl: './view-all.component.html',
  styleUrls: ['./view-all.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    GroupDayTrackingComponent,
    InnoPaginationComponent,
    InnoSpinomponent
  ]
})
export class ViewAllComponent implements OnInit, OnDestroy {

  public dataSource: any[] = []
  public totalPages = 1;
  public isLoading: boolean = false
  public totalTime: string = "00:00:00"
  public showTotalTime: boolean = false
  private dataService = inject(DataService)
  public storeService = inject(StoreService)
  private destroyRef = inject(DestroyRef);
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private timetrackingService = inject(TimetrackingService)
  private unsubscribe: Subscription[] = [];

  constructor() { }

  ngOnInit(): void {
    // Refresh list
    this.unsubscribe.push(
      this.dataService.GetTimeTrackingFilter().subscribe(async (data) => {
        if (data.typeView !== TimeTrackingViewEnum.All) return

        this.isLoading = true
        await new Promise(resolve => setTimeout(resolve, 100));
        const currentFilter = this.dataService.GetTimeTrackingFilterValue()
        currentFilter.userSelected && currentFilter.userSelected != 'all' ? this.showTotalTime = true : this.showTotalTime = false
        if (this.showTotalTime) {
          let queryParams: TimeTrackingQueryParam = {
            loggedBy: currentFilter.userSelected,
            startDate: formatDateFilter(currentFilter.startDate),
            endDate: formatDateFilter(currentFilter.endDate),

          }
          this.timetrackingService.CalculationTotalTimeByUser(queryParams).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((result: any) => {
            if (result) {
              this.totalTime = result.totalLoggedFormatted
            }
          }
          )
        }
        this.timeTrackingProvider.reloadTimeTrackingData()
          .subscribe({
            next: (res: any) => {
              this.totalPages = res?.totalPage ?? 1
              const data = res?.data ?? []
              const labelDateMap = new Map<string, any>()

              data.forEach((item: any) => {
                const labelDate = getLabelDate(item.date)
                const listTracking = labelDateMap.get(labelDate)?.listTracking ?? []
                labelDateMap.set(labelDate, { labelDate, listTracking: [...listTracking, item] })
              })

              this.dataSource = Array.from(labelDateMap.values());

            },
            complete: () => {
              this.isLoading = false
            }
          })
      })
    )
  }

  handleChangePagesize(pageSize: number) {
    this.triggerRefreshListTimeTracking()
  }

  triggerRefreshListTimeTracking() {
    this.dataService.triggerRefreshListTimeTracking()
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
