import { InforUser } from '../../../dto/interface/inforUser.interface';
import { UserBusinessService } from 'app/service/user-business.service';
import { StoreService } from 'app/service/store.service';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, EventEmitter, inject, Input, Output, SimpleChanges } from '@angular/core';
import { ReplaySubject, map } from 'rxjs';
import { FormControl } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { MemberService } from 'app/service/member.service';
import { AvatarModule } from 'ngx-avatars';

@Component({
  selector: 'app-choose-user-in-project',
  standalone: true,
  imports: [SharedModule, AvatarModule],
  templateUrl: './choose-user-in-project.component.html',
  styleUrl: './choose-user-in-project.component.scss'
})
export class ChooseUserInProjectComponent {



  @Input() searchtxt!: string;
  // Public properties
  @Input() options: any = {
    showSearch: true,//hiển thị search input hoặc truyền keyword
    keyword: '',
    data: []
  };
  @Output() ItemSelected = new EventEmitter<any>();
  @Output() IsSearch = new EventEmitter<any>();
  listMember: InforUser[] = [];
  empty: boolean = false;
  customStyle: any = [];
  public filteredMember: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public clientFilterCtrl: FormControl = new FormControl();
  destroyRef = inject(DestroyRef);
  public _storeService = inject(StoreService)
  private userbusiness_services = inject(UserBusinessService)
  constructor(
  ) { }
  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchtxt'] && changes['searchtxt'].currentValue) {
      this.filterMembers(changes['searchtxt'].currentValue);
    }
  }
  /**
   * On init
   */
  ngOnInit() {
    this.GetAllMemberInBusiness();
  }
  GetAllMemberInBusiness() {
    let payload: Parameter = {
      Page: 1,
      PageSize: 100,
      Search: "",

    }
    this.userbusiness_services.GetAllUserBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {

      if (res) {
        this.listMember = res.data.map((x: any) => x.user);
        this.filterMembers("");


      }
    }
    )
  }

  protected filterMembers(search: string) {
    if (!this.listMember) {
      return;
    }

    if (!search || search == "") {
      this.filteredMember.next(this.listMember.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    if (search[0] == '@') {
      this.filteredMember.next(
        this.listMember.filter(bank => (bank.firstName.toLowerCase()).indexOf(search.replace('@', '')) > -1)
      );
    }
    else {
      let result = this.listMember.filter(bank => bank.firstName.toLowerCase().indexOf(search) > -1)
      this.empty = result.length == 0 ? true : false;
      this.filteredMember.next(
        result
      );


    }
  }
  select(user: any) {
    this.ItemSelected.emit(user)
  }
  stopPropagation(event: any) {
    this.IsSearch.emit(event)
  }
}
