import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddMemberBusinessDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/add-member-business/add-member-business.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddMemberBusinessComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        width: '550px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
