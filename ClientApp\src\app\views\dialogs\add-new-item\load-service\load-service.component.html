

@if(listService.length==0) {
<div class="w-full">
    <app-inno-empty-data
        [title]="'EMPTY.NoResult'" />
</div>
} @else {
<app-inno-modal-wrapper>
    <div
        class="container-full  flex items-center justify-between flex-wrap gap-2">

    </div>
    <div class="overflow-auto w-full p-[16px]">
        <div class="selectProjectTableLayout">
            <div class="addBorderBottom w-full flex gap-[8px]">
                <div class="w-[16px] shrink-0">
                    <app-inno-form-checkbox
                        [checked]="listIndexInvoiceSelected.length === listService.length"
                        (onChange)="handleCheckedAll($event)" />
                </div>
                <p class="text-text-tertiary text-text-sm-semibold">
                    {{'SELECTTIMETRACKING.Table.ServiceName'|translate}}
                </p>
            </div>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                {{'SELECTTIMETRACKING.Table.Rate'|translate}}
            </p>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                {{'SELECTTIMETRACKING.Table.Qty'|translate}}
            </p>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                {{'SELECTTIMETRACKING.Table.Description'|translate}}
            </p>
            <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                {{'SELECTTIMETRACKING.Table.Taxes'|translate}}
            </p>
            <p (click)="sortDates('createdAt')"
                [ngClass]="{'font-bold text-black': sortColumn === 'createdAt'}"
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right cursor-pointer">
                {{'SELECTTIMETRACKING.Table.Date'|translate}}
                @if(sortColumn=='createdAt')
                {
                <span class="material-icons pl-1 !text-[15px]">
                    {{ sortDirection === 'Ascending' ? 'arrow_upward' :
                    'arrow_downward'
                    }}
                </span>
                }
            </p>

        </div>
        @for(item of listService; track item.id; let
        i =
        $index) {
        <div class="selectProjectTableLayout">
            <div class="addBorderBottom w-full flex gap-[8px]">
                <div class="w-[16px] shrink-0">
                    <app-inno-form-checkbox
                        [checked]="isCheckedIndex(i)"
                        (onChange)="handleToggleCheckedIndex(i)" />
                </div>
                <p class="text-text-primary text-text-sm-regular text-wrap">
                    {{ item.serviceName ?? '' }}
                </p>
            </div>
            <p
                class="addBorderBottom text-text-primary text-text-sm-regular">
                {{
                item.rate
                ??
                '0' }}
            </p>
            <app-inno-form-input
                class="addBorderIsTable"
                type="number"
                [isTable]="true"
                placeholder="Enter the qty"
                (onChange)="handleQtyIndex($event,i)" />
            <p
                class="addBorderBottom text-text-primary text-text-sm-regular text-center text-wrap">
                {{ item.description ?? '' }}
            </p>
            <p
                class="addBorderBottom text-text-primary text-text-sm-regular text-center text-wrap">
                {{ getNameSelectedTaxes(item?.taxes) }}
            </p>
            <p
                class="addBorderBottom text-text-primary text-text-sm-regular text-center">
                {{ item.createdAt | date: _storeService.getdateFormat() }}
            </p>
        </div>
        }
    </div>
    <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
    <div footer>
        <app-inno-modal-footer
            [isDisableSubmit]="!listIndexInvoiceSelected.length"
            (onCancel)="handleCancel()"
            (onSubmit)="handleSubmit()" />
    </div>
</app-inno-modal-wrapper>
}
