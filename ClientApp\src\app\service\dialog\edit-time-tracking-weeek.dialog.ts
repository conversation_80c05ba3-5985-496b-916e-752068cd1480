import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class EditTimeTrackingWeeekDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/time-tracking-v2/view-week/dialog/edit-time-tracking-week/edit-time-tracking-week.component'
        );

        return this.matDialog.open(
            importedModuleFile.EditTimeTrackingWeekComponent.getComponent(),
            {
                panelClass: 'custom_dialog',
                width: "70%",
                data,
                disableClose: true,
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
