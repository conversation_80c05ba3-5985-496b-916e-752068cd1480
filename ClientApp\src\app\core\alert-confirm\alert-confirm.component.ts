import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { IAlertConfirm } from 'app/service/dialog/alert-confirm.dialog';

@Component({
  selector: 'app-alert-confirm',
  templateUrl: './alert-confirm.component.html',
  styleUrls: ['./alert-confirm.component.scss'],
  standalone: true,
  imports: [
    InnoModalWrapperComponent,
    InnoModalFooterComponent
  ]
})
export class AlertConfirmComponent {

  public classNameSubmitButton?: string = ''

  static getComponent(): typeof AlertConfirmComponent {
    return AlertConfirmComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<AlertConfirmComponent>,
    @Inject(MAT_DIALOG_DATA) public data?: IAlertConfirm
  ) {
    this.classNameSubmitButton = data?.classNameSubmitButton ?? ''
  }
  closeDialog() {
    this.dialogRef.close();
  }
  handleCancel() {
    this.dialogRef.close(false);
  }

  handleSubmit() {
    this.dialogRef.close(true);
  }
}
