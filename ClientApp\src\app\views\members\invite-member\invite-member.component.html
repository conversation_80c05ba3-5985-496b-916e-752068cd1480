<div class=" flex flex-col p-3 ">
    <div class="grid grid-cols-2 gap-4">
        <div>
            <div>
                <h5 class="line-clamp-1">Invite {{email}}</h5>
                <ul
                    class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200">
                    @for(item of RoleMember; track item)
                    {
                    <li class="cursor-pointer" (click)="SelectRole(item.value)">
                        <div
                            class="flex p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600 text-start cursor-pointer">
                            <div class="flex items-center h-5">
                                <input id="helper-radio-4"
                                    [(ngModel)]="selected"
                                    type="radio" [value]="item.value"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                            </div>
                            <div class="ms-2 text-sm cursor-pointer">
                                <label
                                    class="font-medium text-gray-900 dark:text-gray-300">
                                    <div>{{item.name}}</div>
                                    <p
                                        class="text-xs font-normal text-gray-500">{{item.depression}}</p>
                                </label>
                            </div>
                        </div>
                    </li>
                    }

                </ul>
            </div>
            <div class="mb-4">
                <label for="email"
                    class="block mb-2 text-sm font-medium text-gray-300">To</label>
                <input [(ngModel)]="email" disabled type="text" id="email"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" />
            </div>

        </div>
        <div class="flex justify-center">
            <div class="border p-3 w-80">
                <h5>{{selected}}</h5>

                <div class="flex flex-col mt-4">
                    @for(item of ChossedMenu;track item; let vi=$index)
                    {
                    <span>{{item.depression}}</span>
                    @for(it of item?.role;track it; let i=$index)
                    {

                    <div class="flex items-center mt-2">
                        @if(it.check)
                        {
                        <span class="material-icons text-green-500">
                            done
                        </span>
                        }
                        @else
                        {
                        <span class="material-icons text-red-500">
                            not_interested
                        </span>
                        }

                        <span class="pl-3">{{it.text}}</span>
                    </div>
                    }
                    }

                </div>
            </div>
        </div>
    </div>
    <div class="border mt-3">
        <div class="p-3 ">
            <div>
                Hey <strong>{{email}}</strong> <br>
                We’re using innoBook to create estimates and invoices for
                clients,
                track the team's time and manage projects.
            </div>
            <div>
                <a style="color: rgb(25, 113, 236);text-decoration: underline;"
                    href="javascript:void(0)">{{Host}}</a>
            </div>
            <div>
                This gives you access to our FreshBooks account. Start tracking
                your
                time and expenses in there - it's really easy to use.
                <br>
                Thanks !

            </div>
        </div>
    </div>
    <div class="mt-4 flex justify-center">
        <button (click)="Cancel()" type="button"
            class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Cancel</button>
        <button
            type="button"
            (click)="SendInvite()"
            class="  bg-green-600 hover:bg-green-700focus:outline-none text-white focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Invite</button>
    </div>
</div>
