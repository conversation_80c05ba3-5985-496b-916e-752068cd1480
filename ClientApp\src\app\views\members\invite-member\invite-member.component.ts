import { StoreService } from 'app/service/store.service';
import { SpinnerService } from './../../../service/spinner.service';
import { MemberService } from 'app/service/member.service';
import { RoleMenu } from './../../../utils/menu-role';
import { SharedModule } from 'app/module/shared.module';
import { MENU_ITEMS } from './../../../utils/menu-items';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { RoleMember } from 'app/utils/role-member';
import { environment } from 'environments/environment';
import { MatDialog } from '@angular/material/dialog';
import { DoneInviteDialog } from '../../../service/dialog/done-invite.dialog';
import { UserBusinessService } from '../../../service/user-business.service';

@Component({
  selector: 'app-invite-member',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './invite-member.component.html',
  styleUrl: './invite-member.component.scss'
})
export class InviteMemberComponent implements OnInit {
  protected activatedRoute = inject(ActivatedRoute);
  private userBusinessService = inject(UserBusinessService);
  public _storeService = inject(StoreService)
  private spinnerService = inject(SpinnerService)
  Host: string = environment.HOST;
  destroyRef = inject(DestroyRef);
  router = inject(Router);
  private dialog = inject(MatDialog)
  email!: string;
  firstName!: string;
  lastName!: string;
  selected: string = "Acountant";
  RoleMenu = RoleMenu
  ChossedMenu: any
  RoleMember = RoleMember
  public MenuItem = MENU_ITEMS;
  constructor(private doneInviteDialog: DoneInviteDialog) {
    this.ChossedMenu = this.RoleMenu.filter(x => x.value == this.selected);
  }
  SelectRole(value: any) {
    this.selected = value
    this.ChossedMenu = this.RoleMenu.filter(x => x.value == value);
  }


  SendInvite() {
    let payload = {
      email: this.email,
      role: this.selected,
      firstName: this.firstName,
      lastName: this.lastName,
    }
    this.spinnerService.show();
    this.userBusinessService.SendMailAddMember(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.spinnerService.hide();
          this.OpenDialogDone();
          // this.userbusiness_services.AddMemberBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
          // })

        }
      }
    });
  }
  Cancel() {
    this.router.navigateByUrl("/members")
  }
  OpenDialogDone() {
    
    const dialogRef = this.doneInviteDialog.open({});
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res: any) => {
        if (res) {
          this.router.navigate(['/members']);
        }
      });
    })
  };
  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.email) {
        this.email = params.email;
      }
      if (params?.firstName) {
        this.firstName = params.firstName;
      }
      if (params?.lastName) {
        this.lastName = params.lastName
      }
      if (params?.role) {
        this.SelectRole(params.role);
      }
    });
  }
}
