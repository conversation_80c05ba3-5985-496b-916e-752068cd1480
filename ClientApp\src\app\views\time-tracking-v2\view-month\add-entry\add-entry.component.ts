import { Component, inject, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoEnterHoursComponent } from 'app/component/inno-enter-hours/inno-enter-hours.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { InnoSelectSearchTagsComponent } from 'app/component/inno-select-search-tags/inno-select-search-tags.component';
import { InnoTagsComponent } from 'app/component/inno-tags/inno-tags.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { TimeTrackingProvider } from '../../time-tracking.provider';
import { DataService } from 'app/service/data.service';
import { SharedModule } from 'app/module/shared.module';
import { getLabelDate } from 'app/helpers/common.helper';
import { InnoSelectSearchServiceComponent } from 'app/component/inno-select-search-service/inno-select-search-service.component';

@Component({
  selector: 'app-add-entry',
  templateUrl: './add-entry.component.html',
  styleUrls: ['./add-entry.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoSelectSearchProjectComponent,
    InnoFormTextareaComponent,
    InnoSelectSearchTagsComponent,
    InnoTagsComponent,
    InnoFormCheckboxComponent,
    InnoSelectSearchServiceComponent,
    InnoEnterHoursComponent
  ]
})
export class AddEntryComponent implements OnInit, OnDestroy {
  public previewServiceInfo?: IFilterDropdownOption
  public previewWorkingInfo?: IFilterDropdownOption
  public previewDescription?: string = ''
  public previewDate?: Date = undefined;
  public labelDate?: string = ''
  public previewBillable?: boolean = false
  public previewTimeEnd?: string = '';
  public projectId: string = ''

  private dataService = inject(DataService)
  private timeTrackingProvider = inject(TimeTrackingProvider)

  static getComponent(): typeof AddEntryComponent {
    return AddEntryComponent;
  }

  constructor(
    private dialogRef: MatDialogRef<AddEntryComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) { }

  ngOnInit(): void {
    this.previewDate = this.data?.date
    if (this.previewDate) {
      this.labelDate = getLabelDate(this.previewDate)
    }
  }

  handleSelectProject(item: IFilterDropdownOption) {
    this.previewWorkingInfo = item
    this.projectId = item.value
    const billable = item.metadata?.project?.billable
    this.previewBillable = billable
  }

  handleChangeNote(description: any) {
    this.previewDescription = description
  }

  handleChangeBillable(value: any) {
    this.previewBillable = value
  }

  handleChangeEndTime(value: string) {
    this.previewTimeEnd = value
  }

  handleAddTimeTrackingRecord() {

    const payload: any = {
      endTime: this.previewTimeEnd,
      billable: this.previewBillable ?? false,
      date: this.previewDate,
      description: this.previewDescription ?? '',
      clientId: this.previewWorkingInfo?.metadata?.objectClient?.id,
      projectId: this.previewWorkingInfo?.metadata?.type == 'project' ? this.previewWorkingInfo?.value : null,
      serviceId: this.previewServiceInfo?.value
    }

    this.timeTrackingProvider.handleCreateTimeTracking({
      payload,
      optional: {
        callbackSuccess: () => {
          this.dataService.triggerRefreshListTimeTracking()
          this.dialogRef.close();
        }
      }
    })
  }
  handleSelectServices(item: IFilterDropdownOption) {
    this.previewServiceInfo = item
  }

  handleCancel() {
    this.dialogRef.close();
  }

  ngOnDestroy(): void {
  }
}
