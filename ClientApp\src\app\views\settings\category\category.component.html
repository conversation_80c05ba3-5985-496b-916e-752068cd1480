<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <div class="flex items-center gap-[8px]">
      <button class="button-icon button-size-md" (click)="handleBack()">
        <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
      </button>
      <p class="text-text-primary text-headline-lg-bold">
        {{'CATEGORY.Title'| translate}}
      </p>
    </div>

    <button class="button-size-md button-primary" (click)="OpenDialog()">
      <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
      {{'CATEGORY.TitleAdd'| translate}}
    </button>
  </div>
</div>

<div class="container-full mt-[32px]">
  <div class="w-full max-w-[300px]">
    <app-inno-input-search [value]="search"
      (onChange)="handleSearch($event)" />
  </div>
</div>
@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
  <app-inno-spin />
</div>
} @else {
<div class="w-full mt-[12px]">
  <ejs-grid #grid class="customTable"
    [dataSource]="dataSource"
    (rowSelecting)="onRowSelecting($event)"
    [allowSelection]="true"
    [allowSorting]="true"
    [sortSettings]='sortOptions'
    (actionBegin)="onActionBegin($event)"
    (rowDeselecting)="onRowDeselecting($event)"
    [selectionSettings]="selectionOptions">
    <e-columns>
      <!-- <e-column type="checkbox" width="30"></e-column> -->
      <e-column [headerText]="'CATEGORY.GIRD.CategoryName' | translate"
        width="200" field="categoryName">
        <ng-template #template let-data>
          <span class="cursor-pointer">{{data.categoryName}}</span>
        </ng-template>
      </e-column>
      <e-column
        [headerText]="'CATEGORY.GIRD.CreateDate' | translate"
        width="100" field="createdAt">
        <ng-template #template let-data>
          <span class="cursor-pointer">{{data.createdAt | date:
            _storeService.getdateFormat() }}</span>
        </ng-template>
      </e-column>
      <e-column field='action' headerText width="100">
        <ng-template #template let-item>
          <app-inno-table-action
            (onEdit)="handleEdit(item.id)"
            (onDelete)="handleDelete(item)" />
        </ng-template>
      </e-column>
    </e-columns>
  </ejs-grid>

  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>
}
