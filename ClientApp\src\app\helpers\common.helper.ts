import moment from 'moment-timezone';

export const formatDateFilter = (date?: Date | string): string => {
  if (!date) return '';
  const formattedDate = moment(date).format('MM/DD/YYYY');
  return formattedDate;
}

export const currentTimeZone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

export const formatParamsQuery = (params: any, isaddTimeZone = true): any => {
  if (!params) return {};
  
  const query = {
    ...params,
    ...(isaddTimeZone && {
      timeZone: currentTimeZone(),
    }),
  };
  Object.keys(query).forEach(key => (query[key] === null || query[key] === undefined) && delete query[key]);
  
  return query;
}

export const getStartAndEndOfWeek = (date?: Date) => {
  date = date ?? new Date();

  const timeZone = moment.tz.guess();
  const startOfWeek = moment.tz(date, timeZone).startOf('isoWeek');
  const endOfWeek = moment(startOfWeek).add(6, 'days').endOf('day')

  return {
    startOfWeek: formatDateFilter(startOfWeek.toDate()),
    endOfWeek: formatDateFilter(endOfWeek.toDate())
  };
}

export const getStartAndEndOfMonth = (date?: Date) => {
  date = date ?? new Date();
  const timeZone = moment.tz.guess();
  const startOfMonth = moment.tz(date, timeZone).startOf('month');
  const endOfMonth = moment.tz(date, timeZone).endOf('month');

  return {
    startOfMonth: formatDateFilter(startOfMonth.toDate()),
    endOfMonth: formatDateFilter(endOfMonth.toDate())
  };
}

export const getWeekDays = (startDate: string, endDate: string) => {
  const timeZone = moment.tz.guess();
  const daysOfWeek: { [key: string]: string } = {};

  let current = moment.tz(startDate, 'MM/DD/YYYY', timeZone);

  const end = moment.tz(endDate, 'MM/DD/YYYY', timeZone);

  while (current.isSameOrBefore(end, 'day')) {
    const dayName = current.format('dddd');
    const formattedDate = formatDateFilter(current.toDate());
    daysOfWeek[dayName] = formattedDate;

    current.add(1, 'day');
  }

  return daysOfWeek;
}

export const isSameDate = (date1: Date, date2: Date) => {

  const timeZone = moment.tz.guess();
  const moment1 = moment.tz(date1, timeZone).startOf('day');
  const moment2 = moment.tz(date2, timeZone).startOf('day');

  return moment1.isSame(moment2);
}

export const sumHours = (timeArray: string[]) => {
  let totalMinutes = 0;

  timeArray.forEach(time => {
    const [hours, minutes] = time.split(':').map(Number);
    totalMinutes += (hours * 60) + minutes;
  });

  const totalHours = Math.floor(totalMinutes / 60);
  const remainingMinutes = totalMinutes % 60;

  return `${String(totalHours).padStart(2, '0')}:${String(remainingMinutes).padStart(2, '0')}`;
}

export const convertHoursToDecimal = (timeString: string) => {
  const parts = timeString.split(":").map(Number);
  let hours = 0, minutes = 0, seconds = 0;
  if (parts.length === 3) {
    [hours, minutes, seconds] = parts; // HH:MM:SS
  } else if (parts.length === 2) {
    [hours, minutes] = parts; // HH:MM (default seconds to 0)
  } else if (parts.length === 1) {
    [hours] = parts; // HH (default minutes and seconds to 0)
  }
  return hours + minutes / 60 + seconds / 3600;
}

export const formatNumber = (input: string | number) => {
  let number;

  if (typeof input === 'number') {
    number = input;
  } else if (typeof input === 'string' && !isNaN(Number(input))) {
    number = Number(input);
  } else {
    return '0';
  }

  return number.toLocaleString('de-DE');
}

export const getLabelDate = (inputDate: Date) => {
  const now = new Date();

  if (isSameDate(inputDate, now)) {
    return "Today";
  }
  if (isSameDate(inputDate, moment(now).subtract(1, 'day').toDate())) {
    return "Yesterday";
  }
  if (isSameDate(inputDate, moment(now).add(1, 'day').toDate())) {
    return "Tomorrow";
  }

  return moment.tz(inputDate, moment.tz.guess()).format('D MMMM YYYY');
};

export function debounceHandler<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>;

  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    const context = this;
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
}

export const getBase64AndFileName = async (file: File):
  Promise<{ base64: string, fileName: string, type: string }> => {

  if (!file) {
    return Promise.resolve({ base64: '', fileName: '', type: '' });
  }

  const reader = new FileReader();
  reader.readAsDataURL(file);

  return new Promise((resolve) => {
    reader.onload = () => {
      const base64 = reader.result as string;
      const fileName = file.name;
      const type = file.type;
      resolve({ base64, fileName, type });
    };
  });
}

export const calculateElapsedTime = (args: { startTime: Date | string, endTime: Date | string }) => {
  const { startTime, endTime } = args;

  const timeZone = moment.tz.guess();

  const startMoment = moment.tz(startTime, timeZone);
  const endMoment = moment.tz(endTime, timeZone);

  if (!startMoment.isValid() || !endMoment.isValid()) {
    return { hours: 0, minutes: 0, seconds: 0 };
  }

  const duration = moment.duration(endMoment.diff(startMoment));
  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();
  const seconds = duration.seconds();

  return { hours, minutes, seconds };
}
export const getFullAddressClient = (args?: {
  addressLine1?: string,
  addressLine2?: string,
  townCity?: string,
  stateProvince?: string,
  country?: string,
}) => {
  if (!args) return ''

  const {
    addressLine1 = '',
    addressLine2 = '',
    townCity = '',
    stateProvince = '',
    country = '',
  } = args

  return [
    addressLine1,
    addressLine2,
    townCity,
    stateProvince,
    country
  ].filter(x => x).join(', ')
}
export const getFullAddress = (args?: {
  addressLine1?: string,
  addressLine2?: string,
  city?: string,
  townCity?: string,
  stateProvince?: string,
  postalCode?: string,
  country?: string,
}) => {
  if (!args) return ''

  const {
    addressLine1 = '',
    addressLine2 = '',
    city = '',
    townCity = '',
    stateProvince = '',
    postalCode = '',
    country = '',
  } = args

  return [
    addressLine1,
    addressLine2,
    city,
    townCity,
    stateProvince,
    postalCode,
    country
  ].filter(x => x).join(', ')
}

export const formatTimeHoursFromSeconds = (totalSeconds?: number) => {
  if (!totalSeconds) return '00:00:00'

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const secondsLeft = totalSeconds % 60;

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secondsLeft).padStart(2, '0')}`;
}

export const formatSecondsFromTimeHours = (timeString: string) => {
  if (!timeString?.split(':').length) return 0;

  const [hours = 0, minutes = 0, seconds = 0] = timeString.split(':').map(Number);
  return (hours * 3600) + (minutes * 60) + seconds;
}

export function isValidDateRange(startDate?: Date | string, endDate?: Date | string) {
  if (!startDate || !endDate) return false

  if (typeof startDate === 'string') startDate = new Date(startDate)
  if (typeof endDate === 'string') endDate = new Date(endDate)

  const timeZone = moment.tz.guess();
  const start = moment.tz(startDate, timeZone);
  const end = moment.tz(endDate, timeZone);

  if (!start.isValid() || !end.isValid()) {
    return false;
  }

  return start.isBefore(end);
}
