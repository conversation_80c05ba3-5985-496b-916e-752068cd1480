import { DestroyRef, Injectable, OnDestroy, inject } from '@angular/core';
import {
    Router,
    ActivatedRouteSnapshot,
    RouterStateSnapshot,
    RouteConfigLoadStart,
    RouteConfigLoadEnd,
    CanActivateFn
} from '@angular/router';
import { AuthenticationService } from '../auth/service/authentication.service';
import { SpinnerService } from '../service/spinner.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Observable, of } from 'rxjs';
import { Role } from 'app/enum/role.enum';

@Injectable({ providedIn: 'root' })
class RoleService {
    private router = inject(Router);
    private authenticationService = inject(AuthenticationService);
    private spinnerService = inject(SpinnerService);
    destroyRef = inject(DestroyRef);

    constructor() {
      // to show a spinner on lazy load route
      this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((event) => {
        if (event instanceof RouteConfigLoadStart) {
          this.spinnerService.show();
        } else if (event instanceof RouteConfigLoadEnd) {
          this.spinnerService.hide();
        }
      })
    }
    canActivate(
        route: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ): Observable<boolean> {
        // Authenticate
        const accessToken = this.authenticationService.getAccessToken();
        if (!accessToken) {
            this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
            return of(false);
        }

        // Authorize
        const roles = route.data['roles'];
        if (roles && roles.includes(Role.All)) {
            return of(true);
        }
        const userRole = this.authenticationService.getBusinessRole();
        if (userRole && roles && roles.includes(userRole)) {
            return of(true);
        }

        this.router.navigate(['/']);
        return of(false);
    }


}
export const RoleGuard: CanActivateFn = (
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
): Observable<boolean> => {
    return inject(RoleService).canActivate(next, state);
};
