import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from '../../module/shared.module';
import { ActivatedRoute, Router } from '@angular/router';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';

@Component({
  selector: 'app-inno-pagination',
  templateUrl: './inno-pagination.component.html',
  styleUrls: ['./inno-pagination.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent
  ]
})
export class InnoPaginationComponent {

  @Input() totalPages: number = 1;
  @Input() currentPagesize: number = 10;
  @Output() onChangePagesize = new EventEmitter();
  @Output() callbackGoToPage = new EventEmitter();

  public currentPage: number = 1;

  constructor(
    private _route: ActivatedRoute,
    private _router: Router
  ) {
    this._route.queryParams.subscribe(params => {
      this.currentPage = params['page'] ? + params['page'] : 1;
    });
  }

  ngOnChanges(changes: any) {
    if(changes.totalPages){
      if(changes.totalPages.previousValue && changes.totalPages.previousValue !== changes.totalPages.currentValue) {
        this.goToPage(1)
      }
    }
  }

  handleChangePagesize(event: any) {
    // this.onChangePagesize.emit(parseInt(event.target.value))
  }

  goToPage(page: number){
    this._router.navigate([], {
      relativeTo: this._route,
      queryParams: { page: page },
      queryParamsHandling: 'merge',
    });

    this.callbackGoToPage.emit()
  }
}
