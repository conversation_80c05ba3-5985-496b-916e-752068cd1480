<app-innobook-modal-wrapper (onClose)="closeDialog()">

    <form class="text-start p-3 customform" [formGroup]="newPaymentForm"
        (ngSubmit)="onSubmit()">

        <div class="mb-4">
            <h5 class="fw-bold m-0">Add a Payment</h5>
        </div>
        @if(!data)
        {
        <div class="mb-3">
            <label for="client"
                class="block mb-2 text-sm font-medium text-gray-900">Select
                Invoices
            </label>
            <select id="inoive"
                (change)="handleSelectInvoice($event)"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                <option selected>Choose a invoice</option>
                @for(option of listInvoice; track option; let i =
                $index
                )
                {
                <option [value]="option.id">
                    <div class="flex flex-col">
                        <label>{{ option.invoiceNumber }} - </label>
                        <span
                            class="text-gray-300">{{option.client.clientName}}</span>
                    </div>

                </option>
                }

            </select>
        </div>

        }
        <div class="grid gap-4 sm:grid-cols-2 mb-6">

            <div class="w-full">
                <label for="paymentAmount"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment
                    Amount</label>
                <input type="text" name="paymentAmount" id="paymentAmount"
                    formControlName="paymentAmount"
                    (blur)="transformAmount($event)"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5"
                    placeholder="Payment Amount" required>
                @if (
                (f['paymentAmount'].dirty || f['paymentAmount'].touched) &&
                f['paymentAmount'].hasError('required')
                ) {
                <mat-error class="matError">Payment Amount is
                    required</mat-error>
                }
            </div>
            <div class="w-full">
                <label for="paymentDate"
                    class="block mb-2 text-sm font-medium text-gray-900 ">Payment
                    Date</label>
                <input
                    type="date"
                    formControlName="paymentDate"
                    required
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" />
                @if (
                (f['paymentDate'].dirty || f['paymentDate'].touched) &&
                f['paymentDate'].hasError('required')
                ) {
                <mat-error class="matError">Payment Date is required</mat-error>
                }
            </div>
        </div>

        <div class="mb-6">
            <label for="client"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment
                Method

            </label>
            <select id="client"
                (change)="handleSelectMethod($event)"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700">

                @for(option of PaymentMethod; track option; let i =
                $index
                )
                {
                <option [value]="option.value">
                    <label>{{ option.name }}</label>
                </option>
                }

            </select>
        </div>
        <div class="mb-6">
            <label for="_email"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Payment
                Note
            </label>
            <input type="text" id="paymentNote"
                formControlName="paymentNote"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                placeholder="Payment Note" />

        </div>
        <div class="mb-6 flex w-full items-center">
            <div class="text-start">
                <input type="checkbox" id="checkbox"
                    formControlName="paymentSendNotifi"
                    class="bg-gray-50 border w-5 h-5 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block  p-2.5" />
            </div>
            <span class="ml-2">
                Send client a payment notification email
            </span>

        </div>

        <hr>
        <div class="w-full text-center mt-6 flex justify-around">
            <button type="button"
                (click)="closeDialog()"
                class=" font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-cente hover: border">Cancel</button>
            <button type="submit"
                [disabled]="!newPaymentForm.valid"
                [ngClass]="{'bg-green-700 hover:bg-green-800' : newPaymentForm.valid,'bg-gray-400': !newPaymentForm.valid}"
                class="text-white  font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center">Save</button>
        </div>

    </form>
</app-innobook-modal-wrapper>