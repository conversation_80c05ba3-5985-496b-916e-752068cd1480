<div class="w-full flex flex-col relative">
    @if(label) {
    <label [class.required]="isRequired"
        class="text-text-secondary text-text-sm-semibold mb-[2px]">{{ label
        }}</label>
    }

    <app-inno-popover
        position="bottom-start"
        [content]="templateSearchProject"
        [isClickOnContentToClose]="false"
        [isClearPadding]="true"
        (onOpen)="touchControl()"
        (onClose)="callbackAfterHideSearchResult()">
        <button target class="dropdown-md w-full">
            <div
                class="w-full text-left line-clamp-1 text-text-placeholder-slight"
                [ngClass]="{'text-text-primary': labelOfValueSelected!! }">
                {{ labelOfValueSelected || placeholder }}
            </div>
            <img class="shrink-0"
                src="../../../../assets/img/icon/ic_arrow_down_gray.svg"
                alt="Icon">
        </button>
    </app-inno-popover>
    <ng-template #templateSearchProject>
        <div class="w-full max-w-[90dvw]">
            <app-inno-input-search-result
                placeholder="Search..."
                (onChange)="handleSearch($event)"
                [data]="listOptionPreview"
                [isNotFound]="!listOptionPreview.length"
                [isEmptyData]="!listOptionOriginal.length"
                [optionTemplate]="customOptionTemplate ?? optionTemplate"
                [footerTemplate]="isShowCreateButton ? buttonCreateNew : null">
                <ng-template #optionTemplate let-item>
                    <div
                        (click)="handleChooseOption(item)"
                        class="w-full flex p-[8px] my-1 items-center gap-[10px] hover:bg-bg-secondary rounded-md cursor-pointer"
                        [class.selected]="item.value === value">
                        <div class="w-full">
                            <p
                                class="line-clamp-1 text-text-primary text-teapp-inno-input-search-resultxt-sm-regular txtTitle">
                                {{ item.label }}
                            </p>
                            @if(item.description) {
                            <p
                                class="line-clamp-1 text-text-tertiary text-text-xs-regular txtDescription">
                                {{ item.description }}
                            </p>
                            }
                        </div>
                    </div>
                </ng-template>
                <ng-template #buttonCreateNew>

                </ng-template>

            </app-inno-input-search-result>
        </div>
    </ng-template>

    <app-inno-error-message [message]="getErrorMessage()" />
</div>
