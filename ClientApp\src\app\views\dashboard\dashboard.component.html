<!-- Header page -->
<div class=" container-full w-full py-[24px] border-b border-border-primary">
    <div
        class="flex justify-between items-center flex-wrap">
        <span class="text-text-primary  font-bold text-xl">
            {{'DASHBOARD.Title'|translate}}
        </span>

        <button class="button-size-md button-primary">
            {{'DASHBOARD.Explore'|translate}}
        </button>
    </div>
</div>
<!-- End Header page -->

<div class="container-full mt-[24px]">
    <!-- Statistics Section -->
    <div class="grid grid-cols-4 gap-4 mb-6 mxw1100:grid-cols-1">
        <!-- Stat Card -->
        <div class="bg-white p-4 rounded shadow">
            <h6
                class="text-gray-500 uppercase text-sm">{{'DASHBOARD.Value'|translate}}</h6>
            <div class="flex items-center justify-between mt-2">
                <span class="text-2xl font-bold">$0</span>

            </div>
        </div>
        <!-- Repeat similar cards for other stats -->
        <div class="bg-white p-4 rounded shadow">
            <h6
                class="text-gray-500 uppercase text-sm">{{'DASHBOARD.Users'|translate}}</h6>
            <div class="flex items-center justify-between mt-2">
                <span class="text-2xl font-bold">{{CountMember}}</span>

            </div>
        </div>
        <div class="bg-white p-4 rounded shadow">
            <h6 class="text-gray-500 uppercase text-sm">
                {{'DASHBOARD.Orders'|translate}} </h6>
            <div class="flex items-center justify-between mt-2">
                <span class="text-2xl font-bold">0</span>

            </div>
        </div>
        <div class="bg-white p-4 rounded shadow">
            <h6 class="text-gray-500 uppercase text-sm">
                {{'DASHBOARD.Tickets'|translate}} </h6>
            <div class="flex items-center justify-between mt-2">
                <span class="text-2xl font-bold">0</span>

            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="bg-white p-4 rounded shadow w-full h-full">
        <app-revenue-expenses-chart></app-revenue-expenses-chart>
    </div>

    <div class="bg-white p-4 rounded shadow mt-4 w-full h-full ">
        <div class="flex items-center justify-center">
            <app-graphics-chart></app-graphics-chart>
        </div>
    </div>
</div>
