<div class="w-full pb-3">
  <!-- Header page -->
  <div class="w-full py-[24px] border-b border-border-primary">
    <div class="container-full flex justify-between items-center flex-wrap gap-2">
      <p class="text-text-primary text-headline-lg-bold">
        Billing & Plans
      </p>
      <button class="button-size-md button-primary" (click)="managePlan()">
        Manager Your Plan
      </button>
    </div>
  </div>
  <!-- End Header page -->

  <!-- Tabs -->
  <div class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">
    <app-inno-tabs [tabs]="tabs" [value]="currentTab" (onChange)="handleChangeTab($event)" />
  </div>

  @if(currentTab === TYPE_TAB.YOUR_PLAN) {
    <div class="container-full mt-[24px]">
      <div class="grid grid-cols-1 md:grid-cols-2">
        <!-- Bill Summary Section -->
        <div class="p-6">
          <div class="border border-border-primary rounded-md p-6">
            @if(planType !== 0) {
              <div class="flex items-center mb-4 warning-text-bg p-2 rounded-md">
                <div class="flex-shrink-0 mr-2">
                  <img src="assets/img/icon/ic_warning.svg" alt="Clock" class="w-5 h-5" />
                </div>
                <p class="text-text-primary text-text-md-regular">Your next {{ planType === 1 ? 'monthly' : 'yearly' }} invoice will be issued {{nextStatementDate}}</p>
              </div>
            }

            <div class="mb-4">
              <p class="text-text-secondary text-text-sm-regular">Current plan</p>
              <p class="text-text-primary text-headline-sm-bold">{{planName}}</p>
            </div>

            <div class="border-t border-border-primary pt-4 mb-4">
              <div class="flex justify-between items-center mb-2">
                <p class="text-text-primary text-text-md-regular">Total</p>
                <p class="text-text-primary text-text-md-bold">${{planAmount}} /mo</p>
              </div>

              <div class="flex justify-between items-center mb-2">
                <p class="text-text-primary text-text-md-regular">Team members x{{teamMembersCount}}</p>
                <p class="text-text-primary text-text-md-regular">${{addOnsAmount}} /mo</p>
              </div>

              <div class="flex justify-between items-center mb-2">
                <p class="text-text-primary text-text-md-regular">Customers available</p>
                <p class="text-text-primary text-text-md-regular">{{clientProfilesTotal === 0 ? 'Unlimited' : (clientProfilesUsed + ' of ' + clientProfilesTotal)}}</p>
              </div>
            </div>

            <div class="border-t border-border-primary pt-4">
              <div class="flex justify-between items-center">
                <p class="text-text-primary text-headline-sm-bold">Total</p>
                <div>
                  <p class="text-text-primary text-headline-sm-bold text-right">${{totalAmount}} /mo</p>
                  @if(renewType === 2) {
                    <p class="text-text-primary text-headline-sm-bold text-right">${{yearlyAmount}} /year</p>
                  }
                </div>
              </div>
            </div>

            @if(planType !== 0) {
              <div class="mt-4">
                <button class="button-link-primary text-red-500" (click)="cancelAccount()">
                  Cancel Plan
                </button>
              </div>
            }
          </div>
        </div>

        <!-- Team Members Section -->
        <div class="p-6">
          <div class="border border-border-primary rounded-md p-6">
            <div class="mb-4">
              <h2 class="text-text-primary text-headline-sm-bold mb-2">Team Members: ${{addOnsAmount}} /mo</h2>
              <p class="text-text-secondary text-text-sm-regular">View and manage team members included in your paid plan.</p>
            </div>

            <!-- Team Member List -->
            <div class="mb-4">
              <!-- Team Member 1 -->
              <div class="flex justify-between items-center py-3 border-b border-border-primary">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                    <span class="text-purple-700">Ad</span>
                  </div>
                  <div>
                    <p class="text-text-primary text-text-md-regular">Andou Designer</p>
                    <p class="text-text-secondary text-text-sm-regular">{{planName}}</p>
                  </div>
                </div>
                <button class="text-text-secondary">
                  <img src="assets/img/icon/ic_three_dots_verticel.svg" alt="More" class="w-5 h-5" />
                </button>
              </div>

              <!-- Team Member 2 -->
              <div class="flex justify-between items-center py-3 border-b border-border-primary">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                    <span class="text-blue-700">Fc</span>
                  </div>
                  <div>
                    <p class="text-text-primary text-text-md-regular">FredCanadaviet</p>
                    <p class="text-text-secondary text-text-sm-regular">{{planName}}</p>
                  </div>
                </div>
                <button class="text-text-secondary">
                  <img src="assets/img/icon/ic_three_dots_verticel.svg" alt="More" class="w-5 h-5" />
                </button>
              </div>
            </div>

            <!-- Add Member Button -->
            <button class="button-size-md button-outline-primary flex items-center" (click)="addTeamMember()">
              <span class="mr-1">+</span> Add Member
            </button>
          </div>

          <!-- Payment Method Section -->
          <div class="border border-border-primary rounded-md p-6 mt-6">
            <div class="mb-4">
              <h2 class="text-text-primary text-headline-sm-bold mb-2">Payment Method</h2>
              <p class="text-text-secondary text-text-sm-regular">Manage and update your stored payment options.</p>
            </div>

            <div class="flex justify-between items-center py-3 border-b border-border-primary">
              <div class="flex items-center">
                <div class="flex-shrink-0 mr-3">
                  <img src="assets/img/icon/visa.svg" alt="Visa" class="w-8 h-8" />
                </div>
                <div>
                  <p class="text-text-primary text-text-md-regular">Visa **** **** {{cardLastFour}}</p>
                  <p class="text-text-secondary text-text-sm-regular">Expires {{cardExpiryDate}}</p>
                </div>
              </div>
              <p class="text-text-primary text-text-md-bold">${{totalAmount}} /mo</p>
            </div>

            <div class="mt-4">
              <button class="button-link-primary flex items-center" (click)="updatePaymentMethod()">
                <img src="assets/img/icon/ic_edit.svg" alt="Edit" class="w-4 h-4 mr-1" /> Update Payment Method
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Separator line -->
      <div class="border-t border-border-primary my-8"></div>

      <!-- Other Offers Section -->
      <div class="p-6">
        <h2 class="text-text-primary text-headline-sm-bold mb-4">Other Offers</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <!-- Refer and Earn Card -->
          <div class="border border-border-primary rounded-md p-6">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 bg-amber-100 rounded-full p-3">
                <img src="assets/img/icon/gift.svg" alt="Gift" class="w-6 h-6" />
              </div>
              <div>
                <h3 class="text-text-primary text-text-md-bold mb-2">Refer and Earn</h3>
                <p class="text-text-primary text-text-md-regular mb-1">Earn a $100 service credit for each qualified referral you complete!</p>
                <p class="text-text-primary text-text-sm-regular"><a href="javascript:void(0)" class="button-link-primary d-inline-block" (click)="managePlan()">Refer a Friend</a> today.</p>
              </div>
            </div>
          </div>

          <!-- Pay Yearly Card -->
          <div class="border border-border-primary rounded-md p-6">
            <div class="flex items-start gap-4">
              <div class="flex-shrink-0 bg-blue-100 rounded-full p-3">
                <img src="assets/img/icon/calculator.svg" alt="Calculator" class="w-6 h-6" />
              </div>
              <div>
                <h3 class="text-text-primary text-text-md-bold mb-2">Pay Yearly <span class="text-green-500 text-text-sm-regular">Save 10%</span></h3>
                <p class="text-text-primary text-text-md-regular mb-1">Switch to one easy payment per year.</p>
                <button class="button-link-primary" (click)="switchToYearly()">
                  Switch to Yearly
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Need to bill more section -->
      <div class="border-t border-border-primary pt-6 mt-6 text-center">
        <h3 class="text-text-primary text-text-md-bold mb-2">Need to bill more than 50 clients? <a href="javascript:void(0)" class="button-link-primary d-inline-block" (click)="upgradeToPremium()">
          Upgrade to Premium
        </a></h3>

        <p class="text-text-primary text-text-sm-regular mb-4">
          You'll still have access to all the features you love on the Plus package, with a little more room for your business to grow.
        </p>


      </div>
    </div>
  }

  <!-- Receipts Tab -->
  @if(currentTab === TYPE_TAB.RECEIPTS) {
    <div class="container-full mt-[24px]">
      <div class="border border-border-primary rounded-md p-6">
        <h2 class="text-text-primary text-headline-sm-bold mb-4">Receipts</h2>

        <!-- Receipts table would go here -->
        <p class="text-text-primary text-text-md-regular text-center py-8">
          You don't have any receipts yet. They will appear here once you've been billed.
        </p>
      </div>
    </div>
  }
</div>
