import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class DetailCategoryDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/detail-category/detail-category.component'
    );

    return this.matDialog.open(
      importedModuleFile.DetailCategoryComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        width: '550px',
        data,
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
