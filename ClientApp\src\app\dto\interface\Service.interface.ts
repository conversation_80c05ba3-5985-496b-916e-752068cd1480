import { Project } from "./project.interface";

export interface Service {
    serviceName: string;
    projectId: string;
    description?: any;
    rate: number;
    totalHours: number;
    actualHours: number;
    project: Project;
    timeTrackings?: any;
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy?: any;
    taxes?: any
}

export interface ICreateService {
    id?: string,
    serviceName?: string;
  //  projectId?: string;
    description?: any;
    rate?: number;
    totalHours?: number;
    actualHours?: number;
    project?: Project;
    timeTrackings?: any;
    taxes?: any
}
