import { StoreService } from 'app/service/store.service';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { InvoiceService } from 'app/service/invoice.service';
import { Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { ActionEventArgs, GridAllModule, GridComponent, Pager, PagerDropDown, PagerModule, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { skip, Subscription } from 'rxjs';
import { AvatarModule } from 'ngx-avatars';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SortGird } from 'app/dto/interface/SortGird.interface';
Pager.Inject(PagerDropDown);

@Component({
  selector: 'app-estimate-send-to-me',
  standalone: true,
  imports: [
    SharedModule,
    InnoStatusComponent,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InnoPopoverComponent,
    InnoEmptyDataComponent,
    InnoTableActionComponent,
    RouterModule,
    AvatarModule,
    GridAllModule,
    PagerModule,
    InnoSpinomponent,
    DecimalPipe
  ],
  providers: [LayoutUtilsService],
  templateUrl: './estimate-send-to-me.component.html',
  styleUrl: './estimate-send-to-me.component.scss'
})
export class EstimateSendToMeComponent implements OnInit, OnDestroy {
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public isLoading = false;
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any

  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  private translate = inject(TranslateService);
  private _invoiceService = inject(InvoiceService)
  private dataService = inject(DataService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        const page = this.currentPage
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        const textSearch = ''
        this.sort ? this.GetInvoiceSendToMe({ page, textSearch, filter }) : this.GetInvoiceSendToMe({ page, textSearch });
      }
      else {
        this.currentPage = 1
        const page = this.currentPage
        const textSearch = ''
        this.GetInvoiceSendToMe({ page, textSearch });
      }

    });

    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().pipe(skip(1)).subscribe((data) => {
        if (data?.typeView !== InvoiceViewEnum.Created_Tab) return;
        const page = this.activatedRoute.snapshot.queryParams['page'] || 1;
        const textSearch = data?.textSearch ?? ''
        this.GetInvoiceSendToMe({ page, textSearch });
        this.currentPage = page
      }
      )
    )
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      const page = this.currentPage
      const filter = {
        Sort: JSON.stringify(this.sort)
      }
      const textSearch = ''
      this.sort ? this.GetInvoiceSendToMe({ page, textSearch, filter }) : this.GetInvoiceSendToMe({ page, textSearch });
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
      this.dataService.triggerRefreshInvoice()
    }
  }

  GetInvoiceSendToMe(args?: { page?: number, textSearch?: string, filter?: any }) {
    const payload: Parameter = {
      Page: args?.page ?? 1,
      Search: args?.textSearch ?? '',
      PageSize: this.pageSizesDefault,
      Filter: args?.filter

    }
    this.isLoading = true
    this._invoiceService.GetAllEstimateSendToMe(payload).subscribe({
      next: (res) => {
        if (res) {
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };
          }

        }
      },
      complete: () => {
        this.isLoading = false
      }
    })
  }

  handleEdit(item: any) {
    this.router.navigate(['/invoices', item.id])
  }


  handleDelete(item) {
    const _title = this.translate.instant('Delete Estimate !');
    const _description = this.translate.instant('Do you want to delete?');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([item.id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.GetInvoiceSendToMe()
          this._toastService.showSuccess("Delete", "Success");
        }
        else {
          this._toastService.showError("Fail", "Fail");
        }
      })
    })
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        const page = this.currentPage
        const textSearch = ''
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.GetInvoiceSendToMe({ page, textSearch, filter });
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      const page = this.currentPage
      const textSearch = ''

      this.GetInvoiceSendToMe({ page, textSearch });
    }
  }


  ngOnDestroy(): void {

    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
