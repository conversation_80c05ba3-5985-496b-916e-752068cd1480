import { Component, DestroyRef, inject } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { SharedModule } from 'app/module/shared.module';
import { SpinnerService } from 'app/service/spinner.service';
import { ToastService } from 'app/service/toast.service';
import { AuthenticationService } from '../service/authentication.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AuthLayoutComponent } from 'app/layout/authLayout/authLayout.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [
    SharedModule,
    RouterModule,
    MatFormFieldModule,
    AuthLayoutComponent,
    InnoFormInputComponent
  ],
  templateUrl: './forgot-password.component.html',
  styleUrl: './forgot-password.component.scss'
})
export class ForgotPasswordComponent {
  resetPasswordForm!: UntypedFormGroup;
  destroyRef = inject(DestroyRef);
  private translate = inject(TranslateService)
  private formBuilder = inject(UntypedFormBuilder)
  private auth_services = inject(AuthenticationService)
  private spinnerService = inject(SpinnerService)
  private _toastService = inject(ToastService)
  constructor() {
    this.resetPasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.resetPasswordForm.controls as Record<string, FormControl>;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }


  onSubmit() {
    if (this.resetPasswordForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }

    this.spinnerService.show();
    this.auth_services.RecoverPassword(this.resetPasswordForm.controls["email"].value).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      this._toastService.showSuccess("Save", this.translate.instant("User.Reset.Password.SendMail"))
      this.spinnerService.hide();
    });

  }


}
