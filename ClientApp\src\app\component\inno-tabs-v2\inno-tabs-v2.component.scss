.buttonTabItem {
  padding-left: 12px;
  padding-right: 12px;
  height: 100%;
  flex-shrink: 0;
  position: relative;
  border: 1px solid transparent;
  background-color: transparent;
  transition: all .3s;

  @apply rounded-sm;
  @apply text-text-sm-semibold;
  @apply text-text-tertiary;
}

.buttonTabItem.active {
  @apply text-text-brand-primary;
  @apply bg-bg-primary;
  @apply border-border-primary;
}

.buttonTabItem:hover {
  @apply text-text-brand-primary;
}

