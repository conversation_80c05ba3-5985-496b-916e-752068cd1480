<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <div class="flex items-center gap-[8px]">
      <button class="button-icon button-size-md" (click)="handleBack()">
        <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
      </button>
      <p class="text-text-primary text-headline-lg-bold">
        {{'SETTINGS.TaxAndFinancialInformation.Title' | translate}}
      </p>
    </div>
  </div>
</div>

<div class="container-full py-[20px]">
  <div class="w-full flex flex-col gap-[16px]">
    <app-inno-form-select-search
      [label]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.BaseCurrency' | translate"
      [placeholder]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.BaseCurrencyPlaceholder' | translate"
      [options]="currencyOptions"
      [isDisableSearch]="true"
      [value]="selectedCurrency"
      (onSelect)="handleChangeCurrency($event)" />

    <div class="w-full grid md:grid-cols-2 gap-[16px] md:gap-[12px]">
      <app-inno-form-select-search
        [label]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.FiscalYearStartMonth' | translate"
        [placeholder]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.FiscalYearStartMonthPlaceholder' | translate"
        [options]="monthOptions"
        [value]="selectedMonth"
        (onSelect)="handleChangeMonth($event)" />

      <app-inno-form-select-search
        [label]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.FiscalYearEndMonth' | translate"
        [placeholder]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.FiscalYearEndMonthPlaceholder' | translate"
        [options]="dayOptions"
        [value]="selectedMonth"
        (onSelect)="handleChangeDay($event)" />
    </div>

    <div class="flex ">
      <app-inno-form-input
        type="number"
        [label]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.StandardRate' | translate"
        class="w-full"
        [placeholder]="'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.StandardRatePlaceholder' | translate"
        [value]="rate"
        (onChange)="handleChangeRate($event)" />

      <div
        class="flex-shrink-0 flex items-end f-full ml-2 pb-2 text-text-md-medium">
        {{
        'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.StandardRateUnit'
        | translate }}
      </div>
    </div>
  </div>

  <div class="flex justify-center mt-[20px]">
    <button (click)="Submit()" class="button-primary button-size-md">
      {{
      'SETTINGS.TaxAndFinancialInformation.TaxAndFinancialInformationForm.SaveChanges'
      | translate }}
    </button>
  </div>
</div>
