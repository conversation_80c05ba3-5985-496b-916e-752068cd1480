<div
  class="w-full max-w-[160px] px-[8px] py-[6px] flex items-center gap-[8px] cursor-default rounded-md hover:bg-bg-brand-primary transition-all"
  [ngClass]="{'bg-bg-brand-primary': value!!}"
  >
  @if(value) {
    <img class="w-[16px] shrink-0" src="../../../assets/img/icon/ic_tags_green.svg" alt="Icon">
  } @else {
    <img class="w-[16px] shrink-0" src="../../../assets/img/icon/ic_tags_black.svg" alt="Icon">
  }
  <span class="text-text-sm-medium line-clamp-1" [ngClass]="{'text-text-brand-primary': value!!}" >
    {{ value ? value : placeholder }}
  </span>
</div>
