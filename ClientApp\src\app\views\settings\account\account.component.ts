import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { UserBusinessService } from 'app/service/user-business.service';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { StoreService } from 'app/service/store.service';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { SharedModule } from 'app/module/shared.module';
import { MatMenuModule } from '@angular/material/menu';
import { AvatarModule } from 'ngx-avatars';
import { GridComponent, GridModule, PagerModule, Pager, PagerDropDown, PagerComponent, GridAllModule, ActionEventArgs, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { Location } from '@angular/common';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-account',
  standalone: true,
  imports: [InnoPopoverComponent, InnoTableActionComponent, InnoSpinomponent,
    InnoInputSearchComponent, SharedModule, PagerModule, MatMenuModule, AvatarModule, SharedModule, GridModule, GridAllModule],
  providers: [LayoutUtilsService],
  templateUrl: './account.component.html',
  styleUrl: './account.component.scss'
})
export class AccountComponent implements OnInit, OnDestroy {
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public isLoading = false;
  public data?: object[];
  private _subscriptions: Subscription[] = [];
  private searchSubject = new Subject<string>();
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  public columnSelection = false;
  dataSource: any;
  public totalPages = 0;
  public pageSizes = [10, 20, 50, 100];
  pageSizesDefault: number = 10
  public currentPage: number = 1
  search: string = ""
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any

  destroyRef = inject(DestroyRef);
  router = inject(Router);
  private translate = inject(TranslateService);
  private layoutUtilsService = inject(LayoutUtilsService);
  private _authServices = inject(AuthenticationService)
  private _toastService = inject(ToastService)
  protected activatedRoute = inject(ActivatedRoute);
  public storeService = inject(StoreService)
  private userbusinesServices = inject(UserBusinessService)

  constructor(private location: Location,
  ) { }

  ngOnDestroy(): void {
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.GetAllMemberInBusiness(this.currentPage, "", filter)
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllMemberInBusiness(this.currentPage, "");
    }

  }

  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.sort ? this.GetAllMemberInBusiness(this.currentPage, "", filter) : this.GetAllMemberInBusiness(this.currentPage, "");
      }
      else {
        this.GetAllMemberInBusiness(this.currentPage, "")
      }

    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      if (search) {
        this.search = search;
        this.GetAllMemberInBusiness(this.currentPage, this.search)
      }
      else {
        this.search = "";
        this.GetAllMemberInBusiness(this.currentPage, "")
      }
    });
    this._subscriptions.push(sb)
  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllMemberInBusiness(this.currentPage, "")
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }
  GetAllMemberInBusiness(page: number, search: string, filter?: any) {
    this.isLoading = true
    let payload: Parameter = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: search,
      Filter: filter

    }
    this.userbusinesServices.GetAllUserBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.isLoading = false
        this.totalPages = res.totalRecords
        this.dataSource = res.data
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };

        }
      }
    }
    )
  }

  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  GetFullName(data: any) {
    return data.firstName + " " + data.lastName
  }
  creaFormDelete(item: any) {
    const _title = this.translate.instant('ACCOUNT.DeleteUser');
    const _description = this.translate.instant('COMMON.ConfirmDelete');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this.userbusinesServices.DeleteMemberInBusiness(item?.user?.id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          if (res) {
            this.GetAllMemberInBusiness(this.currentPage, "")
            this._toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
            return;
          }
          this._toastService.showError(this.translate.instant("TOAST.Fail"));
        }
      });
    })
  }

  handleBack() {
    this.location.back();
  }
}
