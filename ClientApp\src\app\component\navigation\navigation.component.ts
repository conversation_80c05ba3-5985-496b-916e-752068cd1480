import { CompanyService } from './../../service/company.service';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { UserBusinessService } from 'app/service/user-business.service';
import { Component, OnInit, DestroyRef } from "@angular/core";
import {
  NavigationEnd,
  Router,
  RouterModule,
} from "@angular/router";
import { SharedModule } from "../../module/shared.module";
import { MENU_ITEMS, MenuItem } from "../../utils/menu-items";
import { MatMenuModule } from "@angular/material/menu";
import { SwitchWorkspaceComponent } from "app/views/dialogs/switch-workspace/switch-workspace.component";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { StoreService } from 'app/service/store.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import {
  DropDownButtonModule,
  ItemModel,
  MenuEventArgs,
} from "@syncfusion/ej2-angular-splitbuttons";
import { SwitchWorkspaceDialog } from '../../service/dialog/switch-workspace.dialog';
import { Role } from 'app/enum/role.enum';
import { MatTooltip } from '@angular/material/tooltip';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: "app-navigation",
  templateUrl: "./navigation.component.html",
  styleUrls: ["./navigation.component.scss"],
  standalone: true,
  imports: [
    DropDownButtonModule,
    RouterModule,
    SharedModule,
    MatTooltip,
    MatMenuModule,
    SwitchWorkspaceComponent,
  ],
})
export class NavigationComponent implements OnInit {
  public listGroupMenu: Array<{ group?: string, menus: any[] }> = []
  public languages: ItemModel[] = [
    {
      text: "English",
      id: 'en',
      iconCss: 'img-icon-en'
    },
    {
      text: "Français",
      id: 'fr',
      iconCss: 'img-icon-fr'
    },
  ];
  public userOptions: ItemModel[] = [
    {
      text: "Profile",
    },
    {
      text: "Log Out",
    },
  ];
  public isOpenSidebar = true;

  objectBusiness: UserBusiness | null = null
  RoleBusiness!: string;
  _userBusiness!: UserBusiness[]
  public currentLanguage = "English";
  constructor(
    private router: Router,
    private translate: TranslateService,
    private companyService: CompanyService,
    private businessService: UserBusinessService,
    private storeService: StoreService,
    private destroyRef: DestroyRef,
    private authenticationService: AuthenticationService,
    private switchWorkspaceDialog: SwitchWorkspaceDialog
  ) {
    this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((c) => {
      if (c instanceof NavigationEnd) {
        MENU_ITEMS.forEach((item) => {
          let childMatch = false;
          item.children?.forEach((ch) => {
            ch.active = c.urlAfterRedirects === ch.url;
            childMatch = childMatch ? childMatch : ch.active;
          });
          item.active = childMatch || c.urlAfterRedirects.includes(item.url);
          item.expand = childMatch || c.urlAfterRedirects.includes(item.url);
        });
      }
    })
    if (localStorage.getItem("lang") == "fr") {
      this.currentLanguage = "Français"
    }
  }

  hasPermission(requiredPermissions: string[]): boolean {
    const userPermissions = this.RoleBusiness;
    return requiredPermissions.some(permission => userPermissions != null && (permission === Role.All || permission?.toLowerCase() === userPermissions.toLocaleLowerCase()));
  }

  expand(item: MenuItem, event: any) {
    item.expand = !item.expand;
    event.stopPropagation();
  }

  openOtherWorkSpace() {

    const dialogRef = this.switchWorkspaceDialog.open({});
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.router.navigate(['/']).then(() => {
            window.location.reload();
          });
        }
      })
    });
  }

  ChooseBusiness(item: any) {
    let data = {
      businessId: item.businessId,
    }
    this.storeService.setChooseBusiness(data);
    this.GetBusinessById(item.businessId);
  }

  GetUserBusiness() {
    this.businessService.GetUserBusiness().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this._userBusiness = res;
        if (this._userBusiness.length > 0) {
          let data = {
            businessId: this._userBusiness[0].businessId,
            companyId: this._userBusiness[0].companyId
          }
          this.ChooseBusiness(data)
        }
        else {
          this.RoleBusiness = null;
          this.LoadMenu();
        }
      }

    }
    )
  }
  LoadMenu() {
    const leftMenu = MENU_ITEMS.filter((item: any) =>
      this.hasPermission(item.permissions)
    );

    this.listGroupMenu = [
      {
        group: 'Tracking',
        menus: leftMenu.filter((item: any) => item.group == 'Tracking')
      },
      {
        group: 'Analyze',
        menus: leftMenu.filter((item: any) => item.group == 'Analyze')
      },
      {
        group: 'Manager',
        menus: leftMenu.filter((item: any) => item.group == 'Manager')
      }
    ]

  }
  ClearCache(id: string) {
    if (id != "time_tracking" && (localStorage.getItem("cacheService") || localStorage.getItem("cacheClientProject"))) {
      localStorage.removeItem("cacheClientProject")
      localStorage.removeItem("cacheService")
    }

    if (window.innerWidth < 860) {
      this.isOpenSidebar = false
    }
  }

  GetCurrencyCompany() {
    this.companyService.GetCurrencyCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res && res.currency != '') {
        this.storeService.setCurencyCompany(res.currency);
      }
    });
  }
  GetBusinessById(businessId: string) {
    this.businessService.GetBusinessById(businessId).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.objectBusiness = res;
        this.storeService.set_UserBusiness(res);
        if (res.dateFormat && res.dateFormat != "") {
          this.storeService.setdateFormat(res.dateFormat)
        }
        this.RoleBusiness = this.objectBusiness?.role ?? ''
        this.storeService.setRoleBusiness(this.RoleBusiness)
        this.LoadMenu();

        // Re-generate token after select business to load user's role in that business
        this.authenticationService.UpdateAccessToken({ BusinessId: this.objectBusiness.businessId }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
          if (res) {
            this.authenticationService.saveToken_cookie(res.accessToken, res.refreshToken)

            this.GetCurrencyCompany();
          }
        });

      }
    }
    )
  }
  public select(args: MenuEventArgs) {
    switch (args.item.text) {
      case "Log Out":
        this.authenticationService.logout();
        break;
      case "Profile":
        this.router.navigate(["/profile"]);
        break;
      default:
        break;
    }
  }
  ngOnInit() {
    const business = this.storeService.getChooseBusiness();
    if (!business) {
      this.GetUserBusiness();
    }
    else {
      this.GetBusinessById(JSON.parse(business)?.businessId);
    }

    if (window.innerWidth < 860) {
      this.isOpenSidebar = false
    }
  }
  handleClickOnUser(args: MenuEventArgs) {
    switch (args.item.text) {
      case "Log Out":
        this.authenticationService.logout();
        break;
      case "Profile":
        this.router.navigate(["/profile"]);
        break;
      default:
        break;
    }
  }
  selectLanguage(args: MenuEventArgs) {
    this.translate.use(args.item.id);
    this.currentLanguage = args.item.text
    localStorage.setItem("lang", args.item.id)
    window.location.reload();
  }

  handleToggleSidebar() {
    this.isOpenSidebar = !this.isOpenSidebar
  }
}
