import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { GetProjectRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
import { Project } from '../dto/interface/project.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { CalculationResponse } from '../dto/interface/calculationResponse.interface';
import { formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class ProjectService {

  private http = inject(HttpClient)
  constructor() { }

  CreateProject(payload: any): Observable<Project> {
    return this.http.post<Project>(UrlApi + '/Project/CreateProject', payload);
  }
  UpdateArchive(projectId: any, isArchive: boolean): Observable<Object> {
    return this.http.post(UrlApi + `/Project/UpdateArchive?projectId=${projectId}&isArchive=${isArchive}`, {});
  }

  GetAllProjectAsync(param: GetProjectRequestParam): Observable<PaginatedResponse<Project>> {
    const query = formatParamsQuery(param);
    return this.http.get<PaginatedResponse<Project>>(UrlApi + `/Project/GetAllProjectAsync`, { params: query });
  }
  DeleteProject(payload: any, isActive: boolean): Observable<Object> {
    return this.http.post(UrlApi + `/Project/DeleteProject?isActive=${isActive}`, payload);
  }
  UpdateProject(payload: any): Observable<Object> {
    return this.http.post(UrlApi + '/Project/UpdateProject', payload);
  }
  CalculationProject(): Observable<CalculationResponse<Project>> {
    return this.http.get<CalculationResponse<Project>>(UrlApi + '/Project/CalculationProject');
  }
  GetProjectById(projectId: string): Observable<Project> {
    return this.http.get<Project>(UrlApi + `/Project/GetProjectById?projectId=${projectId}`);
  }
}
