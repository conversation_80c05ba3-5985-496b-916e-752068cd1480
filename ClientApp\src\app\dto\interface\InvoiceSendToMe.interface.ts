export interface InvoiceSendToMe {
    invoiceId: string;
    clientId: string;
    companyId: string;
    email: string;
    isEstimate: boolean;
    listEmail?: any;
    invoice: Invoice;
    client?: any;
    company: Company;
    user?: any;
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy?: any;
}
interface Company {
    businessName: string;
    phone: string;
    email: string;
    country: string;
    adress?: any;
    adress2?: any;
    city?: any;
    province?: any;
    postalCode?: any;
    timeZone: string;
    startWeekOn?: any;
    dateFormat?: any;
    note: string;
    rate?: any;
    fiscalMonth?: any;
    currency?: any;
    fiscalDay?: any;
    clients?: any;
    userBusinesses?: any;
    projects?: any;
    members?: any;
    invoices?: any;
    taxes?: any;
    payments?: any;
    expenses?: any;
    merchants?: any;
    categorys?: any;
    categoryItems?: any;
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy: string;
}
interface Invoice {
    companyId: string;
    clientId: string;
    projectId?: any;
    itemId?: any;
    invoiceNumber: string;
    isEstimate: boolean;
    img?: any;
    invoiceDate: string;
    dueDate: string;
    reference: string;
    notes: string;
    totalAmount: number;
    rate: number;
    paidAmount: number;
    taxAmount: number;
    timeAmount: number;
    status: number;
    base64?: any;
    filename?: any;
    taxes?: any;
    payments?: any;
    itemInvoices?: any;
    client?: any;
    company?: any;
    item?: any;
    project?: any;
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy?: any;
}