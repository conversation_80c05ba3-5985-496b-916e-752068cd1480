import { Component, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SharedModule } from 'app/module/shared.module';
import { DataService } from 'app/service/data.service';
import { Subscription } from 'rxjs';
import { InnoPaginationComponent } from 'app/component/inno-pagination/inno-pagination.component';
import { GroupDayTrackingComponent } from '../group-day-tracking/group-day-tracking.component';
import { TimeTrackingProvider } from '../time-tracking.provider';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';

@Component({
  selector: 'app-view-day',
  templateUrl: './view-day.component.html',
  styleUrls: ['./view-day.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoSpinomponent,
    GroupDayTrackingComponent,
    InnoPaginationComponent
  ]
})

export class ViewDayComponent implements OnInit, OnDestroy {

  public dataSource = []
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  public isLoading: boolean = false
  public labelDateSelected: string = ''

  private dataService = inject(DataService)
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private unsubscribe: Subscription[] = [];

  constructor() { }

  ngOnInit(): void {
    // Refresh list
    this.unsubscribe.push(
      this.dataService.GetTimeTrackingFilter().subscribe((data) => {
        if (data.typeView !== TimeTrackingViewEnum.Day) return

        this.isLoading = true
        this.timeTrackingProvider.reloadTimeTrackingData().subscribe({
          next: (res) => {
            this.totalPages = res?.totalPage ?? 1
            this.dataSource = res?.data ?? []

          },
          complete: () => {
            this.isLoading = false
          }
        })
      })
    )
  }

  handleChangePagesize(pageSize: number) {
    this.pageSizesDefault = pageSize
    this.triggerRefreshListTimeTracking()
  }

  triggerRefreshListTimeTracking() {
    this.dataService.triggerRefreshListTimeTracking()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
