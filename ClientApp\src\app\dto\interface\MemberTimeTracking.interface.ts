import { Project } from "./project.interface";
import { TimeTracking } from "./timeTracking.interface";

export interface MemberTimeTracking {
    userId: string;
    projectId: string;
    companyId: string;
    status: number;
    leftAt?: any;
    user?: any;
    project: Project;
    company?: any;
    timeTrackings: TimeTracking[];
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    updatedBy?: any;
}
