.btnBase {
  cursor: pointer;
  text-decoration: none;
  background: transparent;
  border: 1px solid transparent;
  outline: none;
}

.btnBase:disabled {
  background-color: #EDF0F6 !important;
  color: #9199AF !important;
  pointer-events: none !important;
}

// Default
.newZipplexButton {
  border-radius: 8px;
  height: 48px;
  font-size: 16px;
  line-height: 18px;
  font-weight: 700;
  padding-left: 24px;
  padding-right: 24px;
  text-align: center;
  display: flex;
  justify-content: center;
  gap: 12px;
  align-items: center;

  img {
    width: 20px;
    height: 20px;
    object-fit: cover;
    flex-shrink: 0;
  }
}

.newZipplexButton:hover {
  transition: all 0.3s;
}

/* ========== VARIANT ========== */
// Action table
.newZipplexButton.actionTable {
  height: 24px;
  border-radius: 5px;
  white-space: nowrap;
  font-size: 12px;
  gap: 4px;
  padding-left: 4px;
  padding-right: 4px;

  img {
    width: 16px;
    height: 16px;
    object-fit: cover;
  }
}

.newZipplexButton.actionTable:hover {
  background: #edf0f7;
  border: 1px solid #edf0f7;
  color: #0f182e;
  outline: 1px solid #0f182e;
}


// Primary
.newZipplexButton.primary {
  background-color: #0089EF;
  color: white;
}

.newZipplexButton.primary:hover {
  background-color: #189bfd;
}

// Secondary
.newZipplexButton.secondary {
  background-color: #F2F4F9;
  color: #0F182E;
  border: 1px solid #DEE2EB
}

.newZipplexButton.secondary:hover {
  background-color: #DEE2EB;
}

.newZipplexButton.secondary.active {
  color: #DEE2EB;
  background-color: #0F182E;
}

// Outline
.newZipplexButton.outlinePrimary {
  background-color: #DEF1FF;
  color: #0089EF;
  border: 1px solid #0089EF
}

.newZipplexButton.outlinePrimary:hover,
.newZipplexButton.outlinePrimary.active {
  background-color: #0089EF;
  color: white;

  img {
    filter: brightness(0) invert(1);
  }
}

// Outline white
.newZipplexButton.outlineWhite {
  background-color: #FFFFFF;
  color: #0F182E;
  border: 1px solid #DEE2EB
}

.newZipplexButton.outlineWhite:hover,
.newZipplexButton.outlineWhite.active {
  background-color: #DEE2EB;
}


/* ========== SIZE ========== */

// Medium
.newZipplexButton.md {
  height: 40px;
  font-size: 14px;
  line-height: 16px;
  padding-left: 12px;
  padding-right: 12px;
  gap: 12px;
  font-weight: 700;

  img {
    width: 16px;
    height: 16px;
    object-fit: cover;
  }
}
