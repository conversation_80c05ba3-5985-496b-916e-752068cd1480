import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class EditMemberDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/members/edit-profile-team-members/edit-profile-team-members.component'
        );

        return this.matDialog.open(
            importedModuleFile.EditProfileTeamMembersComponent.getComponent(),
            {
                panelClass: 'custom_dialog',
                width: '450px',
                data,
                disableClose: true,
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
