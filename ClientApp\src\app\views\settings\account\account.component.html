<!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
    <div class="container-full">
        <div class="flex items-center gap-[8px]">
            <button class="button-icon button-size-md" (click)="handleBack()">
                <img src="../../../../assets/img/icon/ic_arrow_left.svg"
                    alt="Icon">
            </button>
            <p class="text-text-primary text-headline-lg-bold">
                {{'ACCOUNT.Title'|translate}}
            </p>
        </div>
    </div>
</div>
<!-- End Header page -->

<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
    <div class="w-full max-w-[300px]">
        <app-inno-input-search [value]="search"
            (onChange)="handleSearch($event)" />
    </div>
</div>
@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
    <app-inno-spin />
</div>
} @else {
<div class="w-full mt-[12px]">
    <ejs-grid class="customTable" #grid
        [allowSorting]="true"
        [sortSettings]='sortOptions'
        (actionBegin)="onActionBegin($event)"
        [dataSource]="dataSource">
        <e-columns>
            <!-- <e-column type="checkbox" width="30"></e-column> -->
            <e-column [headerText]="'ACCOUNT.GIRD.Name' | translate" width="200"
                field="FirstName">
                <ng-template #template let-data>
                    @if(data.user)
                    {

                    <div class="flex items-center cursor-pointer">
                        @if(data.user?.firstName && data.user?.lastName)
                        {
                        <ngx-avatars [size]="30"
                            bgColor="{{storeService.getBgColor(data.user.firstName.slice(0,1))}}"
                            [name]="GetFullName(data.user)"></ngx-avatars>
                        <span class="ml-2"> {{data.user.firstName}}
                            {{data.user.lastName}}</span>
                        }
                        @else{
                        <ngx-avatars [size]="30"
                            bgColor="{{storeService.getBgColor(data.user.email.slice(0,1))}}"
                            [name]="data.user.email.slice(0,1)"></ngx-avatars>
                        <span class="ml-2"> {{data.user.email}}</span>
                        }
                    </div>
                    }
                </ng-template>
            </e-column>
            <e-column [headerText]="'ACCOUNT.GIRD.Role' | translate" width="150"
                field="role">
                <ng-template #template let-data>
                    <div>
                        <span>{{data.role}}</span>
                    </div>
                </ng-template>
            </e-column>
            <e-column [headerText]="'ACCOUNT.GIRD.Action' | translate"
                width="100">
                <ng-template #template let-data>
                    <app-inno-table-action
                        (onDelete)="creaFormDelete(data)" />
                </ng-template>
            </e-column>
        </e-columns>
    </ejs-grid>

    <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
</div>
}
