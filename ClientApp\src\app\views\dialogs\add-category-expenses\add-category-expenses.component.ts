import { CategoryService } from './../../../service/category.service';
import { ToastService } from 'app/service/toast.service';
import { SpinnerService } from 'app/service/spinner.service';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';

@Component({
  selector: 'app-add-category-expenses',
  standalone: true,
  imports: [SharedModule, InnobookModalWrapperComponent],
  templateUrl: './add-category-expenses.component.html',
  styleUrl: './add-category-expenses.component.scss'
})
export class AddCategoryExpensesComponent implements OnInit {
  private _spinnerService = inject(SpinnerService)
  destroyRef = inject(DestroyRef);
  private _toastService = inject(ToastService)
  private _categoryService = inject(CategoryService)

  static getComponent(): typeof AddCategoryExpensesComponent {
    return AddCategoryExpensesComponent;
  }
  constructor(public dialogRef: MatDialogRef<AddCategoryExpensesComponent>, @Inject(MAT_DIALOG_DATA) public data: any
  ) {

  }
  public categoryId: string = '';
  public isAddSvcMode = false;
  listCategory: any[] = []
  listCategoryItem: any[] = []
  listItemCategoryRemove: string[] = []
  categoryName!: string;
  categoryNameItem!: string;
  ngOnInit(): void {
    this.GetAllCategoryAsync();
  }
  GetAllCategoryAsync() {
    let payload: Parameter = {
      Page: 1,
      PageSize: 40,
      Search: ""

    }
    this._categoryService.GetAllCategoryAsync(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.listCategory = res.data
        if (this.listCategory.length > 0) {
          this.listCategoryItem.push({
            itemName: this.data.item,
            categoryId: this.listCategory[0].id,
          })
          this.categoryId = this.listCategory[0].id;
        }
      }
    }
    )
  }
  handleSelectCategory($event: any) {
    this.categoryId = ($event.target as HTMLSelectElement).value.toString()
  }
  RemoveItem(_id: string, index: number) {
    this.listCategoryItem.splice(index, 1)
    this.listItemCategoryRemove.push(_id)

  }
  RemoveAll() {
    this.listCategoryItem = []
  }
  RemoveService(i: number) {
    this.listCategoryItem.splice(i, 1);
  }
  CreatedCategoryItem() {
    this.isAddSvcMode = false
    this.listCategoryItem.push({
      itemName: this.categoryNameItem,
      categoryId: this.categoryId,
    })
    this.categoryNameItem = ''
  }
  AddCategoryItem() {
    this.isAddSvcMode = true;
  }
  closeDialog() {
    this.dialogRef.close();
  }
  Edit() {
  }
  Save() {
    if (this.listCategoryItem.length > 0) {
      this.listCategoryItem.map(x => x.categoryId = this.categoryId)
      this._spinnerService.show();
      this._categoryService.CreateCategoryItem(this.listCategoryItem).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this._toastService.showSuccess("Save", "Success");
          this._spinnerService.hide();
          this.dialogRef.close(res);
        }
      })
    }

  }
}
