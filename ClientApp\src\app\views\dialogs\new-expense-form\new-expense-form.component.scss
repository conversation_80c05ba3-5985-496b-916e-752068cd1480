@import "./variables.scss";

p {
  margin-bottom: 0;
}

.selected {
  @apply bg-bg-brand-primary;
}

.selected .txtTitle {
  @apply text-text-sm-semibold;
}

.selected .txtTitle,
.selected .txtDescription {
  @apply text-text-brand-primary
}

p {
  margin-bottom: 0;
}

.expensesTableLayout {
  width: 100%;
  min-width: 70dvw;
  display: grid;
  grid-template-columns: minmax(200px, 1fr) 100px 100px 100px 200px 100px;
  grid-column-gap: 8px;
  padding-top: 8px;
  padding-bottom: 8px;
}