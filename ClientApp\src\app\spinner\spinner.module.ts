import {
  AfterContentInit,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  OnDestroy
} from '@angular/core';
import { Subscription } from 'rxjs';
import { SharedModule } from '../module/shared.module';
import { SpinnerService } from '../service/spinner.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-progress-spinner',
  templateUrl: 'progress-spinner.html',
  styleUrls: ['progress-spinner.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class ProgressSpinnerComponent implements AfterContentInit {
  public showSpinner = false;
  private spinnerService = inject(SpinnerService);
  private cdRef = inject(ChangeDetectorRef)
  destroyRef = inject(DestroyRef);
  constructor(
  ) { }


  ngAfterContentInit() {
    this.spinnerService.visibility.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((spin) => {
      this.showSpinner = spin;
      this.cdRef.detectChanges();
    })
  }
}
