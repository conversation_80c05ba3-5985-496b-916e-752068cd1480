import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { GetClientQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
import { Client } from '../dto/interface/client.interface';
import { ClientTimeTracking } from '../dto/interface/clientTimeTracking.interface';
import { CalculationResponse } from 'app/dto/interface/calculationResponse.interface';
import { formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api/Client/"
@Injectable({
  providedIn: 'root'
})
export class ClientService {

  private http = inject(HttpClient)
  constructor() { }

  CreateClient(payload: any): Observable<object> {
    return this.http.post(UrlApi + 'Create', payload);
  }
  UpdateClient(payload: any): Observable<Client> {
    return this.http.post<Client>(UrlApi + 'UpdateClient', payload);
  }
  GetAllClient(params: GetClientQueryParam): Observable<Client[]> {
    const query = formatParamsQuery(params);
    return this.http.get<Client[]>(UrlApi + `AllClient`, { params: query });
  }
  ClientTimeTracking(): Observable<ClientTimeTracking[]> {
    return this.http.get<ClientTimeTracking[]>(UrlApi + `ClientTimeTracking`);
  }
  CalculationClient(): Observable<CalculationResponse<Client>> {
    return this.http.get<CalculationResponse<Client>>(UrlApi + 'CalculationClient');
  }

  GetClientById(id: string): Observable<Client> {
    return this.http.get<Client>(UrlApi + `GetClientById?id=${id}`);
  }
  DeleteClient(payload: any): Observable<boolean> {
    return this.http.delete<boolean>(UrlApi + 'DeleteClient', { body: payload });
  }
}
