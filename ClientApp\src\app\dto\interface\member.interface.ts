import { Project } from "./project.interface";
import { TimeTracking } from "./timeTracking.interface";
import { User } from "./user.interface";

export interface Member {
  userId: string;
  projectId: string;
  companyId: string;
  roleId: string;
  status: number;
  leftAt?: any;
  user: User;
  project: Project;
  role?: any;
  company?: any;
  timeTrackings: TimeTracking[];
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy?: any;
}
