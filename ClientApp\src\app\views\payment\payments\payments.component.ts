import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { PaymentService } from './../../../service/payment.service';
import { SharedModule } from 'app/module/shared.module';
import { StoreService } from 'app/service/store.service';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { BreadcrumComponent } from 'app/component/breadcrum/breadcrum.component';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ActionEventArgs, GridAllModule, GridComponent, GridModule, PagerModule, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { AddPaymentDialog } from '../../../service/dialog/add-payment.dialog';

@Component({
  selector: 'app-payments',
  standalone: true,
  imports: [MatMenuModule, BreadcrumComponent, PagerModule, SharedModule, GridModule, GridAllModule, InnoSpinomponent],
  providers: [LayoutUtilsService],
  templateUrl: './payments.component.html',
  styleUrl: './payments.component.scss'
})
export class PaymentsComponent implements OnInit {
  public sort: SortGird
  public isLoading = false;
  public sortOptions: SortSettingsModel = { columns: [] };
  private _subscriptions: Subscription[] = [];
  public data?: object[];
  listChoosePayment: any[] = []
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  public columnSelection = false;
  search: string = ''
  dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  searchSubject = new Subject<string>();
  pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any

  destroyRef = inject(DestroyRef);
  router = inject(Router);
  private translate = inject(TranslateService);
  private layoutUtilsService = inject(LayoutUtilsService)
  private _paymentService = inject(PaymentService)
  protected activatedRoute = inject(ActivatedRoute);
  public _storeService = inject(StoreService)

  constructor(private addPaymentDialog: AddPaymentDialog) {

  }
  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        this.GetAllPaymentCompany(this.currentPage, "")
      }
      else {
        this.GetAllPaymentCompany(this.currentPage, "")
      }

    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      if (search) {
        this.search = search;
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.sort ? this.GetAllPaymentCompany(this.currentPage, this.search, filter) : this.GetAllPaymentCompany(this.currentPage, this.search)
      }
      else {
        this.search = "";
        this.GetAllPaymentCompany(this.currentPage, "")
      }
    });
    this._subscriptions.push(sb)
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllPaymentCompany(this.currentPage, "")
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }

  GetAllPaymentCompany(page: number, search: string, filter?: any) {
    this.isLoading = true;
    let payload: Parameter = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      Filter: filter

    }
    this._paymentService.GetAllPaymentCompany(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.isLoading = false;
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };
          }

        }
      }
    });
  }

  onRowSelecting(event: any): void {
    if (event?.data?.length > 0) {
      event?.data.forEach((element: any) => {
        let index = this.listChoosePayment.findIndex(x => x == element?.id)
        if (index < 0) {
          this.listChoosePayment.push(element?.id)
        }
      });

    } else {
      let index = this.listChoosePayment.findIndex(x => x == event?.data?.id)
      if (index < 0) {
        this.listChoosePayment.push(event?.data?.id)
      }
    }

  }
  onRowDeselecting(event: any): void {
    if (event?.data?.length > 0) {
      this.listChoosePayment = [];
    }
    else {

      let index = this.listChoosePayment.findIndex(x => x == event.data?.id)
      if (index >= 0) {
        this.listChoosePayment.splice(index, 1)
      }
    }

  }


  creaFormDelete() {
    const _title = this.translate.instant('Delete members !');
    const _description = this.translate.instant('Do you want to delete?');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      // this.userbusiness_services.DeleteMemberInBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      //   if (res) {
      //     this.GetAllMemberInBusiness(this.currentPage, "")
      //     this._toastService.showSuccess("Delete", "Success");
      //     this.listChooseMember = [];
      //   }
      // })
    })
  }


  AddPayment() {
    const dialogRef = this.addPaymentDialog.open({});

    dialogRef.then((c) => {
      c.afterClosed().subscribe((result) => {
        if (result) {
          this.GetAllPaymentCompany(this.currentPage, "")
        }
      });
    });
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.GetAllPaymentCompany(this.currentPage, this.search, filter)
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllPaymentCompany(this.currentPage, this.search)
      return;
    }

  }
  ngOnDestroy(): void {

    if (this._subscriptions) {
      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
