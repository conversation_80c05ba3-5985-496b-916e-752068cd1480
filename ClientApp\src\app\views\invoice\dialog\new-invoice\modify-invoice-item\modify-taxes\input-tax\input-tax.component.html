<div class="overflow-auto">
  <div class="layoutGrid">
    <div class="w-full h-full flex items-end pb-[15px]">
      <app-inno-form-checkbox
        [checked]="tax.selected"
        (onChange)="handleChangeTax('selected', $event)" />
    </div>
    <div class="w-full">
      <app-inno-form-input
        [label]="'TAX.TaxName' | translate"
        [placeholder]="'TAX.PlaceholderTaxName' | translate"
        (onChange)="handleChangeTax('name', $event)"
        [value]="tax.name" />
    </div>
    <div class="w-full">
      <app-inno-form-input
        [label]="'TAX.Rate' | translate"
        placeholder="0.00"
        type="number"
        [removeZeros]="true"
        (onChange)="handleChangeTax('amount', $event)"
        [value]="tax.amount" />
    </div>
    <div class="w-full">
      <app-inno-form-input
        [label]="'TAX.TaxNumber' | translate"
        type="number"
        [placeholder]="'TAX.PlaceholderTaxNumber' | translate"
        (onChange)="handleChangeTax('taxeNumber', $event)"
        [value]="tax.taxeNumber" />
    </div>

    @if(!isHideDeleteButton) {
    <div class="w-full flex h-full items-end pb-[5px]">
      <button class="button-icon" (click)="handleDelete()">
        <img src="../../../../../../../../assets/img/icon/ic_remove.svg"
          alt="Icon">
      </button>
    </div>
    }
  </div>
</div>
