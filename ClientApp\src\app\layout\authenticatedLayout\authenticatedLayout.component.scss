p {
  margin-bottom: 0;
}

.authenticatedLayout {
  width: 100vw;
  height: 100dvh;
  display: flex;
  overflow: hidden;
}

.authenticatedLayout .navigation {
  flex-shrink: 0;
  height: 100%;
  // overflow: auto;
  z-index: 2;
}

.authenticatedLayout .pageContent {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  @apply bg-bg-secondary;
}

.authenticatedLayout .pageContent .route {
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
// @media screen and (max-width: 860px) {
//   .authenticatedLayout {
//     flex-direction: column-reverse;
//   }

//   .authenticatedLayout .navigation {
//     height: auto;
//     position: relative;
//     z-index: 2;
//   }

//   // .authenticatedLayout .pageContent .route {
//   //   padding-bottom: 0;
//   // }
// }
