import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class NewExpenseFormDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/new-expense-form/new-expense-form.component'
    );

    return this.matDialog.open(
      importedModuleFile.NewExpenseFormComponent.getComponent(),
      {
        data,
        width: "calc(80% - 10px)",
        maxWidth: "100%",
        height: "auto",
        disableClose: true,
        panelClass: 'custom_dialog',
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
