import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddCategoryExpensesDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/add-category-expenses/add-category-expenses.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddCategoryExpensesComponent.getComponent(),
      {
        data,
        width: '550px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
