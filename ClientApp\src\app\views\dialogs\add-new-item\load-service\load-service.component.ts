import { SortGird } from './../../../../dto/interface/SortGird.interface';
import { StoreService } from 'app/service/store.service';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { ServiceService } from './../../../../service/service.service';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { SharedModule } from 'app/module/shared.module';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { Component, DestroyRef, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetServiceRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { Service } from 'app/dto/interface/service.interface';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { getNameTaxes } from 'app/utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-load-service',
  standalone: true,
  imports: [PagerModule, InnoModalWrapperComponent, InnoEmptyDataComponent, InnoFormInputComponent, InnoSelectSearchProjectComponent, InnoModalFooterComponent, InnoFormCheckboxComponent, SharedModule, FormatNumberPipe],
  templateUrl: './load-service.component.html',
  styleUrl: './load-service.component.scss'
})
export class LoadServiceComponent implements OnInit {
  @Output() cancel: EventEmitter<any> = new EventEmitter<any>();
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  public getNameSelectedTaxes = getNameTaxes
  public listService: Service[] = []
  public listIndexInvoiceSelected: number[] = []
  public projectId!: string
  public projectName!: string
  public currentPage: number = 1
  public totalPages = 1;
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  public sort: SortGird
  sortDirection: 'Ascending' | 'Descending' = 'Ascending';
  sortColumn: string = ''

  public _storeService = inject(StoreService)
  private serviceService = inject(ServiceService)
  private destroyRef = inject(DestroyRef)
  constructor() {

  }
  LoadAllService(args?: { page?: number, textSearch?: string, sort?: any }) {
    const payload: GetServiceRequestParam = {
      Page: args?.page ?? 1,
      Search: args?.textSearch ?? '',
      PageSize: this.pageSizesDefault,
      ...this.sort
    }
    this.serviceService.GetAllService(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.totalPages = res.totalRecords
        this.listService = res.data.map(item => ({ ...item, dateSelectItem: item.createdAt, projectName: item.project?.projectName, isServices: true }));
      }
    });
  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexInvoiceSelected = this.listService.map((_item, index) => index)
    } else {
      this.listIndexInvoiceSelected = []
    }
  }
  handleQtyIndex($event, index: number) {
    this.listService[index]["qty"] = $event
  }

  ngOnInit(): void {
    this.sortDirection = "Ascending"
    this.sortColumn = 'createdAt'
    this.sort = {
      columnName: this.sortColumn,
      direction: this.sortDirection
    }
    this.handleSort(this.sortDirection)
  }
  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected
  }


  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      const page = this.currentPage
      const textSearch = ""
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllService({ page, textSearch });
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      const page = this.currentPage
      const textSearch = ""
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllService({ page, textSearch });
    }
  }
  sortDates(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'Ascending' ? 'Descending' : 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    } else {
      this.sortColumn = column;
      this.sortDirection = 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    }
  }
  handleSort(sortDirection: string) {
    const sort = JSON.stringify(this.sort)
    const page = this.currentPage
    const textSearch = ""
    switch (sortDirection) {
      case 'Descending':
        this.LoadAllService({ page, textSearch, sort });
        break;
      case 'Ascending':
        this.LoadAllService({ page, textSearch, sort });
        break;
      default:
        break;
    }
  }
  handleCancel() {
    this.cancel.emit()
  }

  handleSubmit() {
    const listInvoiceItemSelected = this.listService.filter((_item, index) => this.listIndexInvoiceSelected.includes(index))
    this.submit.emit(listInvoiceItemSelected.map(({ project, ...rest }) => rest))
  }
}
