import { StoreService } from 'app/service/store.service';
import { SortGird } from './../../../dto/interface/SortGird.interface';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { InvoiceService } from 'app/service/invoice.service';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ToastService } from 'app/service/toast.service';
import { Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { ActionEventArgs, GridAllModule, GridComponent, Pager, PagerDropDown, PagerModule, RowDragEventArgs, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { GetEstimateQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { skip, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { AvatarModule } from 'ngx-avatars';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
Pager.Inject(PagerDropDown);

@Component({
  selector: 'app-estimate-management',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    InnoStatusComponent,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InnoPopoverComponent,
    InnoEmptyDataComponent,
    InnoTableActionComponent,
    RouterModule,
    AvatarModule,
    GridAllModule,
    PagerModule,
    InnoSpinomponent,
    FormatNumberPipe,
    DecimalPipe
  ],
  templateUrl: './estimate-management.component.html',
  styleUrl: './estimate-management.component.scss'
})
export class EstimateManagementComponent implements OnInit, OnDestroy {
  public sort: SortGird
  public isLoading = false;
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  public columnName: string
  public direction: any
  @ViewChild('grid') grid?: GridComponent;
  public sortOptions: SortSettingsModel = { columns: [] };

  public _storeService = inject(StoreService)
  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  private _invoiceService = inject(InvoiceService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private dataService = inject(DataService)
  private _subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      this.currentPage = queryParams?.page ?? 1;
      this.GetAllEstimate('');
    });

    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().pipe(skip(1)).subscribe((data) => {
        if (data?.typeView !== InvoiceViewEnum.Created_Tab) return;
        this.currentPage = this.activatedRoute.snapshot.queryParams['page'] || 1;
        const textSearch = data?.textSearch ?? ''
        this.GetAllEstimate(textSearch);
      }
      )
    )
  }


  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetAllEstimate('');
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null;
      this.GetAllEstimate('');
    }
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize;
      this.GetAllEstimate('');
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }
  }

  GetAllEstimate(search: string) {
    const param: GetEstimateQueryParam = {
      Page: this.currentPage ?? 1,
      Search: search ?? '',
      PageSize: this.pageSizesDefault,
      ...this.sort,
      isSendToMe: false
    }
    this.isLoading = true
    this._invoiceService.GetAllEstimate(param).subscribe({
      next: (res) => {
        if (res) {
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };

          }
        }
      },
      complete: () => {
        this.isLoading = false
      }
    })
  }

  handleEdit(item: any) {
    this.router.navigate(['/estimates', item.id])
  }

  handleDelete(item) {
    const _title = this.translate.instant('ESTIMATE.DeleteEstimate');
    const _description = this.translate.instant('COMMON.ConfirmDelete');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([item.id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.GetAllEstimate('');
          this._toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
        }
        else {
          this._toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
    })
  }
  onDragStop(args: RowDragEventArgs): void {
    let payload = {
      fromInvoiceId: this.dataSource[args.fromIndex].id,
      dropInvoiceId: this.dataSource[args.dropIndex].id,
      fromIndex: this.dataSource[args.fromIndex].position,
      dropIndex: this.dataSource[args.dropIndex].position
    }
    this._invoiceService.ChangePosition(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
  }

  ngOnDestroy(): void {

    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
