 <!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary">
    <div
        class="container-full flex justify-between items-center flex-wrap gap-2">
        <p class="text-text-primary text-headline-lg-bold">
            {{'SETTINGS.Title'| translate}}
        </p>
    </div>
</div>
<!-- End Header page -->

<div class="container-full mt-[24px]">
    <div [routerLink]="['/settings/business']"
        class="mb-6 rounded-md  w-full border border-gray-300 cursor-pointer hover:bg-gray-200 ">
        <div class="p-3 flex justify-between  items-center">
            <span class="material-icons mr-2">
                business
            </span>
            <div class="flex flex-col w-full">
                <span> {{'SETTINGS.BasicInformation.Title'| translate}}</span>
                <span
                    class="text-gray-400">{{'SETTINGS.BasicInformation.Description'|
                    translate}}</span>
            </div>
            <span class="material-icons">
                chevron_right
            </span>
        </div>

    </div>

    <div [routerLink]="['/settings/permission']"
        class="rounded-md mb-6 w-full border border-gray-300 cursor-pointer hover:bg-gray-200 ">
        <div class="p-3 flex justify-between  items-center">
            <span class="material-icons mr-2">
                manage_accounts
            </span>
            <div class="flex flex-col w-full">
                <span>{{'SETTINGS.PermissionInformation.Title'|
                    translate}}</span>
                <span
                    class="text-gray-400">{{'SETTINGS.PermissionInformation.Description'|
                    translate}}</span>
            </div>
            <span class="material-icons">
                chevron_right
            </span>
        </div>

    </div>
    @if(role!=Role.Employee&& role!=Role.Accountant &&
    role!=Role.Contractor&&
    role!=Role.Manager)
    {
    <div [routerLink]="['/settings/account']"
        class="rounded-md mb-6 w-full border border-gray-300 cursor-pointer hover:bg-gray-200 ">
        <div class="p-3 flex justify-between  items-center">
            <span class="material-icons mr-2">
                account_circle
            </span>
            <div class="flex flex-col w-full">
                <span>{{'SETTINGS.AccountInformation.Title'| translate}}</span>
                <span
                    class="text-gray-400">{{'SETTINGS.AccountInformation.Description'|
                    translate}}</span>
            </div>
            <span class="material-icons">
                chevron_right
            </span>
        </div>

    </div>
    }

    <div [routerLink]="['/settings/category']"
        class="rounded-md mb-6 w-full border border-gray-300 cursor-pointer hover:bg-gray-200 ">
        <div class="p-3 flex justify-between  items-center">
            <span class="material-icons mr-2">
                category
            </span>
            <div class="flex flex-col w-full">
                <span>{{'SETTINGS.CategoryInformation.Title'|
                    translate}}</span>
                <span
                    class="text-gray-400">{{'SETTINGS.CategoryInformation.Description'|
                    translate}}</span>
            </div>
            <span class="material-icons">
                chevron_right
            </span>
        </div>

    </div>

    <div [routerLink]="['/settings/financial']"
        class="rounded-md mb-6 w-full border border-gray-300 cursor-pointer hover:bg-gray-200 ">
        <div class="p-3 flex justify-between  items-center">
            <span class="material-icons mr-2">
                paid
            </span>
            <div class="flex flex-col w-full">
                <span>{{'SETTINGS.TaxAndFinancialInformation.Title'|
                    translate}}</span>
                <span
                    class="text-gray-400">{{'SETTINGS.TaxAndFinancialInformation.Description'|
                    translate}}</span>
            </div>
            <span class="material-icons">
                chevron_right
            </span>
        </div>

    </div>
</div>
