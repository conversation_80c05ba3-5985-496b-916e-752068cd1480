<div class="w-full bg-bg-primary">
  <div class="px-[8px] py-[16px] border-b border-border-primary-slight">
    <p class="text-headline-sm-bold text-text-primary">
      Add entry on {{ labelDate }}
    </p>
  </div>
  <div class="w-full p-[8px] flex flex-col gap-[8px]">
    <div class="w-full">
      <app-inno-select-search-project
        [templateTrigger]="templateTriggerSelectProject"
        (onSelect)="handleSelectProject($event)"
        [isOnlySelectProject]="true"
        [value]="previewWorkingInfo?.value" />
      <ng-template #templateTriggerSelectProject>
        <button
          class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-full">
          <span
            class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
            [ngClass]="{'text-text-primary': previewWorkingInfo!! }">
            {{previewWorkingInfo?.metadata?.objectClient?.clientName?previewWorkingInfo?.metadata?.objectClient?.clientName+"-":""}}
            {{ previewWorkingInfo?.label ?? 'What are you working on?' }}
          </span>
        </button>
      </ng-template>
      @if(previewWorkingInfo)
      {
      <app-inno-select-search-service
        [templateTrigger]="templateTriggerSelectService"
        (onSelect)="handleSelectServices($event)"
        [lable]="previewWorkingInfo.label"
        [value]="projectId" />

      <ng-template #templateTriggerSelectService>
        <button
          class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-ful mt-2"
          [ngClass]="{'text-text-primary': previewServiceInfo!! }">
          <span
            class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
            [ngClass]="{'text-text-primary': previewWorkingInfo!! }">

            {{ previewServiceInfo?.label ?? 'Choose Service' }}
          </span>
        </button>
      </ng-template>
      }
    </div>

    <app-inno-form-textarea
      placeholder="Note"
      [value]="previewDescription"
      (onChange)="handleChangeNote($event)" />

    <app-inno-enter-hours
      [value]="previewTimeEnd"
      (onChange)="handleChangeEndTime($event)" />

    <div class="flex items-center gap-[12px]">
      <app-inno-select-search-tags
        [templateTrigger]="templateTriggerSelectTags" />
      <ng-template #templateTriggerSelectTags>
        <app-inno-tags target value="Only InnoBook" />
      </ng-template>
      <app-inno-form-checkbox
        [checked]="previewBillable"
        (onChange)="handleChangeBillable($event)">
        Billable
      </app-inno-form-checkbox>
    </div>
  </div>
  <div
    class="w-full p-[16px] flex items-center justify-end border-t border-border-primary-slight gap-[12px]">
    <button class="button-outline button-size-md"
      (click)="handleCancel()">
      Cancel
    </button>
    <button
      class="button-primary button-size-md"
      [disabled]="!previewWorkingInfo || !previewDate || !previewTimeEnd || previewTimeEnd == '00:00'"
      (click)="handleAddTimeTrackingRecord()">
      Add entry
    </button>
  </div>
</div>
