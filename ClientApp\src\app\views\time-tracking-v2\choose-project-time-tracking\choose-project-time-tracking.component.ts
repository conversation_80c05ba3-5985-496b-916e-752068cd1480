import { TimetrackingService } from '../../../service/timetracking.service';
import { ProjectService } from '../../../service/project.service';
import { StoreService } from 'app/service/store.service';
import { AfterViewInit, Component, DestroyRef, EventEmitter, inject, Input, OnDestroy, Output, QueryList, SimpleChanges, ViewChild, viewChild, ViewChildren } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ReplaySubject } from 'rxjs';
import { ClientAndProjectQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SharedModule } from 'app/module/shared.module';
import { MatAccordion, MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
@Component({
  selector: 'app-choose-project-time-tracking',
  standalone: true,
  imports: [SharedModule, MatExpansionModule, MatFormFieldModule],
  templateUrl: './choose-project-time-tracking.component.html',
  styleUrl: './choose-project-time-tracking.component.scss'
})
export class ChooseProjectTimeTrackingComponent implements OnDestroy {
  @ViewChildren(MatExpansionPanel) panels!: QueryList<MatExpansionPanel>;
  @Input() isCloseMenu!: boolean;
  @Input() searchtxt!: string;
  // Public properties
  @Input() options: any = {
    showSearch: true,//hiển thị search input hoặc truyền keyword
    keyword: '',
    data: []
  };
  @ViewChild('myaccordion') myPanels!: MatAccordion;

  @ViewChild('myaccordion2') myPanels2!: MatAccordion;
  isPanelOpen = false;
  @Output() ItemSelected = new EventEmitter<any>();
  @Output() ItemSelectedClient = new EventEmitter<any>();
  @Output() ItemSelectedService = new EventEmitter<any>();
  @Output() IsSearch = new EventEmitter<any>();
  listProject: any[] = [];
  empty: boolean = false;
  customStyle: any = [];
  public filteredProject: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public clientFilterCtrl: FormControl = new FormControl();
  destroyRef = inject(DestroyRef);
  public _storeService = inject(StoreService)
  private _timeTrackingService = inject(TimetrackingService)
  constructor(
  ) { }
  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchtxt'] && changes['searchtxt'].currentValue) {
      this.filterClient(changes['searchtxt'].currentValue);
    }
    else {
      this.filterClient("");
    }

    if (changes['isCloseMenu'] && changes['isCloseMenu'].currentValue) {
      if (changes['isCloseMenu'].currentValue == true) {
        setTimeout(() => {
          this.closeAll();
        }, 200);

      }
    }

  }
  openAll() {
    if (this.myPanels) {
      this.myPanels.openAll();

    }
  }
  closeAll() {
    if (this.myPanels) {
      this.myPanels.closeAll();

    }
    if (this.myPanels2) {
      this.myPanels2.closeAll();

    }

  }
  onPanelOpened(event: any) {
    this.ItemSelectedClient.emit(event);

  }
  onPanelOpenedService(event: any) {
    let item = {
      data: event,
      isClose: false
    }
    this.ItemSelected.emit(item);

    this.isPanelOpen = true;
  }

  // Hàm được gọi khi panel đóng
  onPanelClosed(event: any) {
  }
  /**
   * On init
   */
  ngOnInit() {
    this.GetAllClient();
    this.isPanelOpen = false;

  }
  GetAllClient() {
    let params: ClientAndProjectQueryParam = {
      Page: 0,
      PageSize: 100,
      Search: "",

    }
  //  if (this._storeService.getIdCompany()) {
    this._timeTrackingService.GetProjectAndClientService(params).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.listProject = res.data
          this.filterClient("");
        }
      }
    });
  //  }

  }

  protected filterClient(search: string) {
    if (!this.listProject) {
      return;
    }

    if (!search || search == "") {
      this.filteredProject.next(this.listProject.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    if (search[0] == '@') {
      this.filteredProject.next(
        this.listProject.filter(bank => (bank.clientName.toLowerCase()).indexOf(search.replace('@', '')) > -1)
      );
    }
    else {
      let result = this.listProject.filter(bank => bank.clientName.toLowerCase().indexOf(search) > -1)
      this.empty = result.length == 0 ? true : false;
      this.filteredProject.next(
        result
      );


    }
  }

  select(pro: any) {
    this.closeAll();
    let item = {
      data: pro,
      isClose: true
    }
    this.ItemSelected.emit(item);

  }
  // selectClient(item: any) {
  //   this.ItemSelectedClient.emit(item);
  // }
  stopPropagation(event: any) {
    this.IsSearch.emit(event)
  }
  SelectedService(sr: any) {
    setTimeout(() => {
      this.closeAll()
    }, 200);
    this.ItemSelectedService.emit(sr);
  }
  ngOnDestroy(): void {
    this.closeAll();
  }
}
