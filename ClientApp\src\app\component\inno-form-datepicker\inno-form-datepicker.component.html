<div class="w-full flex flex-col relative" [class.error]="hasError()">
  @if(label) {
  <label [class.required]="isRequired"
    class="text-text-secondary text-text-sm-semibold mb-[2px]">{{ label
    }}</label>
  }

  <ejs-datepicker
    class="customDatePickerV2"
    name="{{ name || '' }}"
    id="{{ id || '' }}"
    [format]='format'
    [enableMask]="enableMask"
    [placeholder]="placeholder"
    [value]="value"
    [start]='start'
    [depth]='depth'
    [showTodayButton]='showTodayButton'
    [weekNumber]="weekNumber"
    (blur)='onBlur($event)'
    (change)="handleChangeValue($event)" />

  <app-inno-error-message [message]="getErrorMessage()" />
  @if(invalidDate)
  {
  <app-inno-error-message message="Invalid Date" />

  }
</div>
