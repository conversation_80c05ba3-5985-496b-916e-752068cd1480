import { HttpE<PERSON>, HttpHandlerFn, HttpInterceptorFn, HttpRequest } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, concatMap, delay, filter, retryWhen, switchMap, take } from 'rxjs/operators';
import { inject } from '@angular/core';
import { AuthenticationService } from '../auth/service/authentication.service';
import { environment } from '../../environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { SpinnerService } from '../service/spinner.service';
import { ToastService } from '../service/toast.service';
import * as Sentry from "@sentry/angular";
import { Router } from '@angular/router';
import { StoreService } from 'app/service/store.service';

export const retryCount = 1;
export const retryWaitMilliSeconds = 1000;
let isRefreshing = false;
const refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

export const ErrorInterceptor: HttpInterceptorFn = (request, next) => {
  const authenticationService = inject(AuthenticationService);
  const storeService = inject(StoreService);
  const toastService = inject(ToastService);
  const router = inject(Router);
  const translateService = inject(TranslateService);
  const spinnerService = inject(SpinnerService);
  return next(request)
    .pipe(
      retryWhen((error) =>
        error.pipe(
          concatMap((error, count) => {
            if (
              count <= retryCount &&
              (error.status === 0 || (error.status === 400 && !error.error))
            ) {
              return of(error);
            }
            return throwError(() => error);
          }),
          delay(retryWaitMilliSeconds)
        )
      )
    )
    .pipe(
      catchError(err => {
        if (err.status === 401) {
          return handle401Error(request, next, authenticationService, storeService, router);
        } else if (err.status !== 0) {
          Sentry.setContext("extra", {
            responseType: request.responseType,
            body: JSON.stringify(request.body),
            status: err.status,
            url: request.url,
            urlWithParams: request.urlWithParams
          });
          Sentry.captureException(err);
        }

        const error = JSON.stringify(err);
        spinnerService.hide();
        let erreur = err?.error?.error ? err?.error?.error : err?.error;
        if (erreur instanceof Blob) {
          const reader = new FileReader();
          reader.onload = () => {
            erreur = reader.result;
            if (!environment.production) {
              toastService.showError('Error', "An error has occurred on the server. The technical team has been notified");
            } else
              toastService.showError('Error', "An error has occurred on the server. The technical team has been notified");
          };
          reader.readAsText(erreur);
        } else {
          if (err.status === 504) {
            toastService.showError(
              translateService.instant('Timeout Error'),
              ''
            );
          } else if (environment.production && err.status === 500) {
            toastService.showError('Error', "An error has occurred on the server. The technical team has been notified");
          } else if (environment.production && err.status === 400) {
            toastService.showError('Error', "An error has occurred on the server. The technical team has been notified");
          } else if (environment.production && err.status === 408) {
            toastService.showError(
              translateService.instant('Error'),
              translateService.instant('Error.Timeout.408')
            );
          } else if (err.status === 403) {
            toastService.showError('Error', "You don't have permission to access API: " + request.url);
          } else if (
            environment.production &&
            err.status !== 0 &&
            err.status !== 401
          ) {
            toastService.showError(translateService.instant('Error'), erreur);
          } else if (!environment.production) {
            toastService.showError('Error', error);
          }
        }
        return throwError(() => error);
      })
    );
};

function handle401Error(
  request: HttpRequest<any>,
  next: HttpHandlerFn,
  authService: AuthenticationService,
  storeService: StoreService,
  router: Router
): Observable<any> {
  if (isRefreshing === false) {
    isRefreshing = true;
    refreshTokenSubject.next(null);

    // Tente de rafraîchir le token
    return authService.refreshAccessToken({ refreshToken: authService.getRefreshToken() }).pipe(
      switchMap((response: any) => {
        isRefreshing = false;

        // Enregistrer les nouveaux tokens
        authService.saveToken_cookie(response);

        // Émettre le nouveau accessToken
        refreshTokenSubject.next(response);

        // Rejouer la requête initiale avec le nouveau token
        return next(addToken(request, response));
      }),
      catchError((error) => {
        isRefreshing = false;

        // Si la tentative de refresh échoue avec un 401
        if (error.status === 401) {
          // Déconnexion de l'utilisateur
          authService.logout();

          // Redirection vers la page de login
          router.navigate(['/login'], { queryParams: { sessionExpired: true } });

          return throwError(() => new Error('Session expired. Please log in again.'));
        }

        return throwError(() => error);
      })
    );
  } else {
    // Si une requête est déjà en cours de rafraîchissement
    return refreshTokenSubject.pipe(
      filter(token => token != null), // Attend que le token soit mis à jour
      take(1),
      switchMap(token => next(addToken(request, token!)))
    );
  }
}

function addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
  return request.clone({
    setHeaders: {
      Authorization: `Bearer ${token}`
    }
  });
}
