import { DataService } from './../../service/data.service';
import { AuthenticationService } from './../../auth/service/authentication.service';
import { Component, DestroyRef, OnInit, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NavigationComponent } from '../../component/navigation/navigation.component';
import { SharedModule } from '../../module/shared.module';
import { InnoTimerComponent } from 'app/component/inno-timer/inno-timer.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CreateNewTimerInfo } from '../../dto/interface/createNewTimerInfo.interface';
import { StoreService } from 'app/service/store.service';

@Component({
  selector: 'app-authenticated-layout',
  templateUrl: './authenticatedLayout.component.html',
  styleUrls: ['./authenticatedLayout.component.scss'],
  standalone: true,
  imports: [
    RouterModule,
    SharedModule,
    NavigationComponent,
    InnoTimerComponent
  ]
})
export class AuthenticatedLayoutComponent implements OnInit {
  destroyRef = inject(DestroyRef);
  private dataService = inject(DataService)
  private storeService = inject(StoreService)
  private authenticationService = inject(AuthenticationService)
  ngOnInit(): void {
    this.authenticationService.CheckTimer().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res && res.isRunning == true) {
        localStorage.setItem("isRunning", res.isRunning)
        this.dataService.SetNewTimeTrackingShowingTimer(true)
        this.updateTimeTrackingTimerInfo({ timerStatus: 'running', billable: true })
      }
    });

    this.authenticationService.GetUser().subscribe((res: any) => {
      this.storeService.set_InforUser(res)
    })


  }



  private updateTimeTrackingTimerInfo(data: Partial<CreateNewTimerInfo>) {
    const currentValue = this.dataService.GetTimeTrackingCreateTimerInfoValue()
    const newValue = { ...currentValue, ...data }
    this.dataService.SetNewTimeTrackingCreateTimerInfo(newValue)
  }
}
