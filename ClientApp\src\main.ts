import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { registerLicense } from '@syncfusion/ej2-base';
import { APP_INITIALIZER, enableProdMode, ErrorHandler, importProvidersFrom, LOCALE_ID } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { Router, RouterModule } from '@angular/router';
import { routes } from './app/app-routing.module';
import { HttpClient, provideHttpClient, withInterceptors } from '@angular/common/http';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { JwtInterceptor } from './app/helpers/jwt.interceptor';
import { ErrorInterceptor } from './app/helpers/error.interceptor';
import { Sentry<PERSON><PERSON>r<PERSON><PERSON><PERSON>, TraceService } from '@sentry/angular';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import * as Sentry from "@sentry/angular";
import { environment } from './environments/environment';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import { SocialAuthServiceConfig } from '@abacritt/angularx-social-login';
import { GoogleLoginProvider } from '@abacritt/angularx-social-login';

registerLocaleData(localeFr);

registerLicense(
  'ORg4AjUWIQA/Gnt2UlhhQlVMfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTX9Td0BhWHpbdHNST2Bc'
);

export function getBaseUrl() {
  return document.getElementsByTagName('base')[0].href;
}

if (environment.production) {
  enableProdMode();
}

bootstrapApplication(AppComponent, {
  providers: [
    importProvidersFrom(
      RouterModule.forRoot(routes, {
        scrollPositionRestoration: 'enabled',
        onSameUrlNavigation: 'reload',
        enableTracing: false//environment.tracing
      })
    ),
    { provide: 'BASE_URL', useFactory: getBaseUrl, deps: [] },
    // CookieService,
    provideHttpClient(withInterceptors([JwtInterceptor, ErrorInterceptor])),
    { provide: ErrorHandler, useClass: SentryErrorHandler },
    {
      provide: TraceService,
      deps: [Router]
    },
    {
      provide: 'SocialAuthServiceConfig',
      useValue: {
        autoLogin: false,
        providers: [
          {
            id: GoogleLoginProvider.PROVIDER_ID,
            provider: new GoogleLoginProvider(
              '164306765554-454lu9vfk0tr9n0723m9ucmut56u1af0.apps.googleusercontent.com',
              { oneTapEnabled: false }
            )
          }
        ],
        onError: (err) => console.error(err),
      } as SocialAuthServiceConfig,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: () => () => { },
      deps: [Sentry.TraceService],
      multi: true
    },
    { provide: LOCALE_ID, useValue: 'en' },
    importProvidersFrom(BrowserAnimationsModule),
    importProvidersFrom(
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      })
    ), provideAnimationsAsync()
  ]
}).catch((err) => console.log(err));

export function HttpLoaderFactory(http: HttpClient) {
  const version = new Date().getTime(); // Timestamp unique
  return new TranslateHttpLoader(http, '/assets/i18n/', `.json?v=${version}`);
}
