import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Role } from 'app/enum/role.enum';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [RouterModule, TranslateModule],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent implements OnInit {
  public Role = Role
  public role!: string;

  private storeService = inject(StoreService)
  private destroyRef = inject(DestroyRef)
  ngOnInit(): void {
    this.storeService.getRoleBusinessAsObservable().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      this.role = res;

    })
  }

}
