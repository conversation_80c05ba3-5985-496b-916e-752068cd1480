export interface IExpensesSelect {
    id: string;
    expensesName: string;
    note: string;
    paidAmount: number;
    total?: number;
    img?: any;
    isArchive: boolean;
    isActive: boolean;
    categoryName: string;
    projectName: string;
    clientName: string;
    itemName: string;
    createdBy: string;
    date: string;
    inforUser: InforUser;
}
interface InforUser {
    firstName: string;
    lastName: string;
    email: string;
}
