import { NewInvoiceDialog } from './../../../../service/dialog/new-invoice.dialog';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { StoreService } from './../../../../service/store.service';
import { ToastService } from './../../../../service/toast.service';
import { InnoFormInputComponent } from './../../../../component/inno-form-input/inno-form-input.component';
import { InnoTableActionComponent } from './../../../../component/inno-table-action/inno-table-action.component';
import { InnoFormCheckboxComponent } from './../../../../component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoEmptyDataComponent } from './../../../../component/inno-empty-data/inno-empty-data.component';
import { ExpensesService } from './../../../../service/expenses.service';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, EventEmitter, inject, Input, input, OnInit, Output, SimpleChanges } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { Expenses } from 'app/dto/interface/expenses.interface';
import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { GetExpenseQueryParam } from 'app/dto/interface/queryParameter.interface';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-genrate-invoice-expenses',
  standalone: true,
  imports: [SharedModule,
    PagerModule,
    FormatNumberPipe,
    DecimalPipe,
    InnoEmptyDataComponent,
    InnoFormCheckboxComponent,
    InnoTableActionComponent,
    InnoFormInputComponent,
    InnoModalFooterComponent
  ],
  templateUrl: './genrate-invoice-expenses.component.html',
  styleUrl: './genrate-invoice-expenses.component.scss'
})
export class GenrateInvoiceExpensesComponent {
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  @Input() clientId: string
  @Input() valueRadio: number
  @Input() listProjectId: any
  @Output() cancel = new EventEmitter<boolean>(false)
  public listExpensesItem: Expenses[] = []
  public listIndexExpensesSelected: number[] = []

  public _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private destroyRef = inject(DestroyRef)
  private expenseService = inject(ExpensesService)
  private newInvoiceDialog = inject(NewInvoiceDialog)
  constructor() {

  }
  ngOnChanges(changes: SimpleChanges) {
    const valueRadio = changes?.['valueRadio']?.currentValue ?? null
    if (valueRadio) {
      this._GetData(valueRadio)
    }
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
    }
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexExpensesSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexExpensesSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexExpensesSelected = newListSelected

  }
  get totalAmount() {
    return this.listExpensesItem.reduce((total, item, index) => {
      if (!this.listIndexExpensesSelected.includes(index)) return total;
      return total += (item?.paidAmount ?? 0)
    }, 0)
  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexExpensesSelected = this.listExpensesItem.map((_item, index) => index)
    } else {
      this.listIndexExpensesSelected = []
    }
  }
  _GetData(value: number) {
    const query: GetExpenseQueryParam = {
      Page: 1,
      PageSize: 20,
      Search: "",
    };
    if (value != 2) {
      query.clientId = this.clientId;
    }
    if (value == 2) {
      query.projectIds = this.listProjectId;
    }

    this.expenseService.GetAllExpenses(query).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.listExpensesItem = res.data;
          this.totalPages = res.totalRecords
        }
      }
    });
  }
  handleCancel() {
    this.cancel.emit(true)
  }

  handleSubmit() {
    this.cancel.emit(true)
    const listItemInvoice = this.listExpensesItem.filter((_item, index) => this.listIndexExpensesSelected.includes(index))
    const payload: Record<string, any> = {
      clientId: this.clientId,
      isGenrate: true,
      itemInvoices: listItemInvoice.map((item: any) => {
        return {
          ExpensesId: item?.id ?? '',
          date: item?.date ?? null,
          description: `(Expenses) ${item?.expensesName}${`\n`} ${item?.categoryName} ${item?.itemName}`,
          rate: item?.project?.hourlyRate,
          qty: 0,
          taxes: [],
          total: item.paidAmount,
          metadata: {
            hours: item?.endTime ?? '00:00:00',
            timeTracking: item
          },
        }
      })
    }

    payload['invoiceDate'] = new Date()
    payload['dueDate'] = new Date()
    const listDate = this.listExpensesItem.map((item: any) => new Date(item.date))
    const startDate = listDate.reduce((total, item) => item < total ? item : total, new Date())
    const endDate = listDate.reduce((total, item) => item > total ? item : total, new Date())
    payload['invoiceDate'] = startDate
    payload['dueDate'] = endDate
    this.newInvoiceDialog.open(payload);
  }
}
