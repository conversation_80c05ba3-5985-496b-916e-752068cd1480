<app-inno-modal-wrapper title="Send Estimate" (onClose)="handleClose()">
    <div class="w-full p-[16px]">
        <div class="w-full flex flex-col relative">
            <label class="text-text-secondary text-text-sm-semibold mb-[2px]">To
                Email</label>
            @if(listEmailSelected.length>0)
            {

            <div
                class="w-full min-h-[46px] cursor-pointer rounded-md border-2 border-border-primary py-[6px] px-[4px] flex justify-between gap-[4px]">
                <div class="grow flex flex-wrap gap-[4px]">
                    @for(item of listEmailSelected; track item; let i =
                    $index) {
                    <div
                        class="rounded-[100px] text-text-secondary text-text-sm-medium border border-border-primary bg-bg-secondary flex gap-[4px] items-center py-[4px] px-[8px]">
                        {{ item }}
                        <div (click)="handleRemoveEmailClient($event, i)"
                            class="w-[16px] shrink-0 h-[16px] hover:bg-bg-danger-secondary flex justify-center items-center rounded-xs">
                            <img class="w-[16px]"
                                src="../../../../../../assets/img/icon/ic_remove.svg"
                                alt="Icon">
                        </div>
                    </div>
                    }

                    <!-- <span
                class="pl-[4px] pt-[3px] text-text-placeholder text-text-md-regular">
                Select or add a new mail
              </span> -->
                </div>
                <!-- <img class="shrink-0 w-[16px]"
              src="../../../../../../assets/img/icon/ic_arrow_down_gray.svg"
              alt="Icon"> -->
            </div>
            }

            <form class="mt-3" [formGroup]="sendInvoiceForm"
                (ngSubmit)="onSubmit()">
                <div class="mb-6">

                    <input type="email" id="_email"
                        formControlName="email"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Email" required />
                    @if (
                    (f['email'].dirty || f['email'].touched) &&
                    f['email'].hasError('required')
                    ) {
                    <mat-error class="matError">Email is required</mat-error>
                    }
                    @if (
                    (f['email'].dirty || f['email'].touched) &&
                    f['email'].hasError('email')
                    ) {
                    <mat-error class="matError">Invalid email</mat-error>
                    }

                </div>
            </form>
            <!-- <app-inno-select-search-client
          [templateTrigger]="templateTriggerSelectClient"
          (onSelect)="handleSelectClient($event)"
          [value]="listSelectedIdClient.toString()" /> -->

        </div>

        <div class="bg-bg-brand-primary rounded-md p-[16px] mt-[16px]">
            <p class="text-text-primary text-text-md-semibold mb-[4px]">
                {{ businessInfo.businessName }} sent you an estimate ({{
                invoice?.['invoiceNumber'] }})
            </p>
            <p class="text-text-secondary text-text-sm-regular">
                {{ businessInfo.businessName }} has sent you an estimate ({{
                invoice?.['invoiceNumber'] }}) with an amount of ${{
                invoice?.['totalAmount'] }}, due on {{ invoice?.['dueDate'] |
                date:'MMM d, yyyy' }}.
                If you have any questions or require further details, please
                feel free
                to reach out.
                <br />
                Thank you!
            </p>
        </div>

        <button class="button-link-primary mt-[16px]" (click)="handleShare()">
            <img src="../../../../../../assets/img/icon/ic_link.svg" alt="Icon">
            Share by the link
        </button>
    </div>
    <app-inno-modal-footer
        textCancel="Back to Estimate"
        textSubmit="Send Email"
        (onCancel)="handleCancel()"
        (onSubmit)="handleSave()" />
</app-inno-modal-wrapper>
