  /* eslint-disable @angular-eslint/no-output-on-prefix */
import { Component, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { SharedModule } from '../../module/shared.module';
  @Component({
    selector: 'app-inno-modal-footer',
    templateUrl: './inno-modal-footer.component.html',
    styleUrls: ['./inno-modal-footer.component.scss'],
    standalone: true,
    imports: [SharedModule]
  })

  export class InnoModalFooterComponent {

    @Input() public classNameSubmitButton?: string = ''
    @Input() public classNameCancelButton?: string = ''
    @Input() public leftAction?: TemplateRef<any>;
    @Input() public customSubmitButton?: TemplateRef<any>;
    @Input() public customCancelButton?: TemplateRef<any>;
    @Input() public textSubmit?: string;
    @Input() public textCancel?: string;
    @Input() public idSubmit?: string = '';
    @Input() public idCancel?: string = '';
    @Input() public isDisableSubmit?: boolean = false

    @Output() public onSubmit = new EventEmitter<void>();
    @Output() public onCancel = new EventEmitter<void>();

    constructor() {}

    handleSubmit() {
      this.onSubmit.emit();
    }

    handleCancel() {
      this.onCancel.emit();
    }
  }
