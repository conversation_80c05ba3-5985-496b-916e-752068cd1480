import { SharedModule } from 'app/module/shared.module';
import { StoreService } from 'app/service/store.service';
import { ClientService } from './../../../service/client.service';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { Component, DestroyRef, EventEmitter, inject, Input, Output } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { FormControl } from '@angular/forms';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-choose-client-project',
  standalone: true,
  imports: [SharedModule],
  templateUrl: './choose-client-project.component.html',
  styleUrl: './choose-client-project.component.scss'
})
export class ChooseClientProjectComponent {



  // Public properties
  @Input() options: any = {
    showSearch: true,//hiển thị search input hoặc truyền keyword
    keyword: '',
    data: []
  };
  @Output() ItemSelected = new EventEmitter<any>();
  @Output() IsSearch = new EventEmitter<any>();
  private clientService = inject(ClientService)
  listClient: any[] = [];
  customStyle: any = [];
  public filteredClient: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public clientFilterCtrl: FormControl = new FormControl();
  destroyRef = inject(DestroyRef);
  public _storeService = inject(StoreService)
  constructor(
  ) { }

  /**
   * On init
   */
  ngOnInit() {
    this.clientFilterCtrl.valueChanges
      .pipe()
      .subscribe(() => {
        this.filterClient();
      });
    this.GetAllClient();
  }
  GetAllClient() {
    let payload: Parameter = {
      Page: 0,
      PageSize: 100,
      Search: "",
    }
  //  if (this._storeService.getIdCompany()) {
      this.clientService.GetAllClient(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.listClient = res.data
          this.filterClient();
        }
      }
      )
 //   }

  }

  protected filterClient() {
    if (!this.listClient) {
      return;
    }

    let search = !this.options.showSearch ? this.options.keyword : this.clientFilterCtrl.value;
    if (!search) {
      this.filteredClient.next(this.listClient.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    if (search[0] == '@') {
      this.filteredClient.next(
        this.listClient.filter(bank => (bank.clientName.toLowerCase()).indexOf(search.replace('@', '')) > -1)
      );
    }
    else {
      this.filteredClient.next(
        this.listClient.filter(bank => bank.clientName.toLowerCase().indexOf(search) > -1)
      );
    }
  }
  select(user: any) {
    this.ItemSelected.emit(user)
  }
  stopPropagation(event: any) {
    this.IsSearch.emit(event)
  }
}
