
<app-innobook-modal-wrapper (onClose)="closeDialog()">
    <div class="relative  w-full  rounded-md p-2">
        <div class="flex items-center justify-center ">

            @if(isEdit)
            {
            <div class="relative  w-full  rounded-md p-2">
                <div class="flex items-center justify-center ">
                    <input type="text"
                        [(ngModel)]=categoryName
                        class="bg-white  border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="CategoryName" />

                    <div
                        (click)="Edit()"
                        class="cursor-pointer">
                        <span class="material-icons">
                            close
                        </span>
                    </div>
                </div>

            </div>
            }
            @else{
            <span class=" text-center text-lg font-bold">
                {{categoryName}}</span>
            <div
                class=" cursor-pointer">
                <button class="button-icon" (click)="Edit()">
                    <img class="w-[20px]"
                        src="../../../assets/img/icon/ic_edit.svg" alt="Icon">
                </button>
            </div>
            }

        </div>

    </div>

    @if(listCategoryItem.length>0)
    {
    <h6
        class="text-start pl-3 mb-2 font-bold">{{'CATEGORY.ListCategory'|translate}}</h6>
    @for(item of listCategoryItem; track item;let vi=$index)
    { <div class="flex items-center cursor-pointer pl-3 mb-2">
        <div class="relative  w-full border rounded-md p-2">
            <div class="flex items-center w-full justify-between">
                {{item.itemName}}

            </div>
        </div>
        @if(item.canEdit)
        {

        <div
            class=" text-end cursor-pointer">
            <button class="button-icon"
                (click)="RemoveItem(item.id,vi)">
                <img class="w-[20px]"
                    src="../../../assets/img/icon/ic_trash.svg"
                    alt="Icon">
            </button>
        </div>
        }

    </div>

    }
    }
    <div class="p-2">
        <div class="flex items-center text-blue-500" (click)="RemoveAll() ">
            @if(listCategory.length>0)
            {
            <span class="material-icons">
                remove
            </span>
            <span
                class="ml-2 cursor-pointer">{{'CATEGORY.RemoveAll'|translate}}</span>
            }
        </div>
        @for(sr of listCategory ; ; track sr; let
        i=$index)
        {
        <div class="flex items-center">
            <div class="relative w-full border rounded-md p-2 text-start">
                {{sr.itemName}}
            </div>
            <div
                class=" text-end cursor-pointer">
                <button class="button-icon"
                    (click)="RemoveService(i)">
                    <img class="w-[20px]"
                        src="../../../assets/img/icon/ic_trash.svg"
                        alt="Icon">
                </button>
            </div>
        </div>

        }
        <div>
            <div class="mb-2">
                @if(isAddSvcMode) {
                <input type="text"
                    [(ngModel)]=categoryNameItem
                    class="bg-bg-primary mt-2  border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'CATEGORY.CategoryItem'|translate" />
                }
                @if(categoryNameItem)
                {
                <div (click)="CreatedCategory()"
                    class="bg-white max-w-68 flex items-center p-2 cursor-pointer hover:bg-gray-400 text-blue-500">
                    <span class="material-icons">
                        add
                    </span> {{'CATEGORY.CreateCategoryItem'|translate}}
                    <span>" {{categoryNameItem}} "</span>

                </div>
                }

            </div>

            <button type="button" (click)="addCategory()"
                class="btn btn-outline-secondary btn-dashed">
                <span class="material-icons">add</span>
                {{'CATEGORY.AddNew'|translate}}
            </button>

        </div>
        <div class="w-full flex justify-around mt-3">
            <button (click)="closeDialog()" type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">{{'BUTTON.Cancel'|translate}}</button>

            <button (click)="Save()" type="button"
                [disabled]="listCategory.length===0&& listItemCategoryRemove.length===0&&!isEdit"
                [ngClass]="{'bg-green-700 hover:bg-green-800 text-white' :listCategory.length>0||listItemCategoryRemove.length>0 || isEdit,'bg-gray-400':listCategory.length==0||listItemCategoryRemove.length==0||!isEdit}"
                class="text-gray-900 focus:outline-none hover:bg-gray-300 focus:ring-4 focus:ring-gray-100 font-medium cursor-pointer rounded-lg text-sm px-5 py-2.5 me-2 mb-2">{{'BUTTON.Save'|translate}}</button>

        </div>
    </div>
</app-innobook-modal-wrapper>
