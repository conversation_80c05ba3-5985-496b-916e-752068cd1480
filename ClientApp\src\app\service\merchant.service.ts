import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Merchant } from '../dto/interface/merchant.interface';
import { Observable } from 'rxjs';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
const UrlApi = environment.HOST_API + "/api"

@Injectable({
  providedIn: 'root'
})
export class MerchantService {

  private http = inject(HttpClient)
  constructor() { }
  CreateMerchant(payload: any): Observable<Merchant> {
    return this.http.post<Merchant>(UrlApi + '/Merchant/CreateMerchant', payload);
  }

  GetAllMerchant(payload: Parameter) {
    return this.http.get<PaginatedResponse<Merchant>>(UrlApi + `/Merchant/GetAllMerchant?Page=${payload.Page}&PageSize=${payload.PageSize}&Search=${payload.Search}`);
  }



}
