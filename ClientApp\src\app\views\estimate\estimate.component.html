<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <p class="text-text-primary text-headline-lg-bold">
      {{'ESTIMATE.Title'|translate}}
    </p>

    <button class="button-size-md button-primary" (click)="NewEstimate()">
      <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
      {{'ESTIMATE.Buttons.NewEstimate'|translate}}
    </button>
  </div>
</div>

<div
  class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">
  <div class="flex w-full items-center gap-[14px] flex-wrap justify-between">
    <app-inno-tabs
      [tabs]="listTypeView"
      [value]="currentTypeView"
      (onChange)="handleChangeTypeView($event)" />
    <div class="flex items-center gap-[8px]">
      <div class="w-full w-[340px] mxw600:w-[240px]">
        <app-inno-input-search
          height="36px"
          [value]="textSearch"
          (onChange)="handleSearch($event)" />
      </div>
      <!-- <app-inno-popover
          position="bottom-start"
          [content]="contentFilter"
          [isClearPadding]="true"
          [isClickOnContentToClose]="false"
          >
          <button target class="button-icon border-2 border-border-primary">
            <img src="../../../assets/img/icon/ic_filter.svg" alt="Icon">
          </button>
          <ng-template #contentFilter>
            <div class="w-[480px] mxw600:w-[350px]">
              Functions developing
            </div>
          </ng-template>
        </app-inno-popover> -->
    </div>
  </div>
</div>

<div class="container-full grid grid-cols-3 mt-[24px] mxw1100:grid-cols-1"
  [ngClass]="{
      'grid-cols-3': currentTypeView === invoiceView.Created_Tab,
      'grid-cols-2': currentTypeView === invoiceView.Sent_To_Me_Tab
    }">
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'ESTIMATE.Summary.TotalOverdue'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">${{calculationInvoice?.totalOverdue
      | decimal:2 |formatNumber }}</p>
  </div>
  @if(currentTypeView === invoiceView.Created_Tab) {
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'ESTIMATE.Summary.TotalInDraft'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">{{calculationInvoice?.totalDraft
      | decimal:2
      | formatNumber}}</p>
  </div>
  }
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'ESTIMATE.Summary.TotalOutstanding'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">${{calculationInvoice?.totalAmount
      | decimal:2 |formatNumber }}</p>
  </div>
</div>

<div class="w-full mt-[24px]">
  @if(currentTypeView === invoiceView.Created_Tab) {
  <app-estimate-management />
  } @else {
  <app-estimate-send-to-me />
  }
</div>
