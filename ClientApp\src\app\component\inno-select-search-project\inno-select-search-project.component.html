<app-inno-popover
  position="bottom-start"
  [content]="templateSearchProject"
  [isClickOnContentToClose]="false"
  [isClearPadding]="true"
  (onOpen)="loadData()">

  @if(templateTrigger) {
  <ng-container target *ngTemplateOutlet="templateTrigger;"></ng-container>
  } @else {
  <button target class="dropdown-invisible flex gap-[4px]">
    Select project or client <img class="w-[16px] translate-y-[2px]"
      src="../../../assets/img/icon/ic_arrow_down_gray.svg" alt="Icon">
  </button>
  }

  <ng-template #templateSearchProject>
    <div class="min-w-[320px]">
      <app-inno-input-search-result
        [placeholder]="'COMMON.SearchProjectsClients'|translate"
        (onChange)="handleSearch($event)"
        [data]="listOptionPreview"
        [isNotFound]="!listOptionPreview.length"
        [isEmptyData]="!listOptionOriginal.length"
        [isLoading]="isLoading"
        [optionTemplate]="optionTemplate"
        [footerTemplate]="isShowCreateButton ? footerTemplate : null"
        [defaultValue]="defaultTextSearch">
        <ng-template #optionTemplate let-item>
          <div
            (click)="handleChooseOption(item)"
            [ngClass]="{'pl-8': isProjectOption(item)}"
            class="w-full flex p-[8px] items-center gap-[10px] rounded-md cursor-pointer hover:bg-bg-brand-primary"
            [class.selected]="item.value === value">
            @if(item?.metadata?.type == 'client') {
            <ngx-avatars
              [size]="32"
              [name]="item.label" />
            } @else {
            <div
              class="w-s[32px] h-[32px] rounded-full overflow-hidden flex justify-center items-center bg-bg-brand-primary shrink-0">
              <img class="w-[16px]"
                src="../../../assets/img/icon/ic_file_green.svg" alt="Icon">
            </div>
            }
            <div class="w-full">
              <p
                class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle">
                {{ item.label }}
              </p>
              @if(item.description) {
              <p
                class="line-clamp-1 text-text-tertiary text-text-xs-regular txtDescription">
                {{ item.description }}
              </p>
              }
            </div>
          </div>
        </ng-template>
        <ng-template #footerTemplate>
          <button (click)="handleCreateNewProject()"
            class="p-[12px] gap-[12px] text-text-brand-primary text-text-sm-semibold w-full flex items-center hover:bg-bg-brand-primary rounded-md cursor-pointer">
            <img src="../../../assets/img/icon/ic_add_green.svg" alt="Icon">
            {{'PROJECT.NewAProject'|translate}}
          </button>
        </ng-template>
      </app-inno-input-search-result>
    </div>
  </ng-template>
</app-inno-popover>
