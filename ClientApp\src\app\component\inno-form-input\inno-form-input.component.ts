import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  OnChanges,
  SimpleChanges,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import {
  FormControl,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
  AbstractControl
} from '@angular/forms';
import { SharedModule } from '../../module/shared.module';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { InnoErrorMMessageComponent } from '../inno-error-message/inno-error-message.component';

@Component({
  selector: 'app-inno-form-input',
  templateUrl: './inno-form-input.component.html',
  styleUrls: ['./inno-form-input.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    NgxMaskDirective,
    InnoErrorMMessageComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormInputComponent),
      multi: true
    },
    provideNgxMask()
  ]
})
export class InnoFormInputComponent implements ControlValueAccessor, AfterViewInit {
  @Input() isRequired?: boolean;
  @Input() isTable?: boolean;
  @Input() type?: 'text' | 'number' | 'email' | 'password' = 'text';
  @Input() label?: string = '';
  @Input() autocomplete?: string = 'off';
  @Input() removeZeros?: boolean;
  @Input() placeholder?: string = '';
  @Input() value?: string | number = '';
  @Input() pattern: string | RegExp = '';
  @Input() mask?: string = '';
  @Input() formControl?: FormControl;
  @Input() errorMessages?: { [key: string]: string };
  @Input() inputClassName?: string = "";

  @Output() onChange = new EventEmitter<string>();

  @ViewChild('inputElement') inputElement?: ElementRef = undefined;

  constructor() { }

  ngAfterViewInit(): void {
    if (this.inputElement) {
      // Because can not set value directly if mask is set
      setTimeout(() => {
        this.inputElement!.nativeElement.value = this.value;
      }, 10)
    }
  }

  registerOnChange(fn: (value: string) => void): void { }

  registerOnTouched(fn: () => void): void { }

  setDisabledState(isDisabled: boolean): void { }

  writeValue(value: string): void { }

  hasError() {
    return (this.formControl?.invalid && (this.formControl.dirty || this.formControl.touched));
  }

  getErrorMessage(): string {
    if (!this.hasError()) return '';

    if (this.formControl?.errors && this.errorMessages) {
      for (const errorType in this.formControl.errors) {
        if (this.errorMessages[errorType]) {
          return this.errorMessages[errorType];
        }
      }
    }
    return '';
  }
  removeLeadingZeros(event: Event) {
    //  Remove leading zeros
    const input = event.target as HTMLInputElement;
    let value = input.value;
    // Check if the input is of type "number"
    if (input.type === 'number' && this.removeZeros) {
      if (/^0\d+/.test(value) && !/^0\.\d+$/.test(value)) {
        input.value = input.value.replace(/^0+/, '');
      }
      else {
        input.value = input.value
      }
    }
  }
  handleChange(event: any): void {
    this.onChange.emit(event?.target?.value ?? '');
  }
}
