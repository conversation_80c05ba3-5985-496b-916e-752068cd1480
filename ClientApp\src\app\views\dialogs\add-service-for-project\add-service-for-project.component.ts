import { SortGird } from 'app/dto/interface/SortGird.interface';
import { StoreService } from 'app/service/store.service';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { ModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { ServiceService } from './../../../service/service.service';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { SharedModule } from 'app/module/shared.module';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { Component, DestroyRef, EventEmitter, Inject, inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetServiceRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { Service } from 'app/dto/interface/service.interface';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { getNameTaxes } from 'app/utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-add-service-for-project',
  standalone: true,
  imports: [PagerModule, InnoEmptyDataComponent, InnoSpinomponent, InnoModalWrapperComponent, InnoFormInputComponent, InnoSelectSearchProjectComponent, InnoModalFooterComponent, InnoFormCheckboxComponent, SharedModule, FormatNumberPipe],
  templateUrl: './add-service-for-project.component.html',
  styleUrl: './add-service-for-project.component.scss'
})
export class AddServiceForProjectComponent implements OnInit {
  public sort: SortGird
  @Output() cancel: EventEmitter<any> = new EventEmitter<any>();
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  public getNameSelectedTaxes = getNameTaxes
  public listService: Service[] = []
  public listIndexServiceSelected: number[] = []
  public projectId!: string
  public projectName!: string
  public isFetching: boolean = false
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  sortDirection: 'Ascending' | 'Descending' = 'Ascending';
  sortColumn: string = ''

  public _storeService = inject(StoreService)
  private serviceService = inject(ServiceService)
  private destroyRef = inject(DestroyRef)
  private modifyItemAndServiceDialog = inject(ModifyItemsAndServiceDialog)
  constructor(public dialogRef: MatDialogRef<AddServiceForProjectComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.projectId = this.data.projectId;
  }
  ngOnInit(): void {
    this.sortDirection = "Ascending"
    this.sortColumn = 'serviceName'
    this.sort = {
      columnName: this.sortColumn,
      direction: this.sortDirection
    }
    this.handleSort(this.sortDirection);
  }
  static getComponent(): typeof AddServiceForProjectComponent {
    return AddServiceForProjectComponent;
  }
  getUniqueItemsByName(arr1, arr2) {
    return [
      ...arr1.filter(item1 => !arr2.some(item2 => item2.serviceName === item1.serviceName)),
      ...arr2.filter(item2 => !arr1.some(item1 => item1.serviceName === item2.serviceName))
    ];
  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllService();
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllService();
    }
  }
  LoadAllService() {
    this.isFetching = true
    const params: GetServiceRequestParam = {
      Page: this.currentPage ?? 1,
      PageSize: this.pageSizesDefault,
      ...this.sort,
      ProjectId: this.projectId,
      isInProject: false
    };
    this.serviceService.GetAllService(params).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.totalPages = res.totalRecords
        this.listService = res.data.map(item => ({ ...item, projectName: item.project?.projectName, isNewItem: true }));
        this.isFetching = false;
        if (this.data.listService) {
          this.listService = this.getUniqueItemsByName(this.data.listService, this.listService)
        }
      }
    });
  }
  handleCreateNewService() {
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Service,
      isShowProject: false
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.LoadAllService();
        }
      })
    });
  }


  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexServiceSelected = this.listService.map((_item, index) => index)
    } else {
      this.listIndexServiceSelected = []
    }
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexServiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexServiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexServiceSelected = newListSelected
  }
  sortName(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'Ascending' ? 'Descending' : 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    } else {
      this.sortColumn = column;
      this.sortDirection = 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    }
  }

  handleCancel() {
    this.dialogRef.close()
  }
  handleSort(sortDirection: string) {
    switch (sortDirection) {
      case 'Descending':
        this.LoadAllService();
        break;
      case 'Ascending':
        this.LoadAllService();
        break;
      default:
        break;
    }
  }
  handleSubmit() {
    const listInvoiceItemSelected = this.listService.filter((_item, index) => this.listIndexServiceSelected.includes(index))
    this.dialogRef.close(listInvoiceItemSelected.map(({ project, ...rest }) => rest))
  }
}
