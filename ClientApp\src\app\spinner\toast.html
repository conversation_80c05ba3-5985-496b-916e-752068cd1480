<!-- <ejs-toast #toasttype [position]='position' [showProgressBar]="showProgressBar"></ejs-toast> -->

<ejs-toast #toasttype
  [position]='position'
  cssClass="customToastWrapper"
  width="auto"
  [animation]="toastAnimation"
  >
  <ng-template #template>
    <div class="p-[16px] flex gap-[16px] rounded-md w-auto max-w-[616px] border bg-bg-primary shadow-md"
      [ngClass]="{
        '!border-border-success': dataToast?.type === 'success',
        '!border-border-danger': dataToast?.type === 'error',
        '!border-border-warning': dataToast?.type === 'warning',
        '!border-border-tertiary': dataToast?.type === 'info',
      }"
    >
      <div class="w-[40px] h-[40px] rounded-lg border border-border-primary-slight shadow-xs flex justify-center items-center shrink-0">
        @if(dataToast?.type === 'success') {
          <img class="w-[28px]" src="../../assets/img/icon/ic_success.svg" alt="Icon">
        } @else if(dataToast?.type === 'error') {
          <img class="w-[28px]" src="../../assets/img/icon/ic_danger.svg" alt="Icon">
        } @else if(dataToast?.type === 'warning') {
          <img class="w-[28px]" src="../../assets/img/icon/ic_warning.svg" alt="Icon">
        } @else {
          <img class="w-[28px]" src="../../assets/img/icon/ic_info.svg" alt="Icon">
        }
      </div>
      <div class="grow flex flex-col justify-center">
        <div class="w-full flex flex-col gap-[2px]">
          <p class="text-text-primary text-text-md-semibold">
            {{ title ?? '' }}
          </p>
          <p class="text-text-secondary text-text-sm-regular">
            {{ content ?? '' }}
          </p>
        </div>
      </div>
      <div (click)="closeToast()" class="button-icon shrink-0 mt-[4px] w-[32px] h-[32px]">
        <img src="../../assets/img/icon/ic_remove.svg" alt="Icon">
      </div>
    </div>
  </ng-template>
</ejs-toast>
