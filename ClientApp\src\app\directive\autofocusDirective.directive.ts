import { Directive, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';

@Directive({
    selector: '[appAutofocus]',
    standalone: true
})
export class AutofocusDirective implements AfterViewInit, OnDestroy {
    private timeoutId: any;
    constructor(private el: ElementRef) { }

    ngAfterViewInit() {
        this.timeoutId = setTimeout(() => {
            if (this.el.nativeElement) {
                this.el.nativeElement.focus();
            }
        }, 100);
    }
    ngOnDestroy() {
        if (this.timeoutId) {
            clearTimeout(this.timeoutId);
        }
    }
}
