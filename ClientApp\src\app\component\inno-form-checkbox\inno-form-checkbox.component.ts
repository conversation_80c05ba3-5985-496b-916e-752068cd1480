/* eslint-disable @angular-eslint/no-output-on-prefix */
import { Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import { SharedModule } from '../../module/shared.module';
import { FormControl, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-inno-form-checkbox',
  templateUrl: './inno-form-checkbox.component.html',
  styleUrls: ['./inno-form-checkbox.component.scss'],
  standalone: true,
  imports: [SharedModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormCheckboxComponent),
      multi: true
    }
  ]
})

export class InnoFormCheckboxComponent implements ControlValueAccessor {

  @Input() checked?: boolean;
  @Output() onChange = new EventEmitter<string>();

  @Input() formControl?: FormControl;
  @Input() errorMessages?: { [key: string]: string };

  constructor() {}

  registerOnChange(fn: (value: string) => void): void {}

  registerOnTouched(fn: () => void): void {}

  setDisabledState(isDisabled: boolean): void {}

  writeValue(value: string): void {}

  handleChange(event: any): void {
    this.onChange.emit(event?.target?.checked ?? false);
  }
}
