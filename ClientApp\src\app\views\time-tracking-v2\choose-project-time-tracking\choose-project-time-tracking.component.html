<div>
    <!-- <div class="max-h-72">

        @for( item of filteredProject | async; track item;let i=$index)
        {
        <div class="users-container">
            <div class="pl-2 cursor-pointer  hover:bg-gray-300 hover:rounded-md"
                (click)="select(item)">
                <div class="flex items-center mb-2">
                    <div
                        class="relative inline-flex items-center justify-center w-6 h-6 overflow-hidden bg-gray-400 rounded-full">
                        <span
                            class="font-medium text-gray-200">{{item.projectName.slice(0,1)}}</span>
                    </div>
                    <div class="p-2">
                        <div>{{item.projectName}}
                        </div>
                    </div>
                </div>

            </div>
        </div>
        }
    </div> -->

    @if(empty)
    {
    <div class="text-center">
        No result
    </div>
    }
    <div (click)="stopPropagation($event)">

        <mat-accordion #myaccordion="matAccordion" multi="true">
            @for( item of filteredProject | async; track item;let i=$index)
            {
            <mat-expansion-panel
                (opened)="onPanelOpened(item)"
                (closed)="onPanelClosed(item)"
                [hideToggle]="item.projects.length==0">
                <mat-expansion-panel-header>
                    <mat-panel-description>
                        <div class="flex items-center justify-between w-full">
                            <span class="font-bold"> {{item.clientName}}</span>
                            @if(item.projects.length>0)
                            {
                            <span class="text-sm text-gray-400 ml-2">
                                {{item.projects.length}} project</span>
                            }
                        </div>
                    </mat-panel-description>
                </mat-expansion-panel-header>
                <div>
                    @for(pro of item.projects ; track pro;let i=$index)
                    {
                    <div>
                        @if(pro.services.length>0)
                        {
                        <mat-accordion #myaccordion2="matAccordion"
                            multi="true">

                            <mat-expansion-panel
                                [expanded]="isPanelOpen"
                                (opened)="onPanelOpenedService(pro)"
                                style="    box-shadow: none;
    margin-bottom: 10px;">
                                <mat-expansion-panel-header>
                                    <div class="flex w-full justify-between">

                                        <div (click)="select(pro)"
                                            class="w-full flex items-center ">
                                            <span
                                                class="flex w-1 h-1 me-3 bg-gray-900 rounded-full"></span>
                                            <span
                                                class="text-blue-500">
                                                {{pro.projectName}}</span>

                                        </div>
                                        <div
                                            class="w-full flex items-center justify-center text-sm text-gray-400">
                                            @if(pro.services.length>0)
                                            {
                                            <div>

                                                <span class="mr-1">
                                                    {{pro.services.length}}
                                                </span>
                                                <span>service</span>
                                            </div>

                                            }
                                        </div>
                                    </div>

                                </mat-expansion-panel-header>

                                @for(ser of pro.services ; track ser;let
                                i=$index)
                                {
                                <div (click)="SelectedService(ser)"
                                    class="p-2 hover:bg-slate-300 cursor-pointer rounded-xl">
                                    {{ser.serviceName}}
                                </div>
                                }

                            </mat-expansion-panel>
                        </mat-accordion>
                        }
                        @else{
                        <div
                            class="p-2 hover:bg-slate-300 cursor-pointer rounded-xl">
                            <div (click)="select(pro)"
                                class="w-full flex items-center">
                                <span
                                    class="flex w-1 h-1 me-3 bg-gray-900 rounded-full"></span>
                                <span
                                    class="text-blue-500 text-inline">
                                    {{pro.projectName}}</span>

                            </div>
                        </div>
                        }

                    </div>
                    }
                </div>
            </mat-expansion-panel>
            }

        </mat-accordion>
    </div>

</div>
