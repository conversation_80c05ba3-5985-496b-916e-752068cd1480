import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class EditServiceDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/item-services/edit-service/edit-service.component'
    );

    return this.matDialog.open(
      importedModuleFile.EditServiceComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        width: '450px',
        data,
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
