<app-inno-modal-wrapper
    [title]="!data ? 'CLIENT.ADD_CLIENT_FORM.Title': 'CLIENT.ADD_CLIENT_FORM.TitleEdit' "
    (onClose)="handleClose()">
    <form class="w-full" [formGroup]="newclientForm">
        <div class="w-full p-[16px] flex flex-col gap-[16px]">
            <app-inno-form-input
                [label]="'CLIENT.ADD_CLIENT_FORM.FirstName' | translate"
                [formControl]="f['firstname']"
                [value]="f['firstname'].value"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.FirstNamePlaceholder' | translate" />

            <app-inno-form-input
                [label]="'CLIENT.ADD_CLIENT_FORM.LastName' | translate"
                [formControl]="f['lastname']"
                [value]="f['lastname'].value"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.LastNamePlaceholder' | translate" />

            <app-inno-form-input
                [isRequired]="true"
                [label]="'CLIENT.ADD_CLIENT_FORM.ClientName' | translate"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.ClientNamePlaceholder' | translate"
                [formControl]="f['clientname']"
                [value]="f['clientname'].value"
                [errorMessages]="{required: 'CLIENT.ADD_CLIENT_FORM.ClientNameRequired' | translate}" />

            <app-inno-form-input
                [isRequired]="true"
                type="email"
                [label]="'CLIENT.ADD_CLIENT_FORM.Email' | translate"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.EmailPlaceholder' | translate"
                [formControl]="f['email']"
                [value]="f['email'].value"
                [errorMessages]="{
                  required: 'CLIENT.ADD_CLIENT_FORM.EmailRequired' | translate,
                  email:'CLIENT.ADD_CLIENT_FORM.EmailInvalid' | translate
                }" />

            <div class=" flex gap-2">
                <app-inno-form-input
                    [isRequired]="true"
                    class="w-full"
                    [label]="'CLIENT.ADD_CLIENT_FORM.PhoneNumber' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.PhoneNumberPlaceholder' | translate"
                    [formControl]="f['phone']"
                    [value]="f['phone'].value"
                    [errorMessages]="{
                        minlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        maxlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        pattern: 'CLIENT.ADD_CLIENT_FORM.PhonePattern' | translate
                    }"
                    mask="(*************"
                    autocomplete="phone" />
                <app-inno-form-input
                    class="w-full"
                    [isRequired]="false"
                    [label]="'CLIENT.ADD_CLIENT_FORM.Post' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.PostPlaceholder' | translate"
                    [formControl]="f['postePhoneNumber']"
                    [value]="f['postePhoneNumber'].value" />
            </div>
            <div class=" flex gap-2">
                <app-inno-form-input
                    class="w-full"
                    [label]="'CLIENT.ADD_CLIENT_FORM.BusinessPhone' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.BusinessPhonePlaceholder' | translate"
                    [formControl]="f['businessphone']"
                    [value]="f['businessphone'].value"
                    [errorMessages]="{
                        minlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        maxlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        pattern: 'CLIENT.ADD_CLIENT_FORM.PhonePattern' | translate
                }"
                    mask="(*************"
                    autocomplete="phone" />
                <app-inno-form-input
                    class="w-full"
                    [isRequired]="false"
                    [label]="'CLIENT.ADD_CLIENT_FORM.Post' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.PostPlaceholder' | translate"
                    [formControl]="f['posteBusinessPhoneNumber']"
                    [value]="f['posteBusinessPhoneNumber'].value" />
            </div>
            <div class=" flex gap-2">
                <app-inno-form-input
                    class="w-full"
                    [label]="'CLIENT.ADD_CLIENT_FORM.MobilePhone' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.MobilePhonePlaceholder' | translate"
                    [formControl]="f['mobilephone']"
                    [value]="f['mobilephone'].value"
                    [errorMessages]="{
                        minlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        maxlength: 'CLIENT.ADD_CLIENT_FORM.PhoneLength' | translate,
                        pattern: 'CLIENT.ADD_CLIENT_FORM.PhonePattern' | translate
                }"
                    mask="(*************"
                    autocomplete="phone" />

                <app-inno-form-input
                    class="w-full"
                    [isRequired]="false"
                    [label]="'CLIENT.ADD_CLIENT_FORM.Post' | translate"
                    [placeholder]="'CLIENT.ADD_CLIENT_FORM.PostPlaceholder' | translate"
                    [formControl]="f['posteMobilePhoneNumber']"
                    [value]="f['posteMobilePhoneNumber'].value" />
            </div>
            <app-inno-form-select-search
                [label]="'CLIENT.ADD_CLIENT_FORM.Country' | translate"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.CountryPlaceholder' | translate"
                [options]="countriesOption"
                [formControl]="f['country']"
                [value]="f['country'].value"
                [errorMessages]="{ required: 'Country is required' }"
                placeholder="Select country" />

            <app-inno-form-input
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.Address1Placeholder' | translate"
                [label]="'CLIENT.ADD_CLIENT_FORM.Address1' | translate"
                [formControl]="f['address1']"
                [value]="f['address1'].value" />

            <app-inno-form-input
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.Address2Placeholder' | translate"
                [label]="'CLIENT.ADD_CLIENT_FORM.Address2' | translate"
                [formControl]="f['address2']"
                [value]="f['address2'].value" />

            <app-inno-form-input
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.TownCityPlaceholder' | translate"
                [label]="'CLIENT.ADD_CLIENT_FORM.TownCity' | translate"
                [formControl]="f['tow_city']"
                [value]="f['tow_city'].value" />

            <app-inno-form-input
                [label]="'CLIENT.ADD_CLIENT_FORM.StateProvince' | translate"
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.StateProvincePlaceholder' | translate"
                [formControl]="f['state_province']"
                [value]="f['state_province'].value" />

            <app-inno-form-input
                [placeholder]="'CLIENT.ADD_CLIENT_FORM.PostalCodePlaceholder' | translate"
                [label]="'CLIENT.ADD_CLIENT_FORM.PostalCode' | translate"
                [formControl]="f['postal_code']"
                [value]="f['postal_code'].value" />
        </div>

    </form>
    <div footer>
        <app-inno-modal-footer
            (onSubmit)="onSubmit()"
            (onCancel)="handleCancel()" />
    </div>
</app-inno-modal-wrapper>
