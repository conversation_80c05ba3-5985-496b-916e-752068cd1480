import { AuthenticationService } from 'app/auth/service/authentication.service';
import { CategoryService } from './../../../service/category.service';
import { ToastService } from 'app/service/toast.service';
import { SpinnerService } from 'app/service/spinner.service';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { MatDialogRef } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Category } from '../../../dto/interface/category.interface';

@Component({
  selector: 'app-new-category',
  standalone: true,
  imports: [SharedModule, InnobookModalWrapperComponent],
  templateUrl: './new-category.component.html',
  styleUrl: './new-category.component.scss'
})
export class NewCategoryComponent implements OnInit {
  private _spinnerService = inject(SpinnerService)
  destroyRef = inject(DestroyRef);
  private _toastService = inject(ToastService)
  private _categoryService = inject(CategoryService)

  static getComponent(): typeof NewCategoryComponent {
    return NewCategoryComponent;
  }

  constructor(public dialogRef: MatDialogRef<NewCategoryComponent>) {

  }
  public isAddSvcMode = true;
  listCategory: Category[] = []
  categoryName!: string;
  ngOnInit(): void {

  }
  RemoveAll() {
    this.listCategory = []
  }
  RemoveService(i: number) {
    this.listCategory.splice(i, 1);
  }
  CreatedCategory() {
    this.isAddSvcMode = false
    this.listCategory.push({
      categoryName: this.categoryName,
    })
    this.categoryName = ''
  }
  addCategory() {
    this.isAddSvcMode = true;
  }
  closeDialog() {
    this.dialogRef.close();
  }
  Save() {
    this._spinnerService.show();
    this._categoryService.CreateCategory(this.listCategory).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Save", "Success");
        this._spinnerService.hide();
        this.dialogRef.close(res);
      }
    })
  }
}
