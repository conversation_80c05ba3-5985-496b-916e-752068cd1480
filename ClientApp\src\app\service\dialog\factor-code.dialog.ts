import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class FactorCodeDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../auth/factor-code/factor-code.component'
    );

    return this.matDialog.open(
      importedModuleFile.FactorCodeComponent.getComponent(),
      {
        data,
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
