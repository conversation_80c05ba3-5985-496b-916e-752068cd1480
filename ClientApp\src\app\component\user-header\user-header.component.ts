import { AuthenticationService } from './../../auth/service/authentication.service';
import { Component, inject } from "@angular/core";
import { Router } from "@angular/router";
import {
  DropDownButtonModule,
  ItemModel,
  MenuEventArgs,
} from "@syncfusion/ej2-angular-splitbuttons";

@Component({
  selector: "app-user-header",
  standalone: true,
  imports: [DropDownButtonModule],
  templateUrl: "./user-header.component.html",
  styleUrl: "./user-header.component.scss",
})
export class UserHeaderComponent {
  private router = inject(Router)
  private authenticationService = inject(AuthenticationService);
  public items: ItemModel[] = [
    {
      text: "Profile",
    },
    {
      text: "Log Out",
    },
  ];

  constructor() { }

  public select(args: MenuEventArgs) {
    switch (args.item.text) {
      case "Log Out":
        this.authenticationService.logout();
        break;
      case "Profile":
        this.router.navigate(["/profile"]);
        break;
      default:
        break;
    }
  }
}
