import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'environments/environment';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class PermissionService {
  private http = inject(HttpClient)
  constructor() { }

  GetPermission() {
    return this.http.get(UrlApi + '/Permission');
  }
}
