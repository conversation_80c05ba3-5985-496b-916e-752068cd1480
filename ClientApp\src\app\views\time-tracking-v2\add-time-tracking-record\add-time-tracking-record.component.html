<div
  class="w-full bg-bg-primary rounded-md border-2 border-dashed py-[16px] px-[12px] border-border-secondary flex justify-between items-start gap-3 flex-wrap transition-all">
  <div class="grow flex flex-col gap-[12px]">
    <div class="flex">
      <app-inno-select-search-project
        [templateTrigger]="templateTriggerSelectProject"
        (onSelect)="handleSelectProject($event)"
        [isOnlySelectProject]="true"
        [isTimetracking]="true"
        [value]="previewWorkingInfo?.value" />

      <ng-template #templateTriggerSelectProject>
        <button
          class="px-[12px] py-[5px] text-headline-sm-semibold text-text-placeholder-slight cursor-pointer border-2 border-transparent rounded-md hover:border-border-primary transition-all"
          [ngClass]="{'text-text-primary': previewWorkingInfo!! }">
          {{previewWorkingInfo?.metadata?.objectClient?.clientName}} -
          {{ previewWorkingInfo?.label ?? 'TIMETRACKING.EntryPlaceholder'|
          translate }}
        </button>
      </ng-template>
      @if(previewWorkingInfo)
      {
      <app-inno-select-search-service
        [templateTrigger]="templateTriggerSelectService"
        (onSelect)="handleSelectServices($event)"
        [lable]="previewWorkingInfo.label"
        [value]="projectId" />

      <ng-template #templateTriggerSelectService>
        <button
          class="px-[12px] py-[5px] text-headline-sm-semibold text-text-placeholder-slight cursor-pointer border-2 border-transparent rounded-md hover:border-border-primary transition-all"
          [ngClass]="{'text-text-primary': previewServiceInfo!! }">
          {{ previewServiceInfo?.label ?? 'TIMETRACKING.ChooseService'|
          translate }}
        </button>
      </ng-template>
      }
    </div>

    @if(previewWorkingInfo) {
    <div class="w-full">
      <app-inno-form-textarea
        placeholder="Note"
        [isAbleResize]="true"
        [value]="previewDescription"
        (onChange)="handleChangeNote($event)" />
      <div class="w-full flex flex-wrap items-center gap-[12px] mt-[12px]">
        <app-inno-select-search-tags
          [templateTrigger]="templateTriggerSelectTags" />
        <ng-template #templateTriggerSelectTags>
          <app-inno-tags target value="Only InnoBook" />
        </ng-template>

        <div class="w-[170px]">
          <app-inno-datepicker
            placeholder="Select previewDate"
            [value]="previewDate"
            (onChange)="handleChangeDate($event)" />
        </div>
        @if(!isInternal)
        {
        <app-inno-form-checkbox
          [checked]="previewBillable"
          (onChange)="handleChangeBillable($event)">
          {{'TIMETRACKING.Billable'|
          translate }}
        </app-inno-form-checkbox>
        }
      </div>
    </div>
    }
  </div>

  <div class="flex items-center gap-[12px] flex-wrap">
    <div class="flex items-center gap-[12px] flex-wrap">
      @if(currentMode === 2) {
      <div class="flex items-center">
        @if(isEditTimerHours)
        {
        <div class="mr-2">
          <app-inno-enter-edit-hours
            [dynamicWidth]="120"
            [value]="timerHoursEdit"
            (onChange)="handleEditEndTime($event)" />
        </div>

        }
        @else
        {
        <p (click)="EditTimer()"
          class="px-[8px] cursor-pointer rounded-sm hover:bg-bg-tertiary py-[4px] text-text-primary text-headline-md-bold">
          {{ timerHours || '00:00:00' }}
        </p>
        }
        @if(previewTimerStatus === 'running') {
        <button (click)="handlePauseOrResumeTime()"
          class="ml-[5px] bg-bg-brand-primary cursor-pointer rounded-full w-[36px] h-[36px] flex justify-center items-center border-1 border-bg-brand-strong-hover">
          <img class="w-[16px]" src="../../../assets/img/icon/ic_pause.svg"
            alt="Icon">
        </button>
        } @else if(previewTimerStatus === 'paused') {
        <button (click)="handlePauseOrResumeTime()"
          class="ml-[5px] bg-bg-brand-primary cursor-pointer rounded-full w-[36px] h-[36px] flex justify-center items-center border-1 border-bg-brand-strong-hover">
          <img class="w-[16px]" src="../../../assets/img/icon/ic_play.svg"
            alt="Icon">
        </button>
        }
      </div>
      @if(!previewTimerStatus) {
      <button class="button-primary button-size-md"
        (click)="handlePauseOrResumeTime()">
        {{'TIMETRACKING.StartTimer'|
        translate }}
      </button>
      } @else {
      <div class="flex gap-[12px] items-center">
        <button
          class="button-primary button-size-md"
          [disabled]="!previewWorkingInfo || !previewDate"
          (click)="handleAddTimeTrackingRecord()">
          {{'TIMETRACKING.LogTime'|
          translate }}
        </button>
        <button class="button-outline button-size-md"
          (click)="handleDiscard()">
          {{'TIMETRACKING.Discard'|
          translate }}
        </button>
      </div>
      }
      } @else {
      <app-inno-enter-hours
        [value]="previewTimeEnd"
        (onChange)="handleChangeEndTime($event)" />
      <div class="flex gap-[12px] items-center">
        <button
          class="button-primary button-size-md"
          (click)="handleAddTimeTrackingRecord()"
          [disabled]="!previewTimeEnd || !previewDate || !projectId">
          {{'TIMETRACKING.AddEntry'|
          translate }}
        </button>
        @if(previewWorkingInfo) {
        <button class="button-outline button-size-md"
          (click)="handleResetAddEntry()">
          {{'BUTTON.Cancel'|
          translate }}
        </button>
        }
      </div>
      }
    </div>
    <app-inno-tabs-v2 [value]="currentMode" [tabs]="listMode"
      (onChange)="handleChangeMode($event)" />
  </div>
</div>
