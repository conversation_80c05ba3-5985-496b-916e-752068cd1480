import { SpinnerService } from 'app/service/spinner.service';
import { InvoiceService } from 'app/service/invoice.service';
import { Component, DestroyRef, inject, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoSelectSearchClientComponent } from 'app/component/inno-select-search-client/inno-select-search-client.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { getFullAddress } from 'app/helpers/common.helper';
import { SharedModule } from 'app/module/shared.module';
import { StoreService } from 'app/service/store.service';
import { ToastService } from 'app/service/toast.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { concatMap } from 'rxjs';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';

@Component({
  selector: 'app-send-invoice',
  templateUrl: './send-invoice.component.html',
  styleUrls: ['./send-invoice.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    MatFormFieldModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoSelectSearchClientComponent
  ]
})
export class SendInvoiceComponent implements OnInit {
  public sendInvoiceForm!: UntypedFormGroup;
  public listEmailSelected: any[] = []
  public invoice: Record<string, any> | null = null;

  private _storeService = inject(StoreService)
  private _toastService = inject(ToastService)
  private _invoiceService = inject(InvoiceService)
  private destroyRef = inject(DestroyRef);
  private spinnerService = inject(SpinnerService)
  private formBuilder = inject(UntypedFormBuilder)

  static getComponent(): typeof SendInvoiceComponent {
    return SendInvoiceComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<SendInvoiceComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.sendInvoiceForm = this.formBuilder.group({
      email: ["", Validators.compose([Validators.required, Validators.email])],
    })

  }
  get f() {
    return this.sendInvoiceForm.controls as Record<string, FormControl>;;
  }
  handleClose() {
    this.dialogRef.close();
  }
  ngOnInit(): void {
    this.invoice = this.data ?? {}
  }

  handleCancel() {
    this.dialogRef.close();
  }

  get listSelectedIdClient() {
    return this.listEmailSelected.map(e => e.id)
  }

  get businessInfo() {
    const business = this._storeService.get_UserBusiness();

    return {
      businessName: business?.company?.businessName ?? '',
      businessPhoneNumber: business?.company?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: business?.company?.adress ?? '',
        addressLine2: business?.company?.adress2 ?? '',
        stateProvince: business?.company?.province ?? '',
        postalCode: business?.company?.postalCode ?? '',
        country: business?.company?.country ?? '',
      }),
    }
  }

  handleSelectClient(item: IFilterDropdownOption) {
    const client = item?.metadata?.client
    if (!client) return

    const isExist = this.listEmailSelected.some((e: any) => e.id === client.id)
    if (isExist) return

    this.listEmailSelected.push(client)

  }

  handleRemoveEmailClient(e: any, index: number) {
    e.stopPropagation()
    this.listEmailSelected.splice(index, 1)
  }

  handleShare() {
    this._toastService.showInfo("The feature is in development.", " ")
  }
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  }
  onSubmit() {
    this.listEmailSelected.push(this.f['email'].value)
    this.f['email'].setValue("")

  }

  handleSave() {
    if (this.listEmailSelected.length > 0) {

      this.spinnerService.show();
      this._invoiceService.CreatedInvoice(this.invoice).pipe(
        takeUntilDestroyed(this.destroyRef),
        concatMap((res: any) => {
          if (res) {
            const payloadInvoiceSend = {
              invoiceId: res.id,
              clientId: this.data.clientId,
              isEstimate: false,
              ListEmail: this.listEmailSelected
            }
            return this._invoiceService.CreatedInvoiceSend(payloadInvoiceSend).pipe(takeUntilDestroyed(this.destroyRef));
          }
          return [];
        })
      ).subscribe(
        (secondRes: any) => {
          if (secondRes) {
            const listEmail = this.listEmailSelected
            let payload = {
              id: secondRes.id,
              businessName: this._storeService.get_UserBusiness().businessName,
              invoiceNumber: secondRes.invoiceNumber,
              payAmount: secondRes.paidAmount,
              date: this.formatDate(secondRes.invoiceDate),
              listEmail: listEmail
            }
            this._invoiceService.SendMailInvoice(payload)
              .subscribe(
            )
            this.spinnerService.hide();
            this.dialogRef.close(secondRes)
            this._toastService.showSuccess("Send", "Success");
          }
        },
        (error: any) => {
          console.error('Error:', error);
        }
      );
    }
    else {
      this._toastService.showInfo("Infor", "Enter to confirm the email you want to send");
    }

  }
}
