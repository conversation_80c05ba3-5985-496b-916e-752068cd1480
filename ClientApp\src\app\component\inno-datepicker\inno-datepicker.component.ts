import { CUSTOM_ELEMENTS_SCHEMA,EventEmitter, Output, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { DatePickerModule } from '@syncfusion/ej2-angular-calendars';

@Component({
  selector: 'app-inno-datepicker',
  templateUrl: './inno-datepicker.component.html',
  styleUrls: ['./inno-datepicker.component.scss'],
  standalone: true,
  imports: [ DatePickerModule ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class InnoDatepickerComponent implements OnChanges {

  @Input() mode?: 'day' | 'week' | 'month' = 'day';
  @Input() id?: string;
  @Input() enableMask?: boolean;
  @Input() name?: string;
  @Input() format?: string = "dd-MM-yyyy"
  @Input() placeholder?: string
  @Input() value?: Date
  @Output() onChange = new EventEmitter<any>();

  public datePickerValue?: Date
  public fields: object = { text: 'label', value: 'value' };
  public start: string = 'Month';
  public depth: string = 'Month';
  public showTodayButton = true;
  public weekNumber = false;

  constructor() {}

  ngOnChanges(changes: SimpleChanges) {
    const _mode = changes['mode']?.currentValue ?? changes['value']?.currentValue
    if(!_mode) return
    this.updateModeSettings(_mode);
  }

  updateModeSettings(mode: 'day' | 'week' | 'month' | undefined) {
    switch (mode) {
      case 'day':
        this.start = 'Month';
        this.depth = 'Month';
        this.showTodayButton = true;
        this.weekNumber = false;
        break;

      case 'week':
        this.start = 'Month';
        this.depth = 'Month';
        this.showTodayButton = false;
        this.weekNumber = true;
        break;

      case 'month':
        this.start = 'Year';
        this.depth = 'Year';
        this.showTodayButton = false;
        this.weekNumber = false;
        break;

      default:
        break;
    }
  }

  handleChangeValue(e: any) {
    const _dateSelected = e?.value ?? undefined
    this.onChange.emit(_dateSelected)
  }
}
