import { Component, Input } from '@angular/core';
import { SharedModule } from '../../module/shared.module';

@Component({
  selector: 'app-inno-error-message',
  templateUrl: './inno-error-message.component.html',
  styleUrls: ['./inno-error-message.component.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class InnoErrorMMessageComponent {

  @Input() message: string = ''
  constructor() {}
}
