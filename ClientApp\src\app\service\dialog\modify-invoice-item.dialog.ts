import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class ModifyInvoiceItemDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/invoice/dialog/new-invoice/modify-invoice-item/modify-invoice-item.component'
    );

    return this.matDialog.open(
      importedModuleFile.ModifyInvoiceItemComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        width: '450px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
