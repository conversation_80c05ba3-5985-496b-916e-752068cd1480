<div class="input-content rounded-md  w-full border border-gray-300">
    <form class="p-3"
        [formGroup]="profileForm" (ngSubmit)="onSubmit()">

        <div class="mb-4">
            <h5 class="fw-bold m-0">{{'PROFILE.Title'|translate}}</h5>
        </div>
        <div class="grid gap-6 mb-6 md:grid-cols-2">
            <div>
                <label for="_name"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'PROFILE.FirstName'|translate}}
                </label>
                <input type="text"
                    formControlName="firstname"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'PROFILE.FirstName'|translate"
                    required />
            </div>
            <div>
                <label for="_name"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'PROFILE.LastName'|translate}}
                </label>
                <input type="text" id="last_name"
                    formControlName="lastname"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'PROFILE.LastName'|translate" required />
            </div>
        </div>
        <div class="mb-6">
            <label for="_name"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'PROFILE.UserName'|translate}}

            </label>
            <input type="text" id="_name"
                formControlName="username"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5"
                [placeholder]="'PROFILE.UserName'|translate" />
        </div>
        <div class="mb-6">

            <label for="name"
                class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'PROFILE.TimeZone'|translate}}

            </label>
            <ejs-dropdownlist formControlName="timezone" id='ddlelement'
                #ddlelement [dataSource]='tzNames'
                (change)='onChange($event)'
                [fields]='fields' (filtering)='onFiltering($event)'
                [filterBarPlaceholder]="filterPlaceholder"
                [popupHeight]='height' [allowFiltering]='true'
                [placeholder]='watermark'></ejs-dropdownlist>

        </div>
        <div class="form-group mb-3">
            <div>
                <label for="_name"
                    class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'PROFILE.Email'|translate}}
                </label>
                <input type="email" id="_email"
                    formControlName="email"
                    class="bg-gray-200 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder="Email" required />
            </div>
        </div>
        <div class="w-full text-center">
            <button type="submit"
                [disabled]="!profileForm.valid"
                [ngClass]="{'bg-green-700 hover:bg-green-800' : profileForm.valid,'bg-gray-400': !profileForm.valid}"
                class="text-white font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center">{{'BUTTON.Save'|translate}}</button>
        </div>
    </form>
</div>