<app-auth-layout>
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <p class="text-headline-lg-bold">{{'LOGIN.TitleSign' | translate }}</p>
      <p class="mt-2 text-text-sm-regular text-text-secondary">
        {{'LOGIN.Or' | translate }}
        <a routerLink="/sign-up" class="font-semibold text-text-brand-primary">
          {{'LOGIN.TitleNewAccount' | translate }}
        </a>
      </p>
    </div>

    <form (ngSubmit)="onSubmit()" [formGroup]="loginForm" class="mt-10">
      <div class="flex flex-col gap-[16px]">
        <div class="w-full">
          <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
            {{'LOGIN.EmailAddress' | translate }}
          </p>
          <div class="relative">
            <i
              class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">email</i>
            <app-inno-form-input
              type="email"
              [placeholder]="'LOGIN.PlaceholderEmail' | translate"
              inputClassName="!h-[46px] pl-[40px]"
              [formControl]="f['email']"
              [value]="f['email'].value"
              [errorMessages]="{
                  required: 'LOGIN.EmailRequired' | translate,
                  email: 'LOGIN.InvalidRequired' | translate
                }" />
          </div>
        </div>

        <div class="w-full">
          <div
            class="flex justify-between items-center gap-[5px] flex-wrap !mb-[5px]">
            <p class="text-text-secondary text-text-sm-semibold">
              {{'LOGIN.Password' | translate }}
            </p>
            <a routerLink="/forgot-password"
              class="text-text-brand-primary text-text-sm-semibold">
              {{'LOGIN.ForgotPassword' | translate }}
            </a>
          </div>
          <div class="relative">
            <i
              class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">lock</i>
            <app-inno-form-input
              [type]="isShowPassword ? 'text' : 'password'"
              [placeholder]="'LOGIN.PlaceholderPassword' | translate"
              inputClassName="!h-[46px] px-[40px]"
              [formControl]="f['password']"
              [value]="f['password'].value"
              [errorMessages]="{
                required: 'LOGIN.PasswordRequired' | translate
              }" />
            <i (click)="handleToggleShowPassword()"
              class="material-icons absolute top-[12px] right-[10px] z-10 text-text-tertiary !text-[22px] cursor-pointer">
              {{ isShowPassword ? 'visibility_off' : 'visibility' }}
            </i>
          </div>
        </div>
      </div>

      <button type="submit"
        class="button-size-md button-primary w-full justify-center mt-[34px]">
        {{'LOGIN.Signin' | translate }}
      </button>
    </form>

    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-border-primary"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-text-tertiary"> {{'LOGIN.OrContinue' |
            translate }}</span>
        </div>
      </div>
    </div>
    <div class="w-full mt-6">
      <app-login-social></app-login-social>
    </div>
  </div>
</app-auth-layout>
