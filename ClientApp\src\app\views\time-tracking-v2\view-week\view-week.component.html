@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
  <app-inno-spin />
</div>
} @else {
@if(!dataSource?.length) {
<div
  class="w-full py-[24px] text-center text-text-disabled text-headline-sm-bold">
  No Data
</div>
} @else {
<div [ngStyle]="{'padding-bottom': isRuning ? '100px' : '0px'}">

  <ejs-grid
    #grid
    class="customTable hideHeaderTable"
    [dataSource]="dataSource">
    <e-columns>
      <e-column width="400">
        <ng-template #template let-data>
          @if(data.index !== '0') {
          <div class="w-full flex flex-col gap-[4px]">
            <div class="w-full text-text-primary text-text-md-semibold">
              {{ data.client?.clientName ?? '' }}
            </div>
            <!-- <p class="text-text-tertiary text-text-sm-regular">
                  Advanced user experience platform development
                </p> -->
            <!-- <app-inno-tags value="IOS platform, Web platform, Only InnoBook" /> -->
          </div>
          }
        </ng-template>
      </e-column>
      <e-column width="700">
        <ng-template #template let-data>
          @if(data.index === '0') {
          <div class="weekLayout">
            @for(dayName of dayNames; track dayName) {
            <div class="w-full">
              <p class="text-text-primary text-headline-md-bold text-center">
                {{ data[dayName]?.shortDate ?? '' }}
              </p>
              <p
                class="mt-[8px] text-text-primary text-text-sm-bold text-center uppercase">
                {{ data[dayName]?.shortName ?? '' }}
              </p>
              <p
                class="mt-[2px] text-text-tertiary text-text-sm-bold text-center">
                {{ showHours(data[dayName]?.totalHoursDay) }}
              </p>
            </div>
            }
          </div>
          } @else {
          <div class="weekLayout">
            @for(dayName of dayNames; track dayName) {
            <p class="text-text-secondary text-text-md-regular text-center">
              {{ showHours(data?.listHoursDay?.[dayName]?.hours) }}
            </p>
            }
            <p class="text-text-primary text-text-md-bold text-center">
              {{ showHours(data?.totalHoursWeek) }}
            </p>
          </div>
          }
        </ng-template>
      </e-column>

      <e-column headerText width="100">
        <ng-template #template let-data>
          @if(data.index !== '0') {
          <div class="flex items-center">
            <button class="button-icon" (click)="handleEdit()">
              <img class="w-[20px]" src="../../../assets/img/icon/ic_edit.svg"
                alt="Icon">
            </button>
            <app-inno-popover [content]="contentPopover">
              <button target class="button-icon">
                <img class="w-[20px]"
                  src="../../../assets/img/icon/ic_three_dots_verticel.svg"
                  alt="Icon">
              </button>
            </app-inno-popover>
            <ng-template #contentPopover>
              <div class="flex w-[78px] flex-col">
                <button
                  class="w-full h-[32px] text-text-sm-regular text-text-danger hover:bg-bg-secondary"
                  (click)="handleDelete()">
                  Delete
                </button>
              </div>
            </ng-template>
          </div>
          }
        </ng-template>
      </e-column>
    </e-columns>
  </ejs-grid>
  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>

}
}
