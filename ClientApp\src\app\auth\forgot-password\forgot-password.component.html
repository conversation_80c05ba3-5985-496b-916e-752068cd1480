<app-auth-layout>
  <div class="max-w-md w-full space-y-8">
    <div class="text-center">
      <p class="text-headline-lg-bold">{{'FORGOTPASSWORD.Title'|translate}}</p>
      <p class="mt-2 text-text-sm-regular text-text-secondary">
        {{'FORGOTPASSWORD.Instruction'|translate}}
      </p>
    </div>

    <form class="mt-10" (ngSubmit)="onSubmit()" [formGroup]="resetPasswordForm">
      <div class="w-full">
        <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
          {{'FORGOTPASSWORD.EmailLabel'|translate}}
        </p>
        <div class="relative">
          <i
            class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">email</i>
          <app-inno-form-input
            type="email"
            [placeholder]="'FORGOTPASSWORD.EmailPlaceholder'|translate"
            inputClassName="!h-[46px] pl-[40px]"
            [formControl]="f['email']"
            [value]="f['email'].value"
            [errorMessages]="{
              required: 'FORGOTPASSWORD.EmailRequired'|translate,
              email: 'FORGOTPASSWORD.EmailInvalid'|translate
            }" />
        </div>
      </div>

      <button type="submit"
        class="button-size-md button-primary w-full justify-center mt-[34px]">
        {{'FORGOTPASSWORD.ResetButton'|translate}}
      </button>

      <div class="text-center mt-6">
        <a routerLink="/sign-in"
          class="text-text-sm-regular font-medium text-text-brand-primary">
          {{'FORGOTPASSWORD.BackToSignIn'|translate}}
        </a>
      </div>
    </form>
  </div>
</app-auth-layout>
