import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { SharedModule } from 'app/module/shared.module';

@Component({
  selector: 'app-input-tax',
  standalone: true,
  imports: [
    SharedModule,
    InnoFormInputComponent,
    InnoFormCheckboxComponent
  ],
  templateUrl: './input-tax.component.html',
  styleUrl: './input-tax.component.scss'
})
export class InputTaxComponent implements OnInit {
  @Input() tax: any
  @Input() isHideDeleteButton?: boolean = false

  @Output() onSelectedTax = new EventEmitter<any>();
  @Output() onChange = new EventEmitter<any>();
  @Output() onDelete = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void { }


  handleChangeTax(key: string, value: any) {
    if (!Object.keys(this.tax)?.length) return
    if (key == "amount") {
      //  Remove leading zeros
      if (/^0\d+/.test(value) && !/^0\.\d+$/.test(value)) {
        this.tax[key] = value.replace(/^0+/, '');
      }
      else {
        this.tax[key] = value
      }
    }
    else {
      this.tax[key] = value

    }

    if (key === 'selected') {
      this.onSelectedTax.emit(value)
    }
  }

  handleDelete() {
    this.onDelete?.emit(this.tax)
  }
}
