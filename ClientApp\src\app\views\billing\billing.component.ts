import { Component, OnInit, inject, DestroyRef } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { TranslateService } from '@ngx-translate/core';
import { StripeService } from 'app/service/stripe.service';
import { PlanService } from 'app/service/plan.service';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SpinnerService } from 'app/service/spinner.service';
import { ToastService } from 'app/service/toast.service';

@Component({
  selector: 'app-billing',
  standalone: true,
  imports: [
    SharedModule,
    InnoTabsComponent,
    InnoSpinomponent,
    FormatNumberPipe,
    DecimalPipe
  ],
  templateUrl: './billing.component.html',
  styleUrl: './billing.component.scss'
})
export class BillingComponent implements OnInit {

  public TYPE_TAB = {
    YOUR_PLAN: 1,
    RECEIPTS: 2
  }

  public tabs = [
    { label: "Your Plan", value: this.TYPE_TAB.YOUR_PLAN },
    { label: "Receipts", value: this.TYPE_TAB.RECEIPTS }
  ];

  public currentTab = this.TYPE_TAB.YOUR_PLAN;
  public isLoading = false;

  // Plan details
  public planName = "Plus Plan";
  public planAmount = 0;
  public yearlyAmount = 0;
  public addOnsAmount = 0;
  public totalAmount = 0;
  public planType = 0;
  public renewType = 1;

  // Team members
  public teamMembersCount = 2;

  // Plan limits
  public clientProfilesUsed = 0;
  public clientProfilesTotal = 0;
  public teamMembersUsed = 0;
  public teamMembersTotal = 1;

  // Payment details
  public nextStatementDate = "28/02/2025";
  public cardLastFour = "7013";
  public cardHolderName = "frederic coutu";
  public cardExpiryDate = "7/2026";

  // Services
  private stripeService = inject(StripeService);
  private planService = inject(PlanService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  private spinnerService = inject(SpinnerService);
  private toastService = inject(ToastService);

  constructor(
    private translate: TranslateService
  ) {}

  ngOnInit(): void {
    // Check for Stripe payment session status from URL parameters
    this.route.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
      if (params['session_id'] && params['status'] === 'success') {
        this.verifyStripePayment(params['session_id']);
      } else if (params['status'] === 'cancelled') {
        this.toastService.showError('Payment Cancelled', 'Your payment was cancelled.');
      }
    });

    this.planService.getCurrentPlan()
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (plan: any) => {
          // Handle free plan
          this.planName = plan.plan.name;
          this.clientProfilesTotal = plan.plan.maxClients;
          this.planType = plan.plan.planType;
          
          if (!plan.subscriptionId) {
            this.planAmount = 0;
            this.addOnsAmount = 0;
            this.clientProfilesUsed = 0;
            this.totalAmount = 0;
          } else {
            this.renewType = plan.renewType;
            this.planAmount = this.renewType == 1 ? plan.plan.monthlyPrice : plan.plan.yearlyPrice / 12;
            this.clientProfilesUsed = 0;
            this.addOnsAmount = plan.plan.additionalUserPrice * plan.additionalUsers;
            this.totalAmount = this.planAmount + this.addOnsAmount;
            this.yearlyAmount = plan.totalPrice;
          }
          
          // Update payment details
          this.nextStatementDate = this.calculateNextStatementDate();
        },
        error: (error) => {
          console.error('Error fetching current plan:', error);
        }
      });

  }

  handleChangeTab(value: any) {
    this.currentTab = value;
  }

  managePlan() {
    // Navigate to plan selection page
    this.router.navigate(['billing/plan-selection']);
  }

  updatePaymentMethod() {
    // this.initiatePaymentWithStripe();
  }

  referFriend() {
    console.log('Refer a friend clicked');
  }

  switchToYearly() {
    // Navigate to plan selection page
    this.router.navigate(['billing/plan-selection']);
  }

  upgradeToPremium() {
    // Navigate to plan selection page
    this.router.navigate(['billing/plan-selection']);
  }

  cancelAccount() {
    // Implement cancel account functionality
    console.log('Cancel account clicked');
  }

  addTeamMember() {
    // Implement add team member functionality
    console.log('Add team member clicked');
    // Navigate to team member invitation page or open a modal
  }


  private verifyStripePayment(sessionId: string) {
    this.spinnerService.show();

    this.stripeService.verifyPayment(sessionId)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: (response: any) => {
          this.spinnerService.hide();

          if (response && response.status === 'complete') {
            this.toastService.showSuccess('Payment Successful', 'Your payment was processed successfully.');
            // Remove sessionId from URL without reloading the page
            this.router.navigate([], {
              relativeTo: this.route,
              queryParams: { session_id: null, status: null },
              queryParamsHandling: 'merge'
            });
          } else {
            this.toastService.showError('Payment Failed', 'Your payment could not be processed.');
          }
        },
        error: (error) => {
          this.spinnerService.hide();
          this.toastService.showError('Error', 'Failed to verify payment: ' + error.message);
        }
      });
  }

  private calculateNextStatementDate(): string {
    const date = new Date();
    if (this.renewType == 1) {
      date.setMonth(date.getMonth() + 1);
    } else {
      date.setFullYear(date.getFullYear() + 1);
    }
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
  }
}
