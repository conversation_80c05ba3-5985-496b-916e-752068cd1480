import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddClientsDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/add-clients-form/add-clients-form.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddClientsFormComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        maxWidth: '600px',
        width: '100%',
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
