p {
    margin-bottom: 0;
}

.btnShowHide {
    top: 30px;
    background-color: transparent;
}


.showTogglePassword input {
    padding-right: 40px;
}

.isRequired::after {
    content: " *";
    @apply text-text-danger;
}

.selected {
    @apply bg-bg-brand-primary
}

.selected .txtTitle {
    @apply text-text-sm-semibold;
}

.selected .txtTitle,
.selected .txtDescription {
    @apply text-text-brand-primary
}