import { StoreService } from 'app/service/store.service';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ToastService } from 'app/service/toast.service';
import { InvoiceService } from '../../../../service/invoice.service';
import { Component, DestroyRef, EventEmitter, inject, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { ActionEventArgs, GridAllModule, GridComponent, Pager, PagerDropDown, PagerModule, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { GetInvoiceQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { skip, Subscription } from 'rxjs';
import { AvatarModule } from 'ngx-avatars';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
Pager.Inject(PagerDropDown);

@Component({
  selector: 'app-invoice-send-to-me',
  standalone: true,
  imports: [
    SharedModule,
    InnoStatusComponent,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InnoPopoverComponent,
    InnoEmptyDataComponent,
    InnoTableActionComponent,
    RouterModule,
    AvatarModule,
    GridAllModule,
    PagerModule,
    InnoSpinomponent,
    DecimalPipe
  ],
  providers: [LayoutUtilsService],
  templateUrl: './invoice-send-to-me.component.html',
  styleUrl: './invoice-send-to-me.component.scss'
})
export class InvoiceSendToMeComponent implements OnInit, OnDestroy {
  public isLoading = false;
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @Output() reloadCalculation = new EventEmitter<boolean>();
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public search = '';

  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  private layoutUtilsService = inject(LayoutUtilsService)
  private destroyRef = inject(DestroyRef);
  private _toastService = inject(ToastService)
  private translate = inject(TranslateService);
  private _invoiceService = inject(InvoiceService)
  private dataService = inject(DataService)
  public _storeService = inject(StoreService)
  private _subscriptions: Subscription[] = [];

  ngOnInit(): void {
    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().pipe(skip(1)).subscribe((data) => {
        if (data?.typeView !== InvoiceViewEnum.Created_Tab) return;
        const textSearch = data?.textSearch ?? ''
        this.search = textSearch;
        this.GetInvoiceSendToMe();
      }
      )
    )
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      this.currentPage = queryParams?.page ?? 1;

      this.GetInvoiceSendToMe();
    });
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize;
      this.sort ? this.GetInvoiceSendToMe() : this.GetInvoiceSendToMe();
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }
  }

  ReloadData() {
    this.GetInvoiceSendToMe();
  }

  GetInvoiceSendToMe() {
    const query: GetInvoiceQueryParam = {
      Page: this.currentPage ?? 1,
      Search: this.search ?? '',
      PageSize: this.pageSizesDefault,
      ...this.sort
    }
    this.isLoading = true
    this._invoiceService.GetAllInvoiceSendToMe(query).subscribe({
      next: (res: any) => {
        if (res) {
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };

          }
        }
      },
      complete: () => {
        this.isLoading = false
      }
    })
  }

  handleEdit(item: any) {
    this.router.navigate(['/invoices', item.id])
  }

  handleDelete(item: any) {
    const _title = this.translate.instant('Delete Invoice !');
    const _description = this.translate.instant('Do you want to delete?');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([item.id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.reloadCalculation.emit(true)
          this.GetInvoiceSendToMe();
          this._toastService.showSuccess("Delete", "Success");
        }
        else {
          this._toastService.showError("Fail", "Fail");
        }
      })
    })
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName;
      this.direction = args.direction;

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetInvoiceSendToMe();
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null;
      this.GetInvoiceSendToMe();
    }

  }
  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
