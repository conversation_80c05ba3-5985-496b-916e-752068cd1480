p {
  margin-bottom: 0;
}

.gridLayout {
  display: grid;
  grid-template-columns: minmax(360px, 2fr) minmax(180px, 1fr) minmax(130px, 1fr) minmax(130px, 1fr) minmax(120px, 1fr) 100px;
}

.gridLayoutWrapper>.gridLayout {
  align-items: flex-start;
}

.gridLayoutWrapper>.gridLayout>div {
  height: 100%;
  ;
  border-bottom: 1px solid;
  padding: 12px 5px;
  @apply border-border-primary;
}

.gridLayoutWrapper>.gridLayout:first-child>div {
  border-top: 1px solid;
  @apply border-border-primary
}

.gridLayoutWrapper>.gridLayout>div:first-child {
  padding-left: 24px;
}

.gridLayoutWrapper>.gridLayout>div:last-child {
  padding-right: 24px;
}