<div class="ml-5 mr-5">
    <a class="navigate-link flex items-center text-blue-400 p-2"
        [routerLink]="['/members']"
        routerLinkActive="router-link-active">
        <span class="material-icons">
            arrow_back
        </span>
        <span>{{'TEAMMEMBERS.Title'|translate}}</span>
    </a>
    @if(objectInfor)
    {
    <hr>
    <div class="grid grid-cols-2 gap-4">
        <div>
            <div class="mt-4">
                <span
                    class="text-lg font-bold ">{{'TEAMMEMBERS.Detail.BasicInfo'|translate}}</span>
                <div class="mt-4 bg-bg-primary">
                    <div
                        class=" rounded-md border flex">
                        <div class="p-4 mr-3">
                            @if(objectInfor.user?.firstName &&
                            objectInfor.user?.lastName)
                            {
                            <ngx-avatars [size]="50"
                                [name]="objectInfor.user.firstName.slice(0,1)"></ngx-avatars>
                            }
                            @else{
                            <ngx-avatars [size]="50"
                                [name]="objectInfor.user.email.slice(0,1)"></ngx-avatars>
                            }

                        </div>
                        <div class=" w-full">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-bold w-full">
                                    {{objectInfor.user?.firstName}}
                                    {{objectInfor.user?.lastName}}</span>
                                <div
                                    class=" w-full p-4 flex justify-end items-center cursor-pointer">
                                    <button class="button-icon"
                                        (click)="handleEditMember(objectInfor.user?.id)">
                                        <img class="w-[20px]"
                                            src="../../../assets/img/icon/ic_edit.svg"
                                            alt="Icon">
                                    </button>
                                </div>

                            </div>
                            <hr>

                            <div class="flex items-center pt-3 pb-3">

                                <span class="material-icons">
                                    person
                                </span>
                                <span
                                    class="ml-2">{{objectInfor.user.email}}</span>
                            </div>

                        </div>

                    </div>

                </div>

            </div>
            <div class="mt-4 ">
                <span
                    class="text-lg font-bold  mb-4">{{'TEAMMEMBERS.Detail.RolePermissions'|translate}}</span>
                <div class=" mt-4">
                    <div
                        class=" rounded-md border flex bg-bg-primary">
                        <div class="p-4 mr-3 ">
                            <div class="w-full flex items-center">
                                <span
                                    class="text-gray-400">{{'TEAMMEMBERS.Detail.RoleLabel'|translate}}:</span>
                                <span class="ml-1"> {{objectInfor.role}}</span>
                            </div>

                        </div>
                        <div
                            class=" w-full p-4 flex justify-end items-center text-blue-400 cursor-pointer">
                            <button class="button-icon" (click)="OpenDialog()">
                                <img class="w-[20px]"
                                    src="../../../assets/img/icon/ic_edit.svg"
                                    alt="Icon">
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4">
            <span
                class="text-lg font-bold">{{'TEAMMEMBERS.Detail.SettingsTitle'|translate}}</span>
            <hr>
            <div
                class="rounded-md  w-full cursor-pointer mt-4  bg-bg-primary">
                <div class="p-3 flex justify-between  items-center">
                    <div class="flex flex-col w-full">
                        <span class="flex">
                            <span class="material-icons mr-2">
                                schedule
                            </span>
                            {{'TEAMMEMBERS.Detail.SetBillableRate'|translate}}</span>
                        <span class="text-gray-400">
                            {{'TEAMMEMBERS.Detail.BillableRateDescription'|translate}}</span>
                    </div>
                    <span class="material-icons">
                        chevron_right
                    </span>
                </div>

            </div>
            <div
                class="rounded-md  w-full cursor-pointer mt-4  bg-bg-primary">
                <div class="p-3 flex justify-between  items-center">
                    <div class="flex flex-col w-full">
                        <span class="flex">
                            <span class="material-icons mr-2">
                                pending_actions
                            </span>
                            {{'TEAMMEMBERS.Detail.SetCostRate'|translate}}</span>
                        <span
                            class="text-gray-400">{{'TEAMMEMBERS.Detail.CostRateDescription'|translate}}</span>
                    </div>
                    <span class="material-icons">
                        chevron_right
                    </span>
                </div>

            </div>
            <div
                class="rounded-md  w-full cursor-pointer mt-4  bg-bg-primary">
                <div class="p-3 flex justify-between  items-center">
                    <div class="flex flex-col w-full">
                        <span class="flex">
                            <span class="material-icons mr-2">
                                hourglass_empty
                            </span>
                            {{'TEAMMEMBERS.Detail.SetCapacity'|translate}}</span>
                        <span class="text-gray-400">
                            {{'TEAMMEMBERS.Detail.CapacityUnit'|translate}}</span>
                    </div>
                    <span class="material-icons">
                        chevron_right
                    </span>
                </div>

            </div>
        </div>
    </div>
    }

</div>