import { CategoryService } from './../../../service/category.service';
import { SharedModule } from 'app/module/shared.module';
import { SpinnerService } from 'app/service/spinner.service';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { ToastService } from 'app/service/toast.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Category } from '../../../dto/interface/category.interface';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-detail-category',
  standalone: true,
  imports: [SharedModule, InnobookModalWrapperComponent],
  templateUrl: './detail-category.component.html',
  styleUrl: './detail-category.component.scss'
})
export class DetailCategoryComponent implements OnInit {
  private _spinnerService = inject(SpinnerService)
  destroyRef = inject(DestroyRef);
  private _toastService = inject(ToastService)
  private _categoryService = inject(CategoryService)
  private translate = inject(TranslateService)
  static getComponent(): typeof DetailCategoryComponent {
    return DetailCategoryComponent;
  }

  constructor(public dialogRef: MatDialogRef<DetailCategoryComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {

  }
  public isAddSvcMode = true;
  isEdit: boolean = false
  listCategory: any[] = []
  number: number;
  listCategoryItem: any[] = []
  listItemCategoryRemove: string[] = []
  categoryName!: string;
  categoryNameItem!: string;

  GetCategoryById() {
    this._categoryService.GetCategoryById(this.data).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this.listCategoryItem = res.categoryItems.slice()
        this.categoryName = res.categoryName
        this.number = res.number
      }
    }
    )
  }


  ngOnInit(): void {
    if (this.data) {
      this.GetCategoryById()
    }

  }
  RemoveItem(_id: string, index: number) {
    this.listCategoryItem.splice(index, 1)
    this.listItemCategoryRemove.push(_id)

  }
  RemoveAll() {
    this.listCategory = []
  }
  RemoveService(i: number) {
    this.listCategory.splice(i, 1);
  }
  CreatedCategory() {
    this.isAddSvcMode = false
    this.listCategory.push({
      itemName: this.categoryNameItem,
      categoryId: this.data,
      canEdit: true
    })
    this.categoryNameItem = ''
  }
  addCategory() {
    this.isAddSvcMode = true;
  }
  closeDialog() {
    this.dialogRef.close();
  }
  Edit() {
    this.isEdit = !this.isEdit;
  }
  Save() {
    if (this.listCategory.length > 0) {
      this._spinnerService.show();
      this._categoryService.CreateCategoryItem(this.listCategory).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
          this._spinnerService.hide();
          this.dialogRef.close(res);
        }
      })
    }

    if (this.listItemCategoryRemove.length > 0) {
      this._spinnerService.show();
      this._categoryService.DeleteCategoryItem(this.listItemCategoryRemove).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
          this._spinnerService.hide();
          this.dialogRef.close(res);
        }
      })
    }

    if (this.isEdit && this.categoryName != this.data.categoryName) {
      this._spinnerService.show();
      let payload: Category = {
        categoryName: this.categoryName,
        number: this.number,
        id: this.data
      }
      this._categoryService.UpdateCategory(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this._toastService.showSuccess(this.translate.instant("TOAST.Update"), this.translate.instant("TOAST.Success"));
          this._spinnerService.hide();
          this.dialogRef.close(res);
        }
      })
    }
  }
}
