import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class GenerateInvoiceDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/time-tracking-v2/generate-invoice/generate-invoice.component'
    );

    return this.matDialog.open(
      importedModuleFile.GenerateInvoiceComponent.getComponent(),
      {
        width: '100%',
        maxWidth: '900px',
        panelClass: 'custom_dialog',
        data,
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
