import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class SelectTimeTrackingDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/invoice/dialog/new-invoice/select-time-tracking/select-time-tracking.component'
    );

    return this.matDialog.open(
      importedModuleFile.SelectTimeTrackingComponent.getComponent(),
      {
        width: "100%",
        maxWidth: "1300px",
        data,
        panelClass: 'custom_dialog',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
