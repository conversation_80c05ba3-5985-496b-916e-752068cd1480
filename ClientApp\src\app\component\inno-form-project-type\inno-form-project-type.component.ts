import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  forwardRef,
  OnChanges,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { SharedModule } from '../../module/shared.module';
import { InnoErrorMMessageComponent } from '../inno-error-message/inno-error-message.component';
import { IFilterDropdownOptionProjectType } from 'app/dto/interface/common.interface';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { FormControl, NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
  selector: 'app-inno-form-project-type',
  templateUrl: './inno-form-project-type.component.html',
  styleUrl: './inno-form-project-type.component.scss',
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    InnoErrorMMessageComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormProjectTypeComponent),
      multi: true
    }
  ]
})
export class InnoFormProjectTypeComponent implements ControlValueAccessor, OnChanges {
  @Input() isRequired?: boolean;
  @Input() label?: string = '';
  @Input() options: IFilterDropdownOptionProjectType[] = [];
  @Input() placeholder?: string = '';
  @Input() value?: string = '';
  @Input() projectId?: string = '';
  @Input() errorMessages?: { [key: string]: string };
  @Input() formControl?: FormControl;
  @Input() customOptionTemplate: TemplateRef<any> | null = null

  @Output() onChange = new EventEmitter<string>();
  @Output() onSelect = new EventEmitter<IFilterDropdownOptionProjectType>();
  @Output() onCreateNew = new EventEmitter<any>();

  public textSearch: string = ''
  public clientName: string = ''
  public labelOfValueSelected?: string = ''
  public listOptionPreview: IFilterDropdownOptionProjectType[] = []
  public listOptionOriginal: IFilterDropdownOptionProjectType[] = []

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor() { }

  registerOnChange(fn: (value: string) => void): void { }

  registerOnTouched(fn: () => void): void { }

  setDisabledState(isDisabled: boolean): void { }

  writeValue(value: string): void { }

  ngOnChanges(changes: SimpleChanges) {
    const newListOptions = changes?.['options']?.currentValue
    const newValue = changes?.['value']?.currentValue ?? ''
    if (newListOptions?.length) {
      this.options = newListOptions
      this.listOptionOriginal = this.options
      this.listOptionPreview = this.listOptionOriginal

      if (this.formControl?.value) {
        this.value = this.formControl.value
        this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === this.value)?.name
      }
    }
    if (newValue) {
      this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === newValue)?.name
    }
  }

  get isShowCreateButton() {
    return (
      this.onCreateNew?.observed
      && this.textSearch.length
      && (!this.listOptionOriginal?.length || !this.listOptionPreview?.length)
    )
  }

  handleChange(event: any): void {
    if (this.onChange?.emit) {
      this.onChange.emit(event);
    }
  }

  hasError() {
    return (this.formControl?.invalid && (this.formControl.dirty || this.formControl.touched));
  }

  getErrorMessage(): string {
    if (!this.hasError()) return '';

    if (this.formControl?.errors && this.errorMessages) {
      for (const errorType in this.formControl.errors) {
        if (this.errorMessages[errorType]) {
          return this.errorMessages[errorType];
        }
      }
    }
    return '';
  }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    this.textSearch = textSearch
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal
      .filter(e => e.name.toLowerCase().indexOf(textSearch) > -1)

  }

  public handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  public touchControl() {
    if (!this.formControl) return

    this.formControl.markAsDirty()
    this.formControl.markAsTouched()
  }

  handleChooseOption(item: IFilterDropdownOptionProjectType) {
    if (item.value == this.value) return
    if (this.formControl) this.formControl.setValue(item.value)
    this.labelOfValueSelected = item.name
    this.value = item.value
    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  callbackAfterHideSearchResult() {
    this.listOptionPreview = this.listOptionOriginal
  }

  handleCreateNew() {
    this.onCreateNew.emit(this.textSearch)
    this.handleCloseSearchResult()
  }
}
