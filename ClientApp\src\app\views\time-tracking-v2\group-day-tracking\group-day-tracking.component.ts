import { StoreService } from 'app/service/store.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { InnoTimerProvider } from 'app/component/inno-timer/inno-timer.provider';
import { Component, DestroyRef, inject, Input } from '@angular/core';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoTagsComponent } from 'app/component/inno-tags/inno-tags.component';
import { SharedModule } from 'app/module/shared.module';
import { DataService } from 'app/service/data.service';
import { TranslateService } from '@ngx-translate/core';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { TimetrackingService } from 'app/service/timetracking.service';
import { ToastService } from 'app/service/toast.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { formatTimeHoursFromSeconds } from 'app/helpers/common.helper';
import { AddTimeDialog } from '../../../service/dialog/dialog-add-time.dialog';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-group-day-tracking',
  templateUrl: './group-day-tracking.component.html',
  styleUrls: ['./group-day-tracking.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    AvatarModule,
    MatTooltipModule,
    InnoTableActionComponent,
    InnoStatusComponent,
    InnoTagsComponent
  ]
})
export class GroupDayTrackingComponent {

  @Input() public dayName?: string = '';
  @Input() public dataSource?: any[] = [];
  @Input() public isDisableShowEmpty?: boolean = false;

  private dataService = inject(DataService)
  private translate = inject(TranslateService);
  private layoutUtilsService = inject(LayoutUtilsService)
  private timeTrackingServices = inject(TimetrackingService)
  private toastService = inject(ToastService)
  private destroyRef = inject(DestroyRef);
  private authenticationService = inject(AuthenticationService)
  private innoTimerProvider = inject(InnoTimerProvider)
  public _storeService = inject(StoreService)
  constructor(private addTimeDialog: AddTimeDialog) { }
  handleResume(data: any) {
    if (localStorage.getItem("isRunning") == 'true') {
      this.toastService.showWarning(this.translate.instant("TOAST.Warning"), this.translate.instant("TOAST.DescriptionWarning"))
      return;
    }
    let dt = {
      projectId: data.projectId,
      serviceId: data.serviceId,
      title: data.client?.clientName,
      description: data.description,
      date: data.date,
      workingInfo: {
        label: data.project?.projectName,
        value: data.projectId,
        metadata: {
          type: 'project',
          objectClient: data.client
        }
      },
      serviceInfo: {
        label: data.service?.serviceName,
        value: data.serviceId,
        metadata: data.project
      },
      id: data.id
    }
    this.dataService.SetResume(dt);
    const [hours, minutes, seconds] = data.endTime.split(':').map(Number);
    const totalSecondsTimer = hours * 3600 + minutes * 60 + seconds;
    const payload = {
      isRunning: true,
      timerStartTime: new Date(),
      timer: formatTimeHoursFromSeconds(totalSecondsTimer)
    }
    this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res: any) => {
      },
      complete: () => {
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ timerStatus: 'running', totalSeconds: totalSecondsTimer, billable: data.billable })
        localStorage.setItem("isRunning", 'true')
        this.dataService.SetNewTimeTrackingShowingTimer(true)
      }
    })

  }
  handleEdit(data: any) {
    const item = { new: true, data }

    const dialogRef = this.addTimeDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res: any) => {
        if (res) this.dataService.triggerRefreshListTimeTracking()
      });
    });
  }

  handleDelete(item: any) {
    const _title = this.translate.instant('TIMETRACKING.DeleteTimeTracking');
    const _description = this.translate.instant('COMMON.ConfirmDelete');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return;

      this.timeTrackingServices.DeleteTimeTracking([item.id]).subscribe({
        next: (res) => {
          if (!res) return this.toastService.showError(this.translate.instant('TOAST.Fail'), this.translate.instant('TOAST.Fail'));

          this.toastService.showSuccess(this.translate.instant('TOAST.Delete'), this.translate.instant('TOAST.Success'));
          this.dataService.triggerRefreshListTimeTracking()
        },
        error: () => {
          this.toastService.showError(this.translate.instant('TOAST.Fail'), this.translate.instant('TOAST.Fail'));
        }
      })
    })
  }
}
