import { Component, EventEmitter, Output } from '@angular/core';

@Component({
  selector: 'app-innobook-modal-wrapper',
  standalone: true,
  imports: [],
  templateUrl: './innobook-modal-wrapper.component.html',
  styleUrl: './innobook-modal-wrapper.component.scss'
})
export class InnobookModalWrapperComponent {
  @Output() public onClose = new EventEmitter<void>();
  handleClose(): void {
    this.onClose.emit();
  }
}
