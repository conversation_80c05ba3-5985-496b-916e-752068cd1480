
<app-inno-modal-wrapper [title]="'TIMETRACKING.EditEntry'"
    (onClose)="closeDialog()">
    <div class="p-4">
        <form class=" w-full">
            <div class="flex flex-col gap-2 w-full">
                <div class="flex">
                    <app-inno-select-search-project
                        [templateTrigger]="templateTriggerSelectProject"
                        [isOnlySelectProject]="true"
                        [value]="previewWorkingInfo?.value"
                        (onSelect)="handleSelectProject($event)" />

                    <ng-template #templateTriggerSelectProject>
                        <button
                            class="px-[12px] py-[5px] text-headline-sm-semibold text-text-placeholder-slight cursor-pointer border-2  rounded-md  border-border-primary transition-all"
                            [ngClass]="{'text-text-primary': previewWorkingInfo!! || clientName }">
                            @if(clientName)
                            {
                            {{clientName}} - {{projectName}}
                            }
                            @else{
                            {{previewWorkingInfo?.metadata?.objectClient?.clientName}}
                            -
                            {{ previewWorkingInfo?.label ??
                            'What are you working on?' }}
                            }

                        </button>
                    </ng-template>
                </div>
                <app-inno-select-search-service
                    [templateTrigger]="templateTriggerSelectService"
                    (onSelect)="handleSelectServices($event)"
                    [value]="projectId" />

                <ng-template #templateTriggerSelectService>
                    <button
                        class="px-[12px] py-[5px] text-text-sm-bold text-text-placeholder-slight cursor-pointer border-2 rounded-md border-border-primary transition-all"
                        [ngClass]="{'text-text-primary': servicesName!='' }">
                        {{
                        servicesName==''?('TIMETRACKING.ChooseService' |
                        translate):servicesName
                        }}
                    </button>
                </ng-template>

                @if(isrequiredProject)
                {
                <mat-error
                    class="matError">{{'TIMETRACKING.ProjectRequired'|translate}}</mat-error>
                }

                <div
                    class="bg-white border border-gray-400  w-32  rounded-lg flex items-center mb-2">
                    <span class="fw-semibold mx-2 mt-1">{{ selectedDate | date:
                        _storeService.getdateFormat()}}</span>
                    <ejs-datepicker class="datepicker-single"
                        [(value)]="selectedDate"
                        (valueChange)="onSelectedDateChange($event)"
                        [showClearButton]="false"></ejs-datepicker>
                </div>

            </div>
            <div class="flex gap-2 mb-2">

                <div>
                    <input type="text"
                        [(ngModel)]="timeEnd"
                        (blur)="onBlur($event)"
                        [ngModelOptions]="{standalone: true}"
                        placeholder="HH:MM"
                        class="bg-white border border-gray-400 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 ">
                    @if(isrequiredTime)
                    {
                    <mat-error
                        class="matError">{{'TIMETRACKING.TimeRequired'|translate}}</mat-error>
                    }
                </div>

            </div>
            <textarea class="mt-2" id="message" rows="3"
                [(ngModel)]="description"
                [ngModelOptions]="{standalone: true}"
                class="block p-2.5 w-full text-sm text-gray-900 bg-white rounded-lg border border-gray-400 focus:ring-blue-500 focus:border-blue-500"
                [placeholder]="'TIMETRACKING.Write'|translate"></textarea>
            @if(data.data.isBilled)
            { <div class="mt-2">
                <span
                    class="  text-text-sm-semibold px-[8px] py-[2px] rounded-md text-center ms-2 text-sm font-medium text-text-success bg-bg-secondary-subtle">Billed</span>
            </div>
            }@else{
            <div class="flex items-center text-start mt-2">
                <input checked id="checked-checkbox" type="checkbox" value
                    [(ngModel)]="checked"
                    [ngModelOptions]="{standalone: true}"
                    class="w-6 h-6 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500">
                <label for="checked-checkbox"
                    class="ms-2 text-sm font-medium text-gray-900">{{'TIMETRACKING.Billable'|translate}}</label>
            </div>
            }

            <div class="flex mt-2">
                <button class="button-primary button-size-md" type="button"
                    (click)="Update()">
                    {{'BUTTON.Update'|translate}}
                </button>

                <button class="button-outline button-size-md ml-3" type="button"
                    (click)="cancel()">
                    {{'BUTTON.Cancel'|translate}}
                </button>
            </div>
        </form>

    </div>