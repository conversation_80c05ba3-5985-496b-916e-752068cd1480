import { MemberService } from 'app/service/member.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';

import { GraphicsChartComponent } from './graphics-chart/graphics-chart.component';
import { RevenueExpensesChartComponent } from './revenue-expenses-chart/revenue-expenses-chart.component';
import { TranslateModule } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    TranslateModule,
    GraphicsChartComponent,
    RevenueExpensesChartComponent
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss'
})
export class DashboardComponent implements OnInit {
  CountMember: Number = 0

  private memberService = inject(MemberService)
  private destroyRef = inject(DestroyRef);
  constructor() {
  }
  CountMemberBusiness() {
    this.memberService.CountMemberBusiness().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this.CountMember = res;
      }
    }
    )
  }
  ngOnInit(): void {
    this.CountMemberBusiness();
  }
}
