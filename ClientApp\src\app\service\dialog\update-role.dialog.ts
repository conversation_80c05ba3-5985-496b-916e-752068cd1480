import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class UpdateRoleDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/members/dialog-update-role/dialog-update-role.component'
    );

    return this.matDialog.open(
      importedModuleFile.DialogUpdateRoleComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
