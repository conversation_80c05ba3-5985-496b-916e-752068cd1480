import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class SendInvoiceDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/invoice/dialog/new-invoice/send-invoice/send-invoice.component'
    );

    return this.matDialog.open(
      importedModuleFile.SendInvoiceComponent.getComponent(),
      {
      width: "480px",
      data,
      panelClass: 'custom_dialog',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
