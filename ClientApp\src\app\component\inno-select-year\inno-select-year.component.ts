import { DropDownListModule } from '@syncfusion/ej2-angular-dropdowns';
import { Component, EventEmitter, Output } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';

@Component({
  selector: 'app-inno-select-year',
  standalone: true,
  imports: [SharedModule, DropDownListModule
  ],
  templateUrl: './inno-select-year.component.html',
  styleUrl: './inno-select-year.component.scss'
})
export class InnoSelectYearComponent {
  selectedYear: number | null = null;
  yearsList: any[] = [];
  @Output() EmitSelectYear: EventEmitter<number> = new EventEmitter<number>();
  constructor() {
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 10;
    const endYear = currentYear + 10;
    this.yearsList = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i).reverse();
    this.selectedYear = currentYear;
  }
  handleSelectYear(item) {
    this.EmitSelectYear.emit(item.value)

  }

}
