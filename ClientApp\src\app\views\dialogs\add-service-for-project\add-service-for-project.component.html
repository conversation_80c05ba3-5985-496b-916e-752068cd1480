
<app-inno-modal-wrapper [title]="'SERVICEFORPROJECT.Title'"
    (onClose)="handleCancel()">

    <div class="w-full p-[16px]">
        @if(isFetching) {
        <div class="w-full py-2 flex justify-center items-center">
            <app-inno-spin />
        </div>
        }
        @else{

        @if(listService.length==0) {
        <div class="w-full">
            <app-inno-empty-data
                [title]="'EMPTY.NoResult'" />
        </div>
        } @else {
        <div
            class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">

        </div>
        <div class="overflow-auto w-full">
            <div class="selectProjectTableLayout">
                <div class="addBorderBottom w-full flex gap-[8px]">
                    <div class="w-[16px] shrink-0">
                        <app-inno-form-checkbox
                            [checked]="listIndexServiceSelected.length === listService.length"
                            (onChange)="handleCheckedAll($event)" />
                    </div>
                    <p (click)="sortName('serviceName')"
                        [ngClass]="{'font-bold text-black': sortColumn === 'serviceName'}"
                        class="text-text-tertiary text-text-sm-semibold text-right cursor-pointer">
                        {{'SERVICEFORPROJECT.ServiceName'|translate}}
                        @if(sortColumn=='serviceName')
                        {
                        <span class="material-icons pl-1 !text-[15px]">
                            {{ sortDirection === 'Ascending' ?
                            'arrow_upward' :
                            'arrow_downward'
                            }}
                        </span>
                        }
                    </p>
                </div>
                <p
                    class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                    {{'SERVICEFORPROJECT.Rate'|translate}}
                </p>
                <p
                    class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                    {{'SERVICEFORPROJECT.Description'|translate}}
                </p>
                <p
                    class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                    {{'SERVICEFORPROJECT.Taxes'|translate}}
                </p>
                <p
                    class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                    {{'SERVICEFORPROJECT.Date'|translate}}
                </p>

            </div>
            @for(item of listService; track item.id; let
            i =
            $index) {
            <div class="selectProjectTableLayout">
                <div class="addBorderBottom w-full flex gap-[8px]">
                    <div class="w-[16px] shrink-0">
                        <app-inno-form-checkbox
                            [checked]="isCheckedIndex(i)"
                            (onChange)="handleToggleCheckedIndex(i)" />
                    </div>
                    <p class="text-text-primary text-text-sm-regular text-wrap">
                        {{ item.serviceName ?? '' }}
                    </p>
                </div>
                <p
                    class="addBorderBottom text-text-primary text-text-sm-regular">
                    {{
                    item.rate
                    ??
                    '0' }}
                </p>
                <p
                    class="addBorderBottom text-text-primary text-text-sm-regular text-center text-wrap">
                    {{ item.description ?? '' }}
                </p>
                <p
                    class="addBorderBottom text-text-primary text-text-sm-regular text-center text-wrap">
                    {{ getNameSelectedTaxes(item?.taxes) }}
                </p>
                <p
                    class="addBorderBottom text-text-primary text-text-sm-regular text-center">
                    {{ item.createdAt | date: _storeService.getdateFormat() }}
                </p>
            </div>
            }
            <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
                [totalRecordsCount]='totalPages'
                [currentPage]="currentPage"
                [pageSizes]="pageSizes" (click)="onPageChange($event)">
            </ejs-pager>
        </div>

        }
        }
    </div>
    <div footer>
        <app-inno-modal-footer
            [customSubmitButton]="customSubmitNewInvoice"
            [isDisableSubmit]="!listIndexServiceSelected.length"
            (onCancel)="handleCancel()" />
        <ng-template #customSubmitNewInvoice>
            <div class="flex items-center gap-[12px]">
                <button (click)="handleSubmit()"
                    class="button-outline-primary button-size-md">
                    {{'BUTTON.Save'|translate}}
                </button>
            </div>
            <div
                class="flex flex-wrap justify-center items-center lg:justify-between lg:mt-0 lg:flex-nowrap">
                <button class="button-size-md button-primary min-w-fit"
                    (click)="handleCreateNewService()">
                    <img src="../../../assets/img/icon/ic_add_white.svg"
                        alt="icon">
                    {{'SERVICEFORPROJECT.Buttons.CreateNewService'|translate}}
                </button>

            </div>
        </ng-template>
    </div>
</app-inno-modal-wrapper>