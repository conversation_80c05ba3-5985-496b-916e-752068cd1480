import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddTimeDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/time-tracking-v2/dialog-add-time/dialog-add-time.component'
    );

    return this.matDialog.open(
      importedModuleFile.DialogAddTimeComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        width: '550px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
