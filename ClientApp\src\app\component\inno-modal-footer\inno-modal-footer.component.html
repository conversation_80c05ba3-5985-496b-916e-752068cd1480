<div
  class="w-full p-[16px] border-t border-border-primary flex items-center gap-[12px]">
  @if(leftAction) {
  <ng-container [ngTemplateOutlet]="leftAction"></ng-container>
  }

  <div class="ml-auto flex gap-[12px] items-center">
    @if(customCancelButton) {
    <ng-container [ngTemplateOutlet]="customCancelButton"></ng-container>
    } @else if (onCancel.observers.length) {
    <button class="button-outline button-size-md"
      [class]="classNameCancelButton" type="button"
      (click)="handleCancel()">
      {{ textCancel || 'BUTTON.Cancel' | translate }}
    </button>
    }

    @if(customSubmitButton) {
    <ng-container [ngTemplateOutlet]="customSubmitButton"></ng-container>
    } @else if(onSubmit.observers.length) {
    <button class="button-primary button-size-md"
      [class]="classNameSubmitButton" [disabled]="isDisableSubmit"
      type="button" (click)="handleSubmit()">
      {{ textSubmit || 'BUTTON.Save' | translate }}
    </button>
    }
  </div>
</div>
