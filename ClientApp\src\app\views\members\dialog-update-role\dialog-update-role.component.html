<app-innobook-modal-wrapper (onClose)="closeDialog()">

    <div class="grid grid-cols-2 gap-4">
        <div>
            <div>
                <h3 class="text-center">Update Role</h3>
                <ul
                    class="p-3 space-y-1 text-sm text-gray-700 dark:text-gray-200">
                    @for(item of RoleMember; track item)
                    {
                    <li (click)="SelectRole(item.value)">
                        <div
                            class="flex p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-600 text-start cursor-pointer">
                            <div class="flex items-center h-5">
                                <input 
                                    [(ngModel)]="selected"
                                    type="radio" [value]="item.value"
                                    class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                            </div>
                            <div class="ms-2 text-sm cursor-pointer">
                                <label 
                                    class="font-medium text-gray-900 dark:text-gray-300">
                                    <div>{{item.name}}</div>
                                    <p id="helper-radio-text-4"
                                        class="text-xs font-normal text-gray-500">{{item.depression}}</p>
                                </label>
                            </div>
                        </div>
                    </li>
                    }

                </ul>
            </div>

        </div>
        <div class="flex justify-center">
            <div class="border p-3 w-80">
                <h5 class="text-center">{{selected}}</h5>

                <div class="flex flex-col mt-4">
                    @for(item of ChossedMenu;track item; let vi=$index)
                    {
                    <span class="text-start">{{item.depression}}</span>
                    @for(it of item?.role;track it; let i=$index)
                    {

                    <div class="flex items-center mt-2">
                        @if(it.check)
                        {
                        <span class="material-icons text-green-500">
                            done
                        </span>
                        }
                        @else
                        {
                        <span class="material-icons text-red-500">
                            not_interested
                        </span>
                        }

                        <span class="pl-3">{{it.text}}</span>
                    </div>
                    }
                    }

                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <button (click)="closeDialog()" type="button"
            class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Cancel</button>
        <button
            type="button"
            (click)="Submit()"
            class="  bg-green-600 hover:bg-green-700focus:outline-none text-white focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Save</button>
    </div>
</app-innobook-modal-wrapper>