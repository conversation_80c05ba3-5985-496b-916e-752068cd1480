import { AuthenticationService } from 'app/auth/service/authentication.service';
import { StoreService } from 'app/service/store.service';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../../../core/services/layout-utils.service';
import { InvoiceService } from './../../../../service/invoice.service';
import { Component, DestroyRef, EventEmitter, HostListener, inject, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { ActionEventArgs, GridAllModule, GridComponent, Pager, PagerDropDown, PagerModule, RowDragEventArgs, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { skip, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { AvatarModule } from 'ngx-avatars';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { Role } from 'app/enum/role.enum';
Pager.Inject(PagerDropDown);

@Component({
  selector: 'app-invoice-management',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    InnoStatusComponent,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InnoPopoverComponent,
    InnoEmptyDataComponent,
    InnoTableActionComponent,
    RouterModule,
    AvatarModule,
    GridAllModule,
    PagerModule,
    InnoSpinomponent,
    FormatNumberPipe,
    DecimalPipe
  ],
  templateUrl: './invoice-management.component.html',
  styleUrl: './invoice-management.component.scss'
})
export class InvoiceManagementComponent implements OnInit, OnDestroy {
  public isLoading = false;
  private isDragging = false;
  private isMobile = false;
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10;
  search: string = "";
  public sort: SortGird;
  public sortOptions: SortSettingsModel = { columns: [] };
  @Output() reloadCalculation = new EventEmitter<boolean>();
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string;
  public direction: any;
  public Role = Role;

  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  private activatedRoute = inject(ActivatedRoute);
  private router = inject(Router);
  private destroyRef = inject(DestroyRef);
  private _invoiceService = inject(InvoiceService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private dataService = inject(DataService)
  private _subscriptions: Subscription[] = [];
  public _storeService = inject(StoreService)
  public authenticationService = inject(AuthenticationService)

  ngAfterViewInit() {
    this.isMobile = window.innerWidth <= 768;

    if (this.isMobile) {
      const gridElement = document.querySelector('.e-grid') as HTMLElement;
      if (gridElement) {

        gridElement.addEventListener('touchstart', (event) => {
          this.isDragging = false;
        });

        gridElement.addEventListener('touchmove', (event) => {
          if (this.isDragging) {
            event.preventDefault();
          }
        });

        gridElement.addEventListener('touchend', () => {
          setTimeout(() => {
            this.isDragging = false;
          }, 200);
        });
      }

    }
  }

  onDragStart() {
    this.isDragging = true;
  }


  ngOnInit(): void {

    // handle search and filter
    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().pipe(skip(1)).subscribe((data) => {
        if (data?.typeView !== InvoiceViewEnum.Created_Tab) return;
        const page = this.activatedRoute.snapshot.queryParams['page'] || 1;
        const textSearch = data?.textSearch ?? ''
        this.search = textSearch;
        this.GetAllInvoice();
        this.currentPage = page
      }
      )
    )

    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      this.currentPage = queryParams?.page ?? 1;
      this.GetAllInvoice();
    });

  }
  handleDelete(item) {
    const _title = this.translate.instant('Delete Invoice !');
    const _description = this.translate.instant('Do you want to delete?');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([item.id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.reloadCalculation.emit(true);
          this.GetAllInvoice();
          this._toastService.showSuccess("Delete", "Success");
        }
        else {
          this._toastService.showError("Fail", "Fail");
        }
      })
    })
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize;
      this.GetAllInvoice()

    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }
  }

  ReloadData() {
    this.GetAllInvoice();
  }

  GetAllInvoice() {
    const payload: Parameter = {
      Page: this.currentPage ?? 1,
      Search: this.search ?? '',
      PageSize: this.pageSizesDefault,
      ...this.sort
    }
    this.isLoading = true
    this._invoiceService.GetAllInvoice(payload).subscribe({
      next: (res) => {
        if (res) {
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };
          }

        }
      },
      complete: () => {
        this.isLoading = false
      }
    })
  }

  onDragStop(args: RowDragEventArgs): void {
    let payload = {
      fromInvoiceId: this.dataSource[args.fromIndex].id,
      dropInvoiceId: this.dataSource[args.dropIndex].id,
      fromIndex: this.dataSource[args.fromIndex].position,
      dropIndex: this.dataSource[args.dropIndex].position
    }
    this.isDragging = false
    this._invoiceService.ChangePosition(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
  }

  handleEdit(item: any) {
    this.router.navigate(['/invoices', item.id])
  }

  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetAllInvoice()
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllInvoice()
    }

  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());

  }
}
