import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddProjectDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/add-project-form/add-project-form.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddProjectFormComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        maxWidth: "700px",
        width: '100%',
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
