import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class SendEstimateDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/estimate/dialogs/send-estimate/send-estimate.component'
        );

        return this.matDialog.open(
            importedModuleFile.SendEstimateComponent.getComponent(),
            {
                width: "480px",
                data,
                panelClass: 'custom_dialog',
                disableClose: true,
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
