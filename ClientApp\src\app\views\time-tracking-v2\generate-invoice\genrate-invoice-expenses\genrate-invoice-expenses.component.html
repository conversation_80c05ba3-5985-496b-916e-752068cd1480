<div class="overflow-auto w-full">
    <div class="selectProjectTableLayout">
        <div class="addBorderBottom w-full flex gap-[8px]">
            <div class="w-[16px] shrink-0">
                <app-inno-form-checkbox
                    [checked]="listIndexExpensesSelected.length === listExpensesItem.length"
                    (onChange)="handleCheckedAll($event)" />
            </div>
            <p class="text-text-tertiary text-text-sm-semibold">
                {{'GENERATEINVOICE.EXPENSES.ExpensesName'|translate}}
            </p>
        </div>
        <p
            class="addBorderBottom text-text-tertiary text-text-sm-semibold">
            {{'GENERATEINVOICE.EXPENSES.CategoryName'|translate}}
        </p>
        <p
            class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
            {{'GENERATEINVOICE.EXPENSES.ItemName'|translate}}
        </p>
        <p
            class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
            {{'GENERATEINVOICE.EXPENSES.PaidAmount'|translate}}
        </p>
    </div>
    @for(expensesItem of listExpensesItem; track expensesItem; let i =
    $index) {
    <div class="selectProjectTableLayout">
        <div class="addBorderBottom w-full flex gap-[8px]">
            <div class="w-[16px] shrink-0">
                <app-inno-form-checkbox
                    [checked]="isCheckedIndex(i)"
                    (onChange)="handleToggleCheckedIndex(i)" />
            </div>
            <p class="text-text-primary text-text-sm-regular">
                {{ expensesItem.expensesName ?? '' }}
            </p>
        </div>

        <p class="addBorderBottom text-text-primary text-text-sm-regular">
            {{ expensesItem.categoryName??'' }}
        </p>
        <p
            class="addBorderBottom text-text-primary text-text-sm-regular text-right">
            {{ expensesItem.itemName ?? '' }}
        </p>
        <p
            class="addBorderBottom text-text-primary text-text-sm-regular text-right">
            ${{ expensesItem.paidAmount ?? 0 | decimal:2 | formatNumber }}
        </p>
    </div>
    }
    <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
</div>
<div
    class="w-full flex pt-[15px] pb-[3px] flex-wrap justify-end items-center gap-[16px]">
    <p class="text-text-primary text-text-sm-regular">
        {{'GENERATEINVOICE.AmountDue'|translate}}
        ({{_storeService.curencyCompany | async}})
    </p>
    <p class="text-headline-sm-bold text-text-primary">
        ${{ totalAmount | decimal:2 | formatNumber }}
    </p>
</div>
<app-inno-modal-footer
    (onCancel)="handleCancel()"
    (onSubmit)="handleSubmit()" />