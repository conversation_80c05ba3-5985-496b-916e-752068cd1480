import { Role } from "app/enum/role.enum";

export interface Action {
    label: string,
    icon: string;
    permissions?: string[],
    action: string,

}
export const MenuActions: Action[] = [
    {
        label: "MENUACTION.MarkAsPaid",
        icon: "../../../../assets/img/icon/ic_cash_circle.svg",
        permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
        action: "paid"
    },
    {
        label: "MENUACTION.MarkAsSent",
        icon: "../../../../assets/img/icon/ic_arrow_right.svg",
        permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
        action: "sent"
    },
    {
        label: "MENUACTION.Duplicate",
        icon: "../../../../assets/img/icon/ic_duplicate.svg",
        permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
        action: "duplicate"
    },
    {
        label: "MENUACTION.DownloadPDF",
        icon: "../../../../assets/img/icon/ic_download.svg",
        permissions: [Role.Admin, Role.Accountant, Role.Manager, Role.Employee, Role.Contractor],
        action: "download"
    },
    {
        label: "MENUACTION.Print",
        icon: "../../../../assets/img/icon/ic_print.svg",
        permissions: [Role.Admin, Role.Accountant, Role.Manager, Role.Employee, Role.Contractor],
        action: "print"
    },
    {
        label: "MENUACTION.Archive",
        icon: "../../../../assets/img/icon/ic_archive.svg",
        permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
        action: "archive"
    },
    {
        label: "MENUACTION.Delete",
        icon: "../../../../assets/img/icon/ic_trash.svg",
        permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
        action: "delete"
    }
];
