<div class="flex items-center justify-between mb-4">
    <h4
        class="text-gray-500 font-bold">{{'DASHBOARD.TitleChartRevenue'|translate}}</h4>
    <div>
        <app-inno-select-year
            (EmitSelectYear)="handleSelectYear($event)"></app-inno-select-year>
    </div>
</div>
<!-- Placeholder for Bar Chart -->
<div
    class="  justify-center items-center w-full h-full overflow-auto xl:flex block">
    <ngx-charts-bar-vertical-2d
        [view]="view"
        [scheme]="colorScheme"
        [results]="dataChart"
        [gradient]="gradient"
        [xAxis]="showXAxis"
        [yAxis]="showYAxis"
        [legend]="showLegend"
        [legendPosition]="legendPosition"
        [xAxisLabel]="xAxisLabel"
        [showXAxisLabel]="showXAxisLabel"
        [showYAxisLabel]="showYAxisLabel">
    </ngx-charts-bar-vertical-2d>
</div>
<div class="w-full flex flex-col items-end mt-[16px]">
    <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
            {{'DASHBOARD.TotalAmount'|translate}}
        </p>
        <p
            class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
            ${{totalAmount | decimal:2 | formatNumber}}
        </p>
    </div>
    <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
            {{'DASHBOARD.TotalAmountPaid'|translate}}
        </p>
        <p
            class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
            ${{totalAmountPaid | decimal:2 | formatNumber}}
        </p>
    </div>
</div>