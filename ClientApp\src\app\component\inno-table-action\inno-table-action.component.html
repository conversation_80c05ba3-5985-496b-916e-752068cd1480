<div class="flex gap-2 items-center">
  @if(onResume.observed) {
  <button matTooltip="Resume" class="button-icon" (click)="handleResume()">
    <img class="w-[20px]" src="../../../assets/img/icon/ic_play.svg" alt="Icon">
  </button>
  }
  @if(onEdit.observed) {
  <button class="button-icon" (click)="handleEdit()">
    <img class="w-[20px]" src="../../../assets/img/icon/ic_edit.svg" alt="Icon">
  </button>
  }
  @if(onDowload.observed) {
  <button class="button-icon" (click)="handleDowload()">
    <img class="w-[20px]" src="../../../assets/img/icon/ic_download.svg"
      alt="Icon">
  </button>
  }
  @if(onArchive.observed && onDelete.observed) {
  <app-inno-popover [content]="contentPopover">
    <button target class="button-icon">
      <img class="w-[20px]"
        src="../../../assets/img/icon/ic_three_dots_verticel.svg" alt="Icon">
    </button>
  </app-inno-popover>
  <ng-template #contentPopover>
    <div class="flex w-[78px] flex-col">
      <button class="w-full h-[32px] text-text-sm-regular hover:bg-bg-secondary"
        (click)="handleArchive()">
        {{'COMMON.Archive' |translate}}
      </button>
      <button
        class="w-full h-[32px] text-text-sm-regular text-text-danger hover:bg-bg-secondary"
        (click)="handleDelete()">
        {{'COMMON.Delete' |translate}}
      </button>
    </div>
  </ng-template>
  } @else {
  @if(onArchive.observed) {
  <button class="button-icon" (click)="handleArchive()">
    <img class="w-[20px]" src="../../../assets/img/icon/ic_archive.svg"
      alt="Icon">
  </button>
  }
  @if(onDelete.observed) {
  <button class="button-icon" (click)="handleDelete()">
    <img class="w-[20px]" src="../../../assets/img/icon/ic_trash.svg"
      alt="Icon">
  </button>
  }

  }
</div>
