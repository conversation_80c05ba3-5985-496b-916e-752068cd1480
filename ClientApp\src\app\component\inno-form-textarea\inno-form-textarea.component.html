<div class="w-full flex flex-col relative" [class.error]="hasError()">
  @if(label) {
  <label [class.required]="isRequired"
    class="text-text-secondary text-text-sm-semibold mb-[2px]">{{ label
    }}</label>
  }

  @if (formControl) {
  <textarea
    [placeholder]="placeholder"
    [formControl]="formControl"
    (keyup)="handleChange($event)"
    class="w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder {{ class }}"
    [ngClass]="{'resize-none': !isAbleResize}"></textarea>

  <app-inno-error-message [message]="getErrorMessage()" />
  } @else {
  <textarea
    (keyup)="handleChange($event)"
    [value]="value"
    [placeholder]="placeholder"
    class="w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder {{ class }}"
    [ngClass]="{'resize-none': !isAbleResize }"></textarea>
  }
</div>
