import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddEstimateDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/estimate/dialogs/new-estimate/new-estimate.component'
        );

        return this.matDialog.open(
            importedModuleFile.NewEstimateComponent.getComponent(),
            {
                data,
                width: "80vw",
                maxWidth: "100%",
                maxHeight: "100%",
                panelClass: 'custom_dialog',
                disableClose: true,
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
