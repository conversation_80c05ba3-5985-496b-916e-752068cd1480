import { Tax } from "./tax.interface";

export interface ItemInvoice {
    invoiceId: string;
    rate: number;
    qty: number;
    total: number;
    description: string;
    taxes: Tax[];
    id: string;
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    dateSelectItem?: string;
    updatedBy?: any;
    user?: any
    serviceName?: string
    projectName?: string
    service?: any
    item?: any
    project?: any
    position?: any
}
