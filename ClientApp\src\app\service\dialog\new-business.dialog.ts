import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class NewBusinessDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/switch-workspace/new-business/new-business.component'
    );

    return this.matDialog.open(
      importedModuleFile.NewBusinessComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        width: '500px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
