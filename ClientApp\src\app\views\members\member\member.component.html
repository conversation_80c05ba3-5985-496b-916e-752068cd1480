<!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <p class="text-text-primary text-headline-lg-bold">
      {{'TEAMMEMBERS.Title'|translate}}
    </p>

    <button class="button-size-md button-primary" (click)="OpenDialog()">
      <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
      {{'TEAMMEMBERS.AddButton'|translate}}
    </button>
  </div>
</div>
<!-- End Header page -->

<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
  <div class="w-full max-w-[300px]">
    <app-inno-input-search [value]="search" (onChange)="handleSearch($event)" />
  </div>
</div>
@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
  <app-inno-spin size="lg" />
</div>
}@else {
<div class="w-full mt-[12px]"
  [ngClass]="{ 'mb-28':storeService.getIsRunning() }">
  <ejs-grid
    class="customTable"
    #grid
    (actionBegin)="onActionBegin($event)"
    [allowSorting]="true"
    [sortSettings]='sortOptions'
    [dataSource]="dataSource">
    <e-columns>
      <!-- <e-column type="checkbox" width="30"></e-column> -->
      <e-column [headerText]="'TEAMMEMBERS.GIRD.Name'|translate" width="200"
        field="FirstName">
        <ng-template #template let-data>
          @if(data.user)
          {

          <div class="flex items-center cursor-pointer"
            (click)="RouterDetail(data.user.id)">
            @if(data.user?.firstName && data.user?.lastName)
            {
            <ngx-avatars [size]="30"
              bgColor="{{storeService.getBgColor(data.user.firstName.slice(0,1))}}"
              [name]="GetFullName(data.user)"></ngx-avatars>
            <span class="ml-2"> {{data.user.firstName}}
              {{data.user.lastName}}</span>
            }
            @else{
            <ngx-avatars [size]="30"
              bgColor="{{storeService.getBgColor(data.user.email.slice(0,1))}}"
              [name]="data.user.email.slice(0,1)"></ngx-avatars>
            <span class="ml-2"> {{data.user.email}}</span>
            }
          </div>
          }
        </ng-template>
      </e-column>
      <e-column [headerText]="'TEAMMEMBERS.GIRD.Role'|translate" width="150"
        field="role">
        <ng-template #template let-data>
          <div>
            <span>{{data.role}}</span>
            @if(data.status==2)
            {
            <span (click)="RouterInvite(data)"
              class="p-1 bg-green-300 ml-2 rounded-md cursor-pointer">Invited</span>
            }
          </div>
        </ng-template>
      </e-column>
      <e-column headerText width="100">
        <ng-template #template let-data>
          <app-inno-table-action
            (onEdit)="RouterDetail(data.user.id)"
            (onDelete)="handleDelete(data)" />
        </ng-template>
      </e-column>
    </e-columns>
  </ejs-grid>

  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>
}
