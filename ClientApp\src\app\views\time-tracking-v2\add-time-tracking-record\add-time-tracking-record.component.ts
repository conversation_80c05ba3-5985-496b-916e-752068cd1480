import { AuthenticationService } from 'app/auth/service/authentication.service';
import { DataService } from 'app/service/data.service';
import { ChangeDetectorRef, Component, DestroyRef, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { InnoDatepickerComponent } from 'app/component/inno-datepicker/inno-datepicker.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { InnoTabsV2Component } from 'app/component/inno-tabs-v2/inno-tabs-v2.component';
import { InnoTagsComponent } from 'app/component/inno-tags/inno-tags.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { SharedModule } from 'app/module/shared.module';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoSelectSearchTagsComponent } from 'app/component/inno-select-search-tags/inno-select-search-tags.component';
import { Subscription } from 'rxjs';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { TimeTrackingProvider } from '../time-tracking.provider';
import { InnoEnterHoursComponent } from 'app/component/inno-enter-hours/inno-enter-hours.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { formatTimeHoursFromSeconds } from 'app/helpers/common.helper';
import { InnoTimerProvider } from 'app/component/inno-timer/inno-timer.provider';
import { InnoSelectSearchServiceComponent } from 'app/component/inno-select-search-service/inno-select-search-service.component';
import { InnoEnterEditHoursComponent } from 'app/component/inno-enter-edit-hours/inno-enter-edit-hours.component';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-add-time-tracking-record',
  templateUrl: './add-time-tracking-record.component.html',
  styleUrls: ['./add-time-tracking-record.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoTabsV2Component,
    InnoTagsComponent,
    InnoFormCheckboxComponent,
    InnoDatepickerComponent,
    InnoFormTextareaComponent,
    InnoSelectSearchProjectComponent,
    InnoSelectSearchTagsComponent,
    InnoEnterHoursComponent,
    InnoEnterEditHoursComponent,
    InnoSelectSearchServiceComponent
  ],
  providers: [LayoutUtilsService]
})
export class AddTimeTrackingRecordComponent implements OnInit, OnDestroy {

  public previewWorkingInfo?: IFilterDropdownOption
  public previewServiceInfo?: IFilterDropdownOption
  public projectId: string = ''
  public isEditTimerHours: boolean = false
  public previewDescription?: string = ''
  public previewDate?: Date = new Date()
  public previewBillable?: boolean = true
  public previewTimeEnd?: string = '';
  public timerHoursEdit?: string = '';
  public previewTimerStatus?: 'running' | 'paused' = undefined
  public timerHours: string = '00:00:00';
  public settimeOur: any
  public isInternal: boolean = false
  public currentMode = 1;
  public listMode = []

  private layoutUtilsService = inject(LayoutUtilsService)
  private dataService = inject(DataService)
  private destroyRef = inject(DestroyRef);
  private cdr = inject(ChangeDetectorRef)
  private authenticationService = inject(AuthenticationService)
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private innoTimerProvider = inject(InnoTimerProvider)
  private translate = inject(TranslateService)
  private addEntryInfo = {
    date: new Date(),
    description: '',
    billable: false,
    workingInfo: null,
    serviceInfo: null,
    timeEnd: ''
  }

  private unsubscribe: Subscription[] = [];
  private unsubscribeTrackingTimer?: Subscription;

  constructor() { }

  ngOnInit() {

    this.listMode = [
      { label: this.translate.instant('TIMETRACKING.Entry'), value: 1 },
      { label: this.translate.instant('TIMETRACKING.Timer'), value: 2 },
    ]
    this.authenticationService.CheckTimer().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.currentMode = 2
        this.unsubscribeTrackingTimer =
          this.dataService.GetTimeTrackingCreateTimerInfo()
            .subscribe(addTimerInfo => {
              if (!addTimerInfo) {
                this.handleResetAddEntry();
              }
              this.previewWorkingInfo = addTimerInfo?.workingInfo
              this.previewServiceInfo = addTimerInfo?.serviceInfo
              this.previewDescription = addTimerInfo?.description ?? ''
              this.previewDate = addTimerInfo?.date ?? new Date()
              this.previewBillable = addTimerInfo?.billable ?? true
              this.timerHours = formatTimeHoursFromSeconds(addTimerInfo?.totalSeconds ?? 0)
              this.previewTimerStatus = addTimerInfo?.timerStatus
            })
      }
    });
    this.handleReloadDataCache();
  }
  handleReloadDataCache() {
    if (this.innoTimerProvider.getCacheClientProject()) {
      this.innoTimerProvider.getCacheClientProject().metadata?.objectClient?.isInternal ? this.isInternal = true : this.isInternal = false;
      this.dataService.SetisInternalClient(this.isInternal)
      this.addEntryInfo.workingInfo = this.innoTimerProvider.getCacheClientProject() as any
      this.previewWorkingInfo = this.addEntryInfo.workingInfo
      this.projectId = this.previewWorkingInfo.value
      const timerDate = this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date ?? new Date()
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ workingInfo: this.innoTimerProvider.getCacheClientProject(), date: timerDate })
      if (this.isInternal) {
        this.previewBillable = false
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: false })
      }
      else {
        const billable = this.innoTimerProvider.getCacheClientProject().metadata?.project?.billable
        this.previewBillable = billable
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: billable })

      }

    }
    else {
      this.previewWorkingInfo = undefined
    }
    if (this.innoTimerProvider.getCacheService()) {
      this.addEntryInfo.serviceInfo = this.innoTimerProvider.getCacheService() as any
      this.previewServiceInfo = this.addEntryInfo.serviceInfo
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ serviceInfo: this.innoTimerProvider.getCacheService() })
    }
    else {
      this.previewServiceInfo = undefined
    }
  }

  private updateTimeTrackingEntryInfo(data: Record<string, any>) {
    const currentValue = this.addEntryInfo
    const newValue = { ...currentValue, ...data }
    this.addEntryInfo = newValue
  }

  handleChangeMode(_mode: number) {
    this.currentMode = _mode;
    if (this.currentMode === 1) {


      this.previewWorkingInfo = this.addEntryInfo.workingInfo as unknown as IFilterDropdownOption
      this.previewServiceInfo = this.addEntryInfo.serviceInfo as unknown as IFilterDropdownOption
      this.previewDescription = this.addEntryInfo.description
      this.previewDate = this.addEntryInfo.date
      this.previewBillable = this.addEntryInfo.billable
      this.previewTimeEnd = this.addEntryInfo.timeEnd

      this.unsubscribeTrackingTimer?.unsubscribe()
    } else {

      this.unsubscribeTrackingTimer =
        this.dataService.GetTimeTrackingCreateTimerInfo()
          .subscribe(addTimerInfo => {
            if (!addTimerInfo) {
              this.handleResetAddEntry();
            }
            this.previewWorkingInfo = addTimerInfo?.workingInfo
            this.previewDescription = addTimerInfo?.description ?? ''
            this.previewDate = addTimerInfo?.date ?? new Date()
            this.previewBillable = addTimerInfo?.billable ?? true
            this.timerHours = formatTimeHoursFromSeconds(addTimerInfo?.totalSeconds ?? 0)
            this.previewTimerStatus = addTimerInfo?.timerStatus
          })
    }
    this.handleReloadDataCache();
  }

  handlePauseOrResumeTime = this.innoTimerProvider.handlePauseOrResumeTime

  handleDiscard() {
    const _title = this.translate.instant('TIMETRACKING.DIALOG.TitleConfirm');
    const _description = this.translate.instant('TIMETRACKING.DIALOG.Discard');
    this.layoutUtilsService.alertConfirm({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this.innoTimerProvider.clearCache();
      this.innoTimerProvider.handleResetTimer()
    })
  }
  EditTimer() {
    this.timerHoursEdit = this.timerHours
    this.isEditTimerHours = !this.isEditTimerHours
  }

  handleEditEndTime(args: { newHours: string, isAlreadyEdit: boolean }) {
    this.isEditTimerHours = false
    if (args.isAlreadyEdit) {
      if (args.newHours != '00:00') {
        this.settimeOur = setTimeout(() => {
          this.previewTimeEnd = args.newHours
          this.addEntryInfo.timeEnd = args.newHours
          this.cdr.detectChanges();
        }, 200);
        localStorage.setItem("isRunning", 'true')
      }
      this.innoTimerProvider.handleUpdateActualTimer(args.newHours)
    }
  }
  handleChangeEndTime(value: string) {
    this.previewTimeEnd = value
    this.addEntryInfo.timeEnd = value
  }
  handleSelectServices(item: IFilterDropdownOption) {
    this.previewServiceInfo = item
    this.innoTimerProvider.setCacheService(item)
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ serviceInfo: item })
    this.addEntryInfo.serviceInfo = item as any
  }

  handleSelectProject(item: IFilterDropdownOption) {

    item.metadata?.objectClient?.isInternal ? this.isInternal = true : this.isInternal = false;
    this.dataService.SetisInternalClient(this.isInternal)
    this.previewWorkingInfo = item
    this.projectId = item.value
    this.innoTimerProvider.setCacheClientProject(item)
    if (this.currentMode === 1) {
      // Payload for the add entry function
      this.updateTimeTrackingEntryInfo({ workingInfo: item })
    } else {
      // Payload for the add timer function
      const timerDate = this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date ?? new Date()
      const __dataUpdate = { workingInfo: item, date: timerDate }
      this.innoTimerProvider.updateTimeTrackingTimerInfo(__dataUpdate)
    }
    if (this.isInternal) {
      this.previewBillable = false
      this.addEntryInfo.billable = false
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: false })
    }
    else {
      const billable = item.metadata?.project?.billable
      this.previewBillable = billable
      this.addEntryInfo.billable = billable
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: billable })
    }
  }

  handleChangeNote(description: any) {
    this.previewDescription = description
    // if (this.currentMode === 1) {
    //   // Payload for the add entry function
    //   this.updateTimeTrackingEntryInfo({ description })
    // } else {
    //   // Payload for the add timer function
    //   this.innoTimerProvider.updateTimeTrackingTimerInfo({ description })
    // }

    // keep data when switch between entry and timer
    this.updateTimeTrackingEntryInfo({ description })
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ description })
  }

  handleChangeDate(value: any) {
    this.previewDate = value
    // if (this.currentMode === 1) {
    //   // Payload for the add entry function
    //   this.updateTimeTrackingEntryInfo({ date: value })
    // } else {
    //   // Payload for the add timer function
    //   this.innoTimerProvider.updateTimeTrackingTimerInfo({ date: value })
    // }

    // keep data when switch between entry and timer
    this.updateTimeTrackingEntryInfo({ date: value })
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ date: value })
  }

  handleChangeBillable(value: any) {
    const billable = value
    this.previewBillable = billable
    if (this.currentMode === 1) {
      // Payload for the add entry function
      this.updateTimeTrackingEntryInfo({ billable })
    } else {
      // Payload for the add timer function
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable })
    }
  }

  handleResetAddEntry() {
    this.previewTimeEnd = ''
    this.previewWorkingInfo = undefined
    this.previewServiceInfo = undefined
    this.previewDescription = ''
    this.previewDate = new Date()
    this.previewBillable = true
    this.updateTimeTrackingEntryInfo({ description: "" })
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ description: "" })
    this.updateTimeTrackingEntryInfo({ date: new Date() })
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ date: new Date() })
    this.timerHoursEdit = ''
    // Payload for the add entry function
    this.addEntryInfo.date = new Date()
    this.addEntryInfo.description = ''
    this.addEntryInfo.billable = true
    this.addEntryInfo.workingInfo = null
    this.addEntryInfo.serviceInfo = null
    this.addEntryInfo.timeEnd = ''
    this.innoTimerProvider.clearCache();
    localStorage.removeItem("isRunning")
    if (this.timerHours && this.timerHours !== '00:00:00') {
      const payload = {
        isRunning: false,
        timerStartTime: null,
        timer: null
      }
      this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
      this.timerHours = null
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ totalSeconds: 0 })
    }
  }

  handleAddTimeTrackingRecord = async () => {
    const payload: any = {}

    if (this.currentMode === 1) {
      const __workingInfo = this.addEntryInfo?.workingInfo as unknown as IFilterDropdownOption
      const __serviceInfo = this.addEntryInfo?.serviceInfo as unknown as IFilterDropdownOption
      payload.endTime = this.addEntryInfo?.timeEnd
      payload.billable = this.dataService.getisInternalClient() ? false : this.addEntryInfo?.billable ?? true
      payload.date = this.addEntryInfo?.date
      payload.description = this.addEntryInfo?.description ?? ''
      payload.clientId = __workingInfo?.metadata?.objectClient?.id
      payload.projectId = __workingInfo?.metadata?.type == 'project' ? __workingInfo?.value : null
      payload.serviceId = __serviceInfo?.value

      this.timeTrackingProvider.handleCreateTimeTracking({
        payload,
        optional: {
          callbackSuccess: () => this.handleResetAddEntry()
        }
      })
    } else {
      this.innoTimerProvider.createTimeTrackingFromTimer().then(() => {
        this.handleResetAddEntry()
      })
    }
  }

  ngOnDestroy(): void {
    if (this.settimeOur) {
      clearTimeout(this.settimeOur);
    }
    this.unsubscribe.forEach((s) => s.unsubscribe());
    if (!localStorage.getItem("isRunning")) {
      this.dataService.SetNewTimeTrackingCreateTimerInfo(undefined)

    }
  }
}
