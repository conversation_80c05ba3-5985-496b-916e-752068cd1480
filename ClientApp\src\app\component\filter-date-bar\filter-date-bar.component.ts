import { Component, EventEmitter, Input, Output } from '@angular/core';
import { DateItem } from './types/dateItem';
import { CommonModule } from '@angular/common';
;

@Component({
  selector: 'app-filter-date-bar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './filter-date-bar.component.html',
  styleUrl: './filter-date-bar.component.scss'
})
export class FilterDateBarComponent {
  @Input('dates') public dates: DateItem[] = [];
  @Input('selectedDate') public selectedDate: Date = new Date();
  @Output() selectedDateChange = new EventEmitter<Date>();

  selectDate(item: DateItem) {
    if (item?.isPrimary && !item.date) {
      return;
    }
    this.selectedDate = item.date ?? new Date();

    this.selectedDateChange.emit(this.selectedDate);
  }

  checkIsActive(currentDate?: Date) {
    return this.selectedDate && currentDate && this.selectedDate.getDate() === currentDate.getDate();
  }
} 
