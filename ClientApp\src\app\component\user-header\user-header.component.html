<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <div class="header-content flex flex-row">

        <div class="header-bar-logo"></div>
        <div class="user-section">
            <a class="btn btn-outline border-circle">
                <span class="material-icons">
                    question_mark
                </span>
            </a>

            <a class="btn btn-outline border-circle">
                <span class="material-icons">
                    notifications
                </span>
            </a>

            <button  class="border-circle" ejs-dropdownbutton [items]="items" (select)='select($event)' iconCss="e-icons e-user user-icon" cssClass="e-caret-hide">
            </button>
        </div>
    </div>
</nav>