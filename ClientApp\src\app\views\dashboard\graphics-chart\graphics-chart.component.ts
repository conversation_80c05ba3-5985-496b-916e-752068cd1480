import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslateModule } from '@ngx-translate/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { InvoiceService } from 'app/service/invoice.service';

@Component({
  selector: 'app-graphics-chart',
  standalone: true,
  imports: [NgxChartsModule, TranslateModule],
  templateUrl: './graphics-chart.component.html',
  styleUrl: './graphics-chart.component.scss'
})
export class GraphicsChartComponent implements OnInit {
  currentYear = new Date().getFullYear();
  private _invoiceService = inject(InvoiceService)
  private destroyRef = inject(DestroyRef);
  chartDataMock = [
    {
      name: 'Invoice',
      series: [
        {
          name: new Date(2017, 0, 1, 2, 34, 17),
          value: 294
        },
        {
          name: new Date(2017, 1, 1, 2, 34, 17),
          value: 314
        },
        {
          name: new Date(2017, 2, 1, 2, 34, 17),
          value: 372
        },
        {
          name: new Date(2017, 3, 1, 2, 34, 17),
          value: 423
        },
        {
          name: new Date(2017, 4, 1, 2, 34, 17),
          value: 332
        }
      ],

    },
    {
      name: 'Expenses',
      series: [
        {
          name: new Date(2017, 0, 1, 2, 34, 17),
          value: 2942
        },
        {
          name: new Date(2017, 1, 1, 2, 34, 17),
          value: 3114
        },
        {
          name: new Date(2017, 2, 1, 2, 34, 17),
          value: 272
        },
        {
          name: new Date(2017, 3, 1, 2, 34, 17),
          value: 1423
        },
        {
          name: new Date(2017, 4, 1, 2, 34, 17),
          value: 1332
        }
      ]
    }
  ];
  chartData: any[] = []
  lineChartProps: any = {
    "view": [1100, 300],
    "autoScale": true,
    "timeline": true,
    "results": this.chartData,
    "gradient": true,
    "xAxis": true,
    "yAxis": true,
    "legend": true,
    "legendTitle": "Legend",
    "showXAxisLabel": true,
    "showYAxisLabel": true,
    "xAxisLabel": "Time",
    "yAxisLabel": "Value",
  }

  GetRevenueChart(year: number) {
    this._invoiceService.GraphicsChart(year).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const mappedData = [
          {
            name: 'Invoice',
            series: res.monthDataInvoice.map((item: any) => ({
              name: item.monthName,
              value: item.paidAmount
            }))
          },
          {
            name: 'Expenses',
            series: res.monthDataExpenses.map((item: any) => ({
              name: item.monthName,
              value: item.paidAmount
            }))
          }
        ];
        this.chartData = mappedData;
        this.lineChartProps["results"] = this.chartData

      }
    }
    )
  }
  ngOnInit(): void {
    this.GetRevenueChart(this.currentYear)
  }
}
