<app-inno-modal-wrapper [title]="'TEAMMEMBERS.AddButton'"
    (onClose)="closeDialog()">
    <form [formGroup]="memberForm" class="p-3" (ngSubmit)="onSubmit()">
        <div class="grid gap-6 mb-6 md:grid-cols-2">
            <div class="text-start">
                <input type="text"
                    formControlName="firstname"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'TEAMMEMBERS.AddMemberForm.FirstName' | translate"
                    required />
                @if (
                (f['firstname'].dirty || f['firstname'].touched) &&
                f['firstname'].hasError('required')
                ) {
                <mat-error class="matError"> {{
                    'TEAMMEMBERS.AddMemberForm.FirstNameRequired' | translate
                    }}</mat-error>
                }
            </div>
            <div class="text-start">

                <input type="text" id="last_name"
                    formControlName="lastname"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'TEAMMEMBERS.AddMemberForm.LastName' | translate"
                    required />
                @if (
                (f['lastname'].dirty || f['lastname'].touched) &&
                f['lastname'].hasError('required')
                ) {
                <mat-error class="matError"> {{
                    'TEAMMEMBERS.AddMemberForm.LastNameRequired' | translate
                    }}</mat-error>
                }
            </div>
        </div>
        <div class="form-group mb-3">
            <div>
                <input type="email" id="_email"
                    formControlName="email"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'TEAMMEMBERS.AddMemberForm.Email' | translate"
                    required />
            </div>
            @if (
            (f['email'].dirty || f['email'].touched) &&
            f['email'].hasError('required')
            ) {
            <mat-error class="matError"> {{
                'TEAMMEMBERS.AddMemberForm.EmailRequired' | translate
                }}</mat-error>
            }
            @if (
            (f['email'].dirty || f['email'].touched) &&
            f['email'].hasError('email')
            ) {
            <mat-error class="matError">{{
                'TEAMMEMBERS.AddMemberForm.InvalidEmail' | translate
                }}</mat-error>
            }
        </div>

        <div class=" flex justify-center">
            <button (click)="closeDialog()" type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">{{'BUTTON.Cancel'|translate}}</button>
            <button [disabled]="!memberForm.valid"
                [ngClass]="{' bg-green-700 hover:bg-green-800' : memberForm.valid,' bg-gray-300 ': !memberForm.valid}"
                type="submit"
                class="focus:outline-none text-white focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">{{'BUTTON.Save'|translate}}</button>
        </div>
    </form>

</app-inno-modal-wrapper>