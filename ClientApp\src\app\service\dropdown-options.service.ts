import { inject, Injectable } from "@angular/core";
import { firstValueFrom } from 'rxjs';
import { IFilterDropdownOption } from "app/dto/interface/common.interface";
import { TimetrackingService } from "./timetracking.service";
import { UserBusinessService } from "./user-business.service";
import { MerchantService } from "./merchant.service";
import { CategoryService } from "./category.service";
import { ServiceService } from './service.service';
import { Category } from '../dto/interface/category.interface';
import { Service } from "../dto/interface/service.interface";

@Injectable({
  providedIn: 'root'
})
export class DropdownOptionsService {

  private timeTrackingService = inject(TimetrackingService)
  private userBusinessServices = inject(UserBusinessService)
  private merchantService = inject(MerchantService)
  private services = inject(ServiceService)
  private categoryService = inject(CategoryService)

  async getDropdownOptionsProjectAndClient(args?: { isOnlyProject?: boolean, isOnlyClient?: boolean }): Promise<IFilterDropdownOption[]> {

    return firstValueFrom(
      this.timeTrackingService.GetProjectAndClientService({
        Page: 1,
        PageSize: 100,
        Search: "",
        getAll: true,
        active: true,
      })
    ).then((res) => {
      const result: IFilterDropdownOption[] = [];
      const data = res?.data ?? [];

      if (data.length) {
        data.forEach((client: any) => {
          const clientItem: IFilterDropdownOption = {
            label: client.clientName ?? '',
            value: client.id,
            metadata: {
              type: 'client',
              client,
            }
          };

          // Add project to list option
          if (!args?.isOnlyProject) result.push(clientItem);

          const listProject = client.projects ?? [];
          if (!args?.isOnlyClient && listProject.length) {
            listProject.forEach((project: any) => {
              const projectItem: IFilterDropdownOption = {
                label: project.projectName ?? '',
                value: project.id,
                metadata: {
                  type: 'project',
                  description: project.description ?? '',
                  objectClient: client,
                  project
                }
              };

              // Add project to list option
              result.push(projectItem);
            });
          }
        });
      }
      return result;
    }).catch(() => []);
  }
  async getDropdownOptionsProjectAndClientTimeTracking(args?: { isOnlyProject?: boolean, isOnlyClient?: boolean }): Promise<IFilterDropdownOption[]> {
    return firstValueFrom(
      this.timeTrackingService.GetProjectAndClientService({
        Page: 1,
        PageSize: 100,
        Search: "",
        active: true,
        getAll: true,
      })
    ).then((res) => {
      const result: IFilterDropdownOption[] = [];
      const data = res?.data ?? [];

      if (data.length) {
        data.forEach((client: any) => {
          const clientItem: IFilterDropdownOption = {
            label: client.clientName ?? '',
            value: client.id,
            metadata: {
              type: 'client',
              client,
            }
          };

          // Add project to list option
          if (!args?.isOnlyProject) result.push(clientItem);

          const listProject = client.projects ?? [];
          if (!args?.isOnlyClient && listProject.length) {
            listProject.forEach((project: any) => {
              const projectItem: IFilterDropdownOption = {
                label: project.projectName ?? '',
                value: project.id,
                metadata: {
                  type: 'project',
                  description: project.description ?? '',
                  objectClient: client,
                  project
                }
              };

              // Add project to list option
              result.push(projectItem);
            });
          }
        });
      }
      return result;
    }).catch(() => []);
  }
  async getDropdownOptionsUser(): Promise<IFilterDropdownOption[]> {
    return firstValueFrom(
      this.userBusinessServices.GetAllUserBusiness({
        Page: 1,
        PageSize: 100,
        Search: "",
      })
    ).then((res: any) => {
      const result: IFilterDropdownOption[] = [];
      const data: any[] = res?.data ?? [];

      if (data.length) {
        data.forEach(item => {
          const user = item.user;
          if (!user) return;

          const fullName = [user.firstName, user.lastName].filter(x => x).join(' ');
          const clientItem: IFilterDropdownOption = {
            label: fullName,
            value: user.id,
            metadata: user
          };
          result.push(clientItem);
          // if (clientItem.value != this.authenticationService.getIdUser()) {
          //   result.push(clientItem);
          // }
        });
      }
      return result;
    }).catch(() => []);
  }

  async getDropdownOptionsService(ProjectId: string, sort: any): Promise<IFilterDropdownOption[]> {
    return firstValueFrom(
      this.services.GetAllService({
        Page: 0,
        PageSize: 100,
        Search: "",
        ProjectId: ProjectId,
        isInProject: true, 
        ...sort
      })
    ).then((res) => {
      const result: IFilterDropdownOption[] = [];
      const data: Service[] = res?.data ?? [];

      if (data.length) {
        data.forEach(item => {
          const serviecItem: IFilterDropdownOption = {
            label: item.serviceName,
            value: item.id,
            metadata: item.project
          };
          result.push(serviecItem);
        });
      }
      return result;
    }).catch(() => []);
  }

  async getDropdownOptionsMerchant(): Promise<IFilterDropdownOption[]> {
    return firstValueFrom(
      this.merchantService.GetAllMerchant({
        Page: 1,
        PageSize: 100,
        Search: "",
      })
    ).then((res: any) => {
      const result: IFilterDropdownOption[] = [];
      const data = res?.data ?? [];

      if (data.length) {
        data.forEach((expense: any) => {
          const expenseItem: IFilterDropdownOption = {
            label: expense.merchantName,
            value: expense.id,
          };
          result.push(expenseItem);
        });
      }
      return result;
    }).catch(() => []);
  }

  async getDropdownOptionsCategories(): Promise<IFilterDropdownOption[]> {

    return firstValueFrom(
      this.categoryService.GetCategory()
    ).then((listCategory: Category[]) => {
      if (!listCategory?.length) return []
      const result: IFilterDropdownOption[] = [];

      listCategory.forEach((item: any) => {
        const parentCategory: IFilterDropdownOption = {
          label: item.categoryName ?? '',
          value: item.id,
          metadata: {
            type: 'parentCategory',
          }
        };

        result.push(parentCategory);

        const childCategories = item.categoryItems ?? [];
        if (childCategories.length) {
          childCategories.forEach((child: any) => {
            const childCategory: IFilterDropdownOption = {
              label: child.itemName ?? '',
              value: child.id,
              metadata: {
                type: 'childCategory',
                parentCategory: item
              }
            };

            result.push(childCategory);
          });
        }
      });

      return result;
    }).catch(() => []);
  }
}
