.customDatePickerV2 .e-date-wrapper {
  margin: 0 !important;
  padding: 8px 12px;
  display: flex;
  min-width: 125px !important;
  height: 40px !important;
  border-radius: 8px;
}

.customDatePickerV2 .e-input-group {
  @apply bg-bg-primary;
}

.customDatePickerV2 .e-input-group > input {
  padding: 0;
}

.customDatePickerV2 .e-input-group::before,
.customDatePickerV2 .e-input-group::after {
  display: none;
}

.customDatePickerV2 .e-input-group-icon.e-date-icon {
  margin: 0;
}

.customDatePickerV2 .e-date-wrapper .e-icons {
  min-height: unset !important;
}

// COLOR
.customDatePickerV2 input.e-input::selection,
.customDatePickerV2 .e-date-wrapper::before,
.customDatePickerV2 .e-date-wrapper::after,
.e-datepicker .e-focused-date.e-selected .e-day,
.e-datepicker .e-cell.e-selected .e-day {
  background-color: #0f182e !important;
}

.customDatePickerV2 .e-date-wrapper .e-icons.e-active,
.customDatePickerV2 input.e-input,
.e-datepicker .e-today:not(.e-focused-date) .e-day,
.e-datepicker .e-today {
  color: #0f182e !important;
}

.e-datepicker .e-model-header {
  background-color: white !important;
}

.e-datepicker .e-today .e-day,
.customDatePickerV2 .e-date-wrapper {
  border: 2px solid #E9EAEB !important;
}

.e-datepicker .e-today:hover {
  background-color: rgb(36, 36, 36, 0.2) !important;
}
