import { SpinnerService } from 'app/service/spinner.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { SharedModule } from './../../../module/shared.module';
import { Component, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { DialogAllModule } from '@syncfusion/ej2-angular-popups';
import { NewBusinessComponent } from './new-business/new-business.component';
import { UserBusinessService } from 'app/service/user-business.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { StoreService } from 'app/service/store.service';
import { MatDialogRef } from '@angular/material/dialog';
import { ConfirmTimerDialog } from '../../../service/dialog/confirm-timer.dialog';
import { NewBusinessDialog } from '../../../service/dialog/new-business.dialog';

@Component({
  selector: 'app-switch-workspace',
  standalone: true,
  imports: [
    SharedModule,
    InnobookModalWrapperComponent,
    NewBusinessComponent,
    DialogAllModule
  ],
  templateUrl: './switch-workspace.component.html',
  styleUrl: './switch-workspace.component.scss'
})
export class SwitchWorkspaceComponent implements OnInit, OnDestroy {

  destroyRef = inject(DestroyRef);
  isloading: boolean = false
  listUserBusiness: UserBusiness[] = []
  private _spinnerService = inject(SpinnerService)
  private businessService = inject(UserBusinessService)
  private authenticationService = inject(AuthenticationService)
  public _storeService = inject(StoreService)
  public tempBusiness: any

  static getComponent(): typeof SwitchWorkspaceComponent {
    return SwitchWorkspaceComponent;
  }

  constructor(public dialogRef: MatDialogRef<SwitchWorkspaceComponent>, private confirmTimerDialog: ConfirmTimerDialog,
    private newBusinessDialog: NewBusinessDialog) {
  }

  closeDialog() {
    this.dialogRef.close();
  }
  ngOnDestroy(): void {

  }
  ngOnInit() {
    this.isloading = true
    this.GetUserBusiness();
  }
  OpenConfirm() {
    const dialogRef = this.confirmTimerDialog.open({});

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this._spinnerService.show();
          this._storeService.setChooseBusiness(this.tempBusiness)
          this.authenticationService.UpdateAccessToken({ BusinessId: this.tempBusiness.businessId }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
            if (res) {
              this.authenticationService.saveToken_cookie(res.accessToken, res.refreshToken)
              this.dialogRef.close(res);
              this._spinnerService.hide();
              localStorage.removeItem("isRunning")
              localStorage.removeItem("ResumeData")
              localStorage.removeItem("cacheClientProject")
              localStorage.removeItem("cacheService")
              this.tempBusiness = null
            }
          }
          )
        }
        else {
          this.tempBusiness = null
        }
      })
    });

  }
  GetUserBusiness() {
    this.businessService.GetUserBusiness().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      this.isloading = false
      this.listUserBusiness = res;
      if (this.listUserBusiness.length > 0 && !this._storeService.getChooseBusiness()) {
        let data = {
          businessId: this.listUserBusiness[0].businessId,
        }
        this.DefaultChooseBusiness(data)
      }
    }
    )
  }
  DefaultChooseBusiness(item: any) {
    this._spinnerService.show();
    let data = {
      businessId: item.businessId,
    }
    this._storeService.setChooseBusiness(data)
    this.authenticationService.UpdateAccessToken({ BusinessId: item.businessId }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.authenticationService.saveToken_cookie(res.accessToken, res.refreshToken)
        localStorage.removeItem("cacheClientProject");
        localStorage.removeItem("cacheService")
        this._spinnerService.hide();
        this.dialogRef.close(res);
      }
    }
    )
  }
  ChooseBusiness(item: any) {
    let data = {
      businessId: item.businessId,
    }
    this.tempBusiness = data;
    if (localStorage.getItem("isRunning") === 'true') {
      this.OpenConfirm();
      return;
    }
    this._spinnerService.show();

    this._storeService.setChooseBusiness(data)
    this.authenticationService.UpdateAccessToken({ BusinessId: item.businessId }).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.authenticationService.saveToken_cookie(res.accessToken, res.refreshToken)
        this._spinnerService.hide();
        localStorage.removeItem("cacheClientProject");
        localStorage.removeItem("cacheService")
        this.dialogRef.close(res);
      }
    }
    )
  }

  openAddNewBusiness() {
    const dialogRef = this.newBusinessDialog.open({});

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {

          this.GetUserBusiness();
        }
      })
    });
  }

}
