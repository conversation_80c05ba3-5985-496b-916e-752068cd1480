@if(isShowingTimer) {
<div class="innoTimer hide" [class.active]="isExpand">
  <div
    class="h-[40px] rounded-tl-[8px] rounded-tr-[8px] w-full flex gap-[6px] items-center bg-bg-secondary py-[8px] px-[16px]">
    <button
      class="button-icon shrink-0"
      (click)="handleToggleExpand()"
      [ngClass]="{'transform rotate-180': !isExpand}">
      <img src="../../../assets/img/icon/ic_arrow_down_gray.svg" alt="Icon">
    </button>
    <p
      class="grow text-center line-clamp-1 text-text-sm-bold text-text-primary">
      @if(title)
      {
      {{title}}
      }@else{
      No client
      }
    </p>
    <!-- <button class="button-icon shrink-0" (click)="handleCloseTimer()">
      <img src="../../../assets/img/icon/ic_remove.svg" alt="Icon">
    </button> -->
  </div>
  <div class="w-full px-[16px] overflow-auto">
    <div class="h-[70px] flex justify-center items-center gap-[16px]">
      @if(isEditTimerHours)
      {
      <app-inno-enter-edit-hours
        [value]="timerHoursEdit"
        [dynamicWidth]="210"
        (onChange)="handleEditEndTime($event)" />
      }@else{
      <p (click)="EditTimer()"
        class="px-[10px] transition-all cursor-pointer hover:bg-bg-tertiary rounded-sm text-headline-xl-bold text-text-primary">
        {{ timerHours || '00:00:00' }}
      </p>
      }

      <button (click)="handlePauseOrResumeTime()"
        class="bg-bg-brand-primary cursor-pointer rounded-full w-[36px] h-[36px] flex justify-center items-center border-1 border-bg-brand-strong-hover">
        @if(isPause) {
        <img class="w-[16px]" src="../../../assets/img/icon/ic_play.svg"
          alt="Icon">
        } @else {
        <img class="w-[16px]" src="../../../assets/img/icon/ic_pause.svg"
          alt="Icon">
        }
      </button>
    </div>
    <div class="w-full flex flex-col gap-[8px]">
      @if(!isResume)
      {

      <app-inno-select-search-project
        [templateTrigger]="templateTriggerSelectProject"
        (onSelect)="handleSelectProject($event)"
        [isOnlySelectProject]="true"
        [isTimetracking]="true"
        [value]="previewWorkingInfo?.value" />
      <ng-template #templateTriggerSelectProject>
        <button
          class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-full">
          <span
            class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
            [ngClass]="{'!text-text-primary !font-bold': previewWorkingInfo!! }">
            {{ previewWorkingInfo?.metadata?.objectClient?.clientName }} -
            {{ previewWorkingInfo?.label ?? 'TIMETRACKING.EntryPlaceholder'|
            translate }}
          </span>
        </button>
      </ng-template>
      }@else{
      <button
        class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-full">
        <span
          class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
          [ngClass]="{'!text-text-primary !font-bold': previewWorkingInfo!! }">
          {{ previewWorkingInfo?.metadata?.objectClient?.clientName }} -
          {{ previewWorkingInfo?.label }}
        </span>
      </button>
      }

      @if(previewWorkingInfo)
      {
      @if(!isResume)
      {
      <app-inno-select-search-service
        [templateTrigger]="templateTriggerSelectService"
        (onSelect)="handleSelectServices($event)"
        [lable]="previewWorkingInfo.label"
        [value]="projectId" />

      <ng-template #templateTriggerSelectService>
        <button
          class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-full">
          <span
            class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
            [ngClass]="{'!text-text-primary !font-bold': previewServiceInfo!! }">

            {{ previewServiceInfo?.label ?? 'TIMETRACKING.ChooseService'|
            translate }}
          </span>
        </button>
      </ng-template>
      }@else{
      <button
        class="h-[40px] cursor-pointer border-2 rounded-md border-border-primary w-full">
        <span
          class="px-[8px] text-left line-clamp-1 text-text-md-regular text-text-placeholder"
          [ngClass]="{'!text-text-primary !font-bold': previewServiceInfo!! }">

          {{ previewServiceInfo?.label ?? 'No Service' }}
        </span>
      </button>
      }
      }
      <app-inno-form-textarea
        placeholder="Note"
        [value]="previewDescription"
        (onChange)="handleChangeNote($event)" />
      <app-inno-select-search-tags
        [templateTrigger]="templateTriggerSelectTags" />
      <ng-template #templateTriggerSelectTags>
        <app-inno-tags target value="Only InnoBook" />
      </ng-template>
      <app-inno-datepicker
        placeholder="Select date"
        [value]="previewDate"
        (onChange)="handleChangeDate($event)" />
      @if(!isInternal)
      {
      <app-inno-form-checkbox
        [checked]="previewBillable"
        (onChange)="handleChangeBillable($event)">
        {{'TIMETRACKING.Billable'|
        translate }}
      </app-inno-form-checkbox>
      }

      <div class="w-full flex flex-col mt-[16px] gap-[8px]">
        <button class="button-primary button-size-md w-full justify-center"
          [disabled]="!previewWorkingInfo || !previewDate"
          (click)="handleAddTimeTrackingRecord()">
          {{'TIMETRACKING.LogTime'|
          translate }}
        </button>
        <button class="button-outline button-size-md w-full justify-center"
          (click)="handleDiscard()">
          {{'TIMETRACKING.Discard'|
          translate }}
        </button>
      </div>
    </div>
  </div>
</div>
}
