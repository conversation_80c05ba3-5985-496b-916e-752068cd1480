import { UserBusinessService } from './../../../../service/user-business.service';
import { SharedModule } from 'app/module/shared.module';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { COUNTRIES } from 'app/utils/country-items';
import { MatFormFieldModule } from '@angular/material/form-field';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-new-business',
  standalone: true,
  imports: [InnobookModalWrapperComponent, MatFormFieldModule, SharedModule],
  templateUrl: './new-business.component.html',
  styleUrl: './new-business.component.scss'
})
export class NewBusinessComponent implements OnInit {
  public businessForm!: UntypedFormGroup;
  countries = COUNTRIES;
  submitted: boolean = false

  destroyRef = inject(DestroyRef);
  private formBuilder = inject(UntypedFormBuilder)
  private user_business = inject(UserBusinessService)
  static getComponent(): typeof NewBusinessComponent {
    return NewBusinessComponent;
  }


  constructor(public dialogRef: MatDialogRef<NewBusinessComponent>,) {
    this.businessForm = this.formBuilder.group({
      business: ["", Validators.compose([Validators.required])],
      note: [""],
      email: ["", Validators.compose([Validators.required, Validators.email])],
      country: ['', Validators.compose([Validators.required])],
    })


  }
  onSubmit() {
    if (!this.submitted) {
      this.submitted = true
      let payload = {
        name: this.businessForm.controls["business"].value,
        note: this.businessForm.controls["note"].value,
        email: this.businessForm.controls["email"].value,
        country: this.businessForm.controls["country"].value,
      }
      this.user_business.CreateUserBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          if (res) {
            this.submitted = false
            this.dialogRef.close(res);
          }
        }
      });
    }

  }

  ngOnInit() {

  }

  get f() {
    return this.businessForm.controls;
  }
  closeDialog() {
    this.dialogRef.close();
  }
}
