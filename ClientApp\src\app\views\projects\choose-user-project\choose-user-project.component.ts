import { AuthenticationService } from 'app/auth/service/authentication.service';
import { UserBusinessService } from 'app/service/user-business.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormBuilder, FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { ReplaySubject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AvatarModule } from 'ngx-avatars';
import { SharedModule } from 'app/module/shared.module';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-choose-user-project',
  standalone: true,
  imports: [AvatarModule, MatInputModule, SharedModule],
  templateUrl: './choose-user-project.component.html',
  styleUrl: './choose-user-project.component.scss'
})
export class ChooseUserProjectComponent {

  // Public properties
  @Input() options: any = {
    showSearch: true,//hiển thị search input hoặc truyền keyword
    keyword: '',
    data: []
  };
  @Input() isNewView = false;
  @Output() ItemSelected = new EventEmitter<any>();
  @Output() IsSearch = new EventEmitter<any>();

  listUser: any[] = [];
  customStyle: any = [];
  public filteredUsers: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public userFilterCtrl: FormControl = new FormControl();
  destroyRef = inject(DestroyRef);
  router = inject(Router);
  private auth_services = inject(AuthenticationService)
  private userbusiness_services = inject(UserBusinessService)
  public _storeService = inject(StoreService)
  constructor(
  ) { }

  /**
   * On init
   */
  ngOnInit() {
    this.userFilterCtrl.valueChanges
      .pipe()
      .subscribe(() => {
        this.filterUsers();
      });
    this.GetAllMemberInBusiness();
  }
  GetAllMemberInBusiness() {
    let payload: Parameter = {
      Page: 1,
      PageSize: 100,
      Search: "",
    }
    this.userbusiness_services.GetAllUserBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.listUser = res.data.filter((x: any) => x.user.id != this.auth_services.getIdUser())
        this.filterUsers();

      }
    }
    )
  }
  protected filterUsers() {
    if (!this.listUser) {
      return;
    }

    let search = !this.options.showSearch ? this.options.keyword : this.userFilterCtrl.value;
    if (!search) {
      this.filteredUsers.next(this.listUser.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    if (search[0] == '@') {
      this.filteredUsers.next(
        this.listUser.filter(bank => (bank.user.firstName.toLowerCase()).indexOf(search.replace('@', '')) > -1)
      );
    }
    else {
      this.filteredUsers.next(
        this.listUser.filter(bank => bank.user.firstName.toLowerCase().indexOf(search) > -1)
      );
    }
  }
  select(user: any) {
    this.ItemSelected.emit(user)
  }
  stopPropagation(event: any) {
    this.IsSearch.emit(event)
  }
}
