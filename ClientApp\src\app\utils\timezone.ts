export interface TimeZone {
    Name: string;
    Code: string;
  Moment: string[];
}
export const TIMEZONE: TimeZone[] = [
  { Name: '(UTC-12:00) International Date Line West', Code: 'Dateline Standard Time', Moment: ["Etc/GMT+12"] },
  { Name: '(UTC-11:00) Coordinated Universal Time-11', Code: 'UTC-11', Moment: ["Pacific/Midway"] },
  { Name: '(UTC-10:00) Hawaii', Code: 'Hawaiian Standard Time', Moment: ["Pacific/Honolulu"] },
  { Name: '(UTC-09:00) Alaska', Code: 'Alaskan Standard Time', Moment: ["America/Anchorage"] },
  { Name: '(UTC-08:00) Pacific Time (US & Canada)', Code: 'Pacific Standard Time', Moment: ["America/Los_Angeles"] },
  { Name: '(UTC-07:00) Mountain Time (US & Canada)', Code: 'Mountain Standard Time', Moment: ["America/Denver"] },
  { Name: '(UTC-06:00) Central Time (US & Canada)', Code: 'Central Standard Time', Moment: ["America/Chicago"] },
  { Name: '(UTC-05:00) Eastern Time (US & Canada)', Code: 'Eastern Standard Time', Moment: ["America/New_York"] },
  { Name: '(UTC-04:00) Atlantic Time (Canada)', Code: 'Atlantic Standard Time', Moment: ["America/Halifax"] },
  { Name: '(UTC-03:00) Buenos Aires', Code: 'Argentina Standard Time', Moment: ["America/Argentina/Buenos_Aires"] },
  { Name: '(UTC-02:00) Mid-Atlantic', Code: 'Mid-Atlantic Standard Time', Moment: ["Atlantic/South_Georgia"] },
  { Name: '(UTC-01:00) Azores', Code: 'Azores Standard Time', Moment: ["Atlantic/Azores"] },
  { Name: '(UTC±00:00) London, Lisbon', Code: 'GMT Standard Time', Moment: ["Europe/London"] },
  { Name: '(UTC+01:00) Brussels, Paris', Code: 'Central European Standard Time', Moment: ["Europe/Brussels"] },
  { Name: '(UTC+02:00) Cairo', Code: 'Egypt Standard Time', Moment: ["Africa/Cairo"] },
  { Name: '(UTC+03:00) Moscow, Riyadh', Code: 'Arab Standard Time', Moment: ["Europe/Moscow"] },
  { Name: '(UTC+04:00) Dubai', Code: 'Azerbaijan Standard Time', Moment: ["Asia/Dubai"] },
  { Name: '(UTC+05:00) Pakistan', Code: 'Pakistan Standard Time', Moment: ["Asia/Karachi"] },
  { Name: '(UTC+06:00) Almaty', Code: 'Central Asia Standard Time', Moment: ["Asia/Almaty"] },
  { Name: '(UTC+07:00) Bangkok', Code: 'SE Asia Standard Time', Moment: ["Asia/Bangkok"] },
  { Name: '(UTC+08:00) Beijing', Code: 'China Standard Time', Moment: ["Asia/Shanghai"] },
  { Name: '(UTC+09:00) Tokyo', Code: 'Tokyo Standard Time', Moment: ["Asia/Tokyo"] },
  { Name: '(UTC+10:00) Sydney', Code: 'AUS Eastern Standard Time', Moment: ["Australia/Sydney"] },
  { Name: '(UTC+11:00) Solomon Islands', Code: 'Central Pacific Standard Time', Moment: ["Pacific/Guadalcanal"] },
  { Name: '(UTC+12:00) Fiji', Code: 'Fiji Standard Time', Moment: ["Pacific/Fiji"] }
];

export const timezoneMapping = {
  // Amérique du Nord
  'America/Toronto': 'America/New_York',  // Toronto est dans le même fuseau horaire que New York
  'America/Vancouver': 'America/Los_Angeles', // Vancouver est dans le même fuseau horaire que Los Angeles
  'America/Los_Angeles': 'America/Los_Angeles',
  'America/Chicago': 'America/Chicago',
  'America/New_York': 'America/New_York',
  'America/Anchorage': 'America/Anchorage',
  'America/Houston': 'America/Chicago',
  'America/Denver': 'America/Denver',
  'America/Calgary': 'America/Denver',
  'America/Phoenix': 'America/Denver',  // Phoenix ne passe pas à l'heure d'été, donc c'est dans le même fuseau horaire que Denver

  // Amérique Centrale
  'America/Mexico_City': 'America/Mexico_City',
  'America/Regina': 'America/Chicago', // Regina est dans le même fuseau horaire que Chicago
  'America/Bogota': 'America/New_York', // Bogota est sur le même fuseau horaire que New York

  // Europe
  'Europe/London': 'Europe/London',
  'Europe/Paris': 'Europe/Paris',
  'Europe/Brussels': 'Europe/Brussels',
  'Europe/Berlin': 'Europe/Brussels', // Berlin et Bruxelles partagent le même fuseau horaire
  'Europe/Madrid': 'Europe/Brussels', // Madrid et Bruxelles partagent le même fuseau horaire
  'Europe/Amsterdam': 'Europe/Brussels', // Amsterdam et Bruxelles partagent le même fuseau horaire
  'Europe/Rome': 'Europe/Brussels', // Rome et Bruxelles partagent le même fuseau horaire
  'Europe/Copenhagen': 'Europe/Brussels',
  'Europe/Stockholm': 'Europe/Brussels',
  'Europe/Helsinki': 'Europe/Brussels',
  'Europe/Istanbul': 'Europe/Istanbul', // Istanbul a un fuseau horaire différent, mais dans cette catégorie géographique

  // Asie
  'Asia/Tokyo': 'Asia/Tokyo',
  'Asia/Seoul': 'Asia/Tokyo',
  'Asia/Shanghai': 'Asia/Shanghai',
  'Asia/Hong_Kong': 'Asia/Shanghai',
  'Asia/Karachi': 'Asia/Karachi',
  'Asia/Kolkata': 'Asia/Kolkata',
  'Asia/Kathmandu': 'Asia/Kolkata', // Katmandou partage le même fuseau horaire que Kolkata
  'Asia/Jakarta': 'Asia/Jakarta',
  'Asia/Ho_Chi_Minh': 'Asia/Jakarta', // Ho Chi Minh partage le même fuseau horaire que Jakarta

  // Océanie
  'Australia/Sydney': 'Australia/Sydney',
  'Australia/Brisbane': 'Australia/Sydney',
  'Australia/Melbourne': 'Australia/Sydney',
  'Pacific/Guam': 'Pacific/Guam',
  'Pacific/Fiji': 'Pacific/Fiji',
  'Pacific/Noumea': 'Pacific/Fiji',  // Nouméa partage le même fuseau horaire que Fidji

  // Afrique
  'Africa/Cairo': 'Africa/Cairo',
  'Africa/Nairobi': 'Africa/Nairobi',
  'Africa/Johannesburg': 'Africa/Johannesburg',
  'Africa/Lagos': 'Africa/Johannesburg', // Lagos partage le même fuseau horaire que Johannesburg
  'Africa/Khartoum': 'Africa/Cairo',
  'Africa/Douala': 'Africa/Johannesburg', // Douala partage le même fuseau horaire que Johannesburg

  // Autres
  'Asia/Dubai': 'Asia/Dubai',
  'Asia/Baku': 'Asia/Dubai',  // Baku partage le même fuseau horaire que Dubaï
  'Asia/Singapore': 'Asia/Singapore',
  'Asia/Bangkok': 'Asia/Bangkok',
  'Asia/Kuala_Lumpur': 'Asia/Singapore', // Kuala Lumpur partage le même fuseau horaire que Singapour
  'Atlantic/South_Georgia': 'Atlantic/South_Georgia',  // Mid-Atlantic
  'Atlantic/Azores': 'Atlantic/Azores',  // Azores
  'Asia/Almaty': 'Asia/Almaty',  // Almaty
  'Asia/Yekaterinburg': 'Asia/Yekaterinburg',  // Ekaterinburg
  'Asia/Samara': 'Asia/Samara', // Samara
  'Africa/Casablanca': 'Africa/Casablanca', // Casablanca
  'Africa/Abidjan': 'Africa/Casablanca',  // Abidjan partage le même fuseau horaire que Casablanca
  'Africa/Dakar': 'Africa/Casablanca'  // Dakar partage le même fuseau horaire que Casablanca
};
