{"name": "innologiciel.client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve innobook --configuration=development --hmr", "build": "ng build", "test": "ng test", "lint": "ng lint", "playwrightInstall": "npx playwright install", "playwright": "npx playwright test --config playwright.config.ts", "build:stats": "ng build --stats-json", "analyze": "webpack-bundle-analyzer dist/stats.json"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^2.3.0", "@angular/animations": "^18.1.0", "@angular/cdk": "^18.2.8", "@angular/common": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/material": "^18.2.8", "@angular/platform-browser": "^18.2.7", "@angular/platform-browser-dynamic": "^18.2.7", "@angular/router": "^18.1.0", "@auth0/angular-jwt": "^5.2.0", "@floating-ui/dom": "^1.6.12", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@sentry/angular": "^8.33.1", "@swimlane/ngx-charts": "^20.5.0", "@syncfusion/ej2-angular-calendars": "^27.1.52", "@syncfusion/ej2-angular-dropdowns": "^27.1.53", "@syncfusion/ej2-angular-grids": "^27.1.52", "@syncfusion/ej2-angular-inputs": "^27.1.50", "@syncfusion/ej2-angular-lists": "^27.1.50", "@syncfusion/ej2-angular-navigations": "^27.1.52", "@syncfusion/ej2-angular-notifications": "^27.1.50", "@syncfusion/ej2-angular-popups": "^27.1.56", "@syncfusion/ej2-angular-schedule": "^27.2.2", "@syncfusion/ej2-angular-splitbuttons": "^27.1.56", "@syncfusion/ej2-base": "^27.1.52", "@syncfusion/ej2-material-theme": "^27.1.52", "angular-code-input": "^2.0.0", "bootstrap": "^5.3.3", "jest-editor-support": "*", "moment-timezone": "^0.5.46", "ngx-avatars": "^1.8.0", "ngx-cookie-service": "^18.0.0", "ngx-mask": "^18.0.3", "run-script-os": "*", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.0", "@angular/cli": "^18.1.0", "@angular/compiler-cli": "^18.1.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "~5.5.2"}}