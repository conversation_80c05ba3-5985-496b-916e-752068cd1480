import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { InvoiceService } from 'app/service/invoice.service';
import { InnoSelectYearComponent } from './../../../component/inno-select-year/inno-select-year.component';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { LegendPosition } from '@swimlane/ngx-charts';
import { DropDownListModule } from '@syncfusion/ej2-angular-dropdowns';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
@Component({
  selector: 'app-revenue-expenses-chart',
  standalone: true,
  imports: [NgxChartsModule, DecimalPipe, SharedModule, FormatNumberPipe, DropDownListModule, InnoSelectYearComponent],
  templateUrl: './revenue-expenses-chart.component.html',
  styleUrl: './revenue-expenses-chart.component.scss'
})
export class RevenueExpensesChartComponent implements OnInit {

  ListMonth = [
    { name: 'January', value: 0 },
    { name: 'February', value: 0 },
    { name: 'March', value: 0 },
    { name: 'April', value: 0 },
    { name: 'May', value: 0 },
    { name: 'June', value: 0 },
    { name: 'July', value: 0 },
    { name: 'August', value: 0 },
    { name: 'September', value: 0 },
    { name: 'October', value: 0 },
    { name: 'November', value: 0 },
    { name: 'December', value: 0 },
  ];
  dataChart: any[] = []
  view: any = [1100, 600];
  // options for the chart
  showXAxis = true;
  showYAxis = true;
  gradient = true;
  showLegend = true;
  legendPosition = LegendPosition.Below;
  showXAxisLabel = true;
  xAxisLabel = 'Expenditure comparison chart';
  showYAxisLabel = true;
  yAxisLabel = 'Sales';
  timeline = true;
  doughnut = true;
  public colorScheme: any = {
    domain: ['#FF7F50', '#50EE90']
  };
  totalAmount: number;
  totalAmountPaid: number;
  currentYear = new Date().getFullYear();
  private _invoiceService = inject(InvoiceService)
  private destroyRef = inject(DestroyRef);
  constructor() {

  }
  handleSelectYear($event) {
    this.GetRevenueChart($event)


  }
  GetRevenueChart(year: number) {
    this._invoiceService.GetRevenueChart(year).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this.totalAmount = res.totalAmount
        this.totalAmountPaid = res.totalAmountPaid
        const mappedData = res.monthData.map(element => ({
          name: element.monthName,
          series: [
            {
              name: "Outstanding invoices",
              value: element.totalAmount
            },
            {
              name: "Amount Spent",
              value: element.totalAmountPaid
            }
          ]
        }));
        this.dataChart = mappedData;
      }
    }
    )
  }
  ngOnInit(): void {
    this.GetRevenueChart(this.currentYear);
    // this.ListMonth.forEach(element => {
    //   let item = {
    //     name: element.name,
    //     series: [
    //       {
    //         name: "Outstanding invoices",
    //         value: Math.floor(Math.random() * 1001),
    //       },
    //       {
    //         name: "Amount Spent",
    //         value: Math.floor(Math.random() * 1001),
    //       }
    //     ]
    //   }
    //   this.dataChart.push(item)
    // });
  }
}
