<p class="container-full py-[12px] text-text-primary text-headline-sm-bold">
  {{ labelDateSelected }}
</p>

@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
  <app-inno-spin />
</div>
} @else {
<div class="mt-[24px] flex flex-col gap-[36px]">
  <app-group-day-tracking [dataSource]="dataSource" />
</div>
@if(dataSource.length) {
<div class="container-full mt-[12px]">
  <app-inno-pagination
    [totalPages]="totalPages"
    (onChangePagesize)="handleChangePagesize($event)"
    (callbackGoToPage)="triggerRefreshListTimeTracking()" />
</div>
}
}
