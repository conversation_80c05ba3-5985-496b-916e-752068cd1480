import { Component, EventEmitter, Input, OnInit, Output, TemplateRef } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoSpinomponent } from '../inno-spin/inno-spin.component';
import { InnoEmptyDataComponent } from '../inno-empty-data/inno-empty-data.component';
import { AutofocusDirective } from 'app/directive/autofocusDirective.directive';

@Component({
  selector: 'app-inno-input-search-result',
  templateUrl: './inno-input-search-result.component.html',
  styleUrls: ['./inno-input-search-result.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoSpinomponent,
    InnoEmptyDataComponent,
    AutofocusDirective
  ]
})
export class InnoInputSearchResultComponent implements OnInit {

  @Input() public defaultValue: string = ''
  @Input() public placeholder: string = ''
  @Input() public data: any[] = []
  @Input() public optionTemplate: TemplateRef<any> | null = null
  @Input() public footerTemplate: TemplateRef<any> | null = null
  @Input() public isNotFound: boolean = false
  @Input() public isEmptyData: boolean = false
  @Input() public isLoading: boolean = false
  @Input() public isDisableSearch: boolean = false

  public inputSearchValue = ''

  @Output() onChange = new EventEmitter<any>();

  constructor() { }

  ngOnInit(): void {
    this.inputSearchValue = this.defaultValue
  }

  handleOnChange(event: any) {
    const textSearch = event?.target?.value ?? ''
    this.onChange.emit(textSearch)
    this.inputSearchValue = textSearch
  }
}
