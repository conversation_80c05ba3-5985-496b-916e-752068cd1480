import { Component, Input, TemplateRef } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';

@Component({
  selector: 'app-inno-collapse',
  templateUrl: './inno-collapse.component.html',
  styleUrls: ['./inno-collapse.component.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class InnoCollapseComponent {

  @Input() customTargetTemplate: TemplateRef<any> | null = null
  @Input() isExpanded: boolean = false

  constructor() {}

  toggle() {
    this.isExpanded = !this.isExpanded;
  }
}
