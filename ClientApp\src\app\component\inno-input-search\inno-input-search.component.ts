import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-inno-input-search',
  templateUrl: './inno-input-search.component.html',
  styleUrls: ['./inno-input-search.component.scss'],
  standalone: true,
  imports: [CommonModule, TranslateModule]
})
export class InnoInputSearchComponent {
  @Input() public height?: string;
  @Input() public value?: string;
  @Output() onChange = new EventEmitter<any>();

  constructor() { }

  handleOnChange(event: any) {
    this.onChange.emit(event?.target?.value ?? '')
  }
}
