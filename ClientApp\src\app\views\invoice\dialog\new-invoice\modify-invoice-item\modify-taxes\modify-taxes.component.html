<app-inno-modal-wrapper [title]="'TAX.AddTax'" (onClose)="handleClose()">
  <div class="p-[16px] w-full flex flex-col gap-[16px]">
    <!-- 
    @for( tax of listTax; track tax; let i = $index) {
    <app-input-tax [tax]="tax"
      [isHideDeleteButton]="true" />
    } -->

    @for( tax of taxes; track tax; let i = $index) {
    <app-input-tax [tax]="tax" (onSelectedTax)="handleSelectedTax($event, i)"
      (onDelete)="Delete(i)" />
    }

    <div class="flex justify-start">
      <button (click)="addTax()" class="button-link-primary">
        <img src="../../../../../../../assets/img/icon/ic_add_green.svg"
          alt="Icon">
        {{'TAX.AddAnother'|translate}}
      </button>
    </div>
    <hr>
    <div class="w-full mt-[10px]">
      <app-inno-form-checkbox
        [checked]="selectAll"
        (onChange)="handleChangeCheckAll($event)">
        {{'TAX.ApplyTax'|translate}}
      </app-inno-form-checkbox>
    </div>
  </div>
  <app-inno-modal-footer
    (onCancel)="handleCancel()"
    (onSubmit)="handleSubmit()" />
</app-inno-modal-wrapper>
