import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class SwitchWorkspaceDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/dialogs/switch-workspace/switch-workspace.component'
    );

    return this.matDialog.open(
      importedModuleFile.SwitchWorkspaceComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        width: '400px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
