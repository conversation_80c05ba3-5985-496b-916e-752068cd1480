<div class="w-full flex items-center justify-between gap-2 py-[12px] flex-wrap">
  <div class="flex items-center gap-[8px]">
    <p class="text-text-primary text-text-md-regular">
      Show row per page
    </p>
    <select class="dropdown-sm" (change)="handleChangePagesize($event)" >
      <option value="10" [selected]="currentPagesize === 10">10</option>
      <option value="20" [selected]="currentPagesize === 20">20</option>
      <option value="50" [selected]="currentPagesize === 50">50</option>
    </select>
  </div>
  <div class="flex items-center gap-[12px]">
    <p class="text-text-md-semibold text-text-primary">{{ currentPage }}<span class="text-text-tertiary">{{ ' of ' + totalPages }}</span></p>
    <div class="flex items-center gap-[2px]">
      @if(currentPage > 1) {
        <button class="button-icon" (click)="goToPage(currentPage - 1)" >
          <img src="../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
        </button>
      }
      @if(currentPage < totalPages) {
        <button class="button-icon" (click)="goToPage(currentPage + 1)" >
          <img src="../../../assets/img/icon/ic_arrow_right.svg" alt="Icon">
        </button>
      }
    </div>
  </div>
</div>
