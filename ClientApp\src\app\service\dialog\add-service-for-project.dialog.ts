

import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddServiceForProjectDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/dialogs/add-service-for-project/add-service-for-project.component'
        );

        return this.matDialog.open(
            importedModuleFile.AddServiceForProjectComponent.getComponent(),
            {
                panelClass: 'custom_dialog',
                width: "100%",
                maxWidth: "700px",
                data,
                disableClose: true,
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
