import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class ConfirmTimerDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../component/navigation/confirm-timer/confirm-timer.component'
    );

    return this.matDialog.open(
      importedModuleFile.ConfirmTimerComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        width: '400px',
        disableClose: true,
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
