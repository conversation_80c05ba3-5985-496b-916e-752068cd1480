import { Component, EventEmitter, Input, OnDestroy, Output, TemplateRef, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { Subscription } from 'rxjs';
import { IOptionFilterProjectOrClient } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { InnoFormCheckboxComponent } from '../inno-form-checkbox/inno-form-checkbox.component';
import { AddProjectDialog } from '../../service/dialog/add-project.dialog';

@Component({
  selector: 'app-inno-select-search-tags',
  templateUrl: './inno-select-search-tags.component.html',
  styleUrls: ['./inno-select-search-tags.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    AvatarModule,
    InnoPopoverComponent,
    InnoFormCheckboxComponent,
    InnoInputSearchResultComponent,
  ]
})
export class InnoSelectSearchTagsComponent implements OnDestroy {

  @Input() public templateTrigger: TemplateRef<any> | null = null
  @Input() public defaultTextSearch: string = ''
  @Output() public onSelect = new EventEmitter<IOptionFilterProjectOrClient>();

  public listOptionPreview: IOptionFilterProjectOrClient[] = []
  public listOptionOriginal: IOptionFilterProjectOrClient[] = []
  public isLoading: boolean = false

  protected unsubscribe: Subscription[] = [];

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor(private addProjectDialog: AddProjectDialog) { }

  loadData() { }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal
      .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)
    const result: any[] = [];
    this.listOptionPreview.forEach(element => {
      result.push(element);
      this.listOptionOriginal.filter(x => x.type == 'project').forEach((item: any) => {
        if (element.value == item.objectClient.id) {
          result.push(item)
        }

      });
    });
    this.listOptionPreview = result
  }

  handleCreateNewProject() {
    this.addProjectDialog.open({});
    this.handleCloseSearchResult()
  }

  public handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  handleChooseOption(item: IOptionFilterProjectOrClient) {
    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
