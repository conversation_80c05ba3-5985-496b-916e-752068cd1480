import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-done-invite',
  standalone: true,
  imports: [InnobookModalWrapperComponent],
  templateUrl: './done-invite.component.html',
  styleUrl: './done-invite.component.scss'
})
export class DoneInviteComponent {

  static getComponent(): typeof DoneInviteComponent {
    return DoneInviteComponent;
  }
  constructor(public dialogRef: MatDialogRef<DoneInviteComponent>) {

  }
  closeDialog() {
    this.dialogRef.close();
  }
  Done() {
    this.dialogRef.close("done");
  }
}
