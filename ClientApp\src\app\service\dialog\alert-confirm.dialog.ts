import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';


export interface IAlertConfirm {
  title?: string;
  description?: string;
  textSubmit?: string;
  textCancel?: string;
  classNameSubmitButton?: string;
}
@Injectable({ providedIn: 'root' })
export class AlertConfirmDialog extends AsyncDialog<any> {
  async open(data: IAlertConfirm): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../core/alert-confirm/alert-confirm.component'
    );

    return this.matDialog.open(
      importedModuleFile.AlertConfirmComponent.getComponent(),
      {
        data,
        width: '440px',
        panelClass: 'custom_dialog',
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
