import { ProjectDto } from "./project.dto";

export class ClientDto {
  public id!: string;
  public clientName: string;
  public companyId?: string;
  public firstName?: string;
  public lastName?: string;
  public emailAddress?: string;
  public phoneNumber?: string;
  public businessPhoneNumber?: string;
  public mobilePhoneNumber?: string;
  public country?: string;
  public addressLine1?: string;
  public addressLine2?: string;
  public townCity?: string;
  public stateProvince?: string;
  public postalCode?: string;
  public company?: any;
  public projects?: ProjectDto[];
  public timeTrackings?: any;
  public createdAt?: Date;
  public updatedAt?: Date;
  public createdBy?: string;
  public updatedBy?: string | null;

  public static fromJson(json: Record<string, any>): ClientDto {
    const client = new ClientDto();
    Object.assign(client, json);

    let bidon: object[] = json['projects'] ?? [];
    client.projects = bidon.map((jsonItem) => ProjectDto.fromJson(jsonItem));

    return client;
  }
}
