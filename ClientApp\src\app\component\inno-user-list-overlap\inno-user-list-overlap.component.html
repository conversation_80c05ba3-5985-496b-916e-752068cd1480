<div class="flex relative h-[24px]" [ngStyle]="{ width: maxWidth }">
  <div
    *ngFor="let item of avatars; let i = index"
    class="border-2 border-white rounded-full absolute"
    [ngStyle]="{
      width: avatarSize + 'px',
      height: avatarSize + 'px',
      left: i > 0 ? (i * realAvatarSize) + 'px' : '0px'
    }">
    @if(item.user?.firstName &&
    item.user?.lastName)
    {
    <ngx-avatars [size]="35"
      bgColor="{{_storeService.getBgColor(item.user?.firstName.slice(0,1))}}"
      [name]="item.user.firstName.slice(0,1)"></ngx-avatars>
    }
    @else{
    <ngx-avatars [size]="35"
      bgColor="{{_storeService.getBgColor(item.user.email.slice(0,1))}}"
      [name]="item.user.email.slice(0,1)"></ngx-avatars>
    }
  </div>

  @if(overLength > 0) {
  <div
    class="rounded-full flex justify-center items-center absolute bg-bg-secondary text-text-tertiary text-text-sm-semibold border-2 border-border-white"
    [ngStyle]="{
      width: avatarSize + 'px',
      height: avatarSize + 'px',
      left: (avatars.length * realAvatarSize) + 'px'
    }">
    +{{ overLength }}
  </div>
  }
</div>
