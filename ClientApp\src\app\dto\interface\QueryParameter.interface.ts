export interface Parameter {
    Page?: number,
    PageSize?: number,
    Search?: string,
    InvoiceId?: string,
    ProjectId?: string,
    ExpensesId?: string,
    Filter?: any
}

export interface TimeTrackingQueryParam extends Parameter {
    filterDate?: string;
    timeZone?: string;
    clientId?: string;
    loggedBy?: string;
    projectId?: string;
    startDate?: string;
    endDate?: string;
    isUnBill?: boolean;
    columnName?: string;
    direction?: string;
}

export interface ClientAndProjectQueryParam extends Parameter {
    active?: boolean;
    getAll?: boolean;
}

export interface GetAllUserBusinessQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
}

export interface GetExpenseQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
    filterDate?: string;
    timeZone?: string;
    clientId?: string;
    projectIds?: string[];
    status?: number;
    isGetAll?: boolean;
}

export interface GetUploadedExpensesQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
    timeZone?: string;
    expenseId?: string;
}

export interface GetClientQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
}

export interface GetEstimateQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
    isSendToMe?: boolean;
}

export interface GetInvoiceQueryParam extends Parameter {
    columnName?: string;
    direction?: string;
    isSendToMe?: boolean;
}

export interface GetProjectRequestParam extends Parameter {
    columnName?: string;
    direction?: string;
    filterDate?: string;
    timeZone?: string;
    isActive?: boolean;
    isArchive?: boolean;
}

export interface GetServiceRequestParam extends Parameter {
    columnName?: string;
    direction?: string;
    isInProject?: boolean;
}