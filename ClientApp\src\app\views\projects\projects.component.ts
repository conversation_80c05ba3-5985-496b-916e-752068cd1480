import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { DecimalPipe } from './../../pipes/decimal.pipe';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InnoSpinomponent } from './../../component/inno-spin/inno-spin.component';
import { InnoPopoverComponent } from './../../component/inno-popover/inno-popover.component';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../core/services/layout-utils.service';
import { ProjectService } from './../../service/project.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { DialogModule } from '@syncfusion/ej2-angular-popups';
import { HeaderSearchBarComponent } from 'app/component/header-search-bar/header-search-bar.component';
import { GridAllModule, GridComponent, PagerModule, Pager, PagerDropDown, ActionEventArgs, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { AddProjectFormComponent } from '../dialogs/add-project-form/add-project-form.component';
import { GetProjectRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { SharedModule } from 'app/module/shared.module';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoDatepickerComponent } from 'app/component/inno-datepicker/inno-datepicker.component';
import { InnoUserListOverlapComponent } from 'app/component/inno-user-list-overlap/inno-user-list-overlap.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { Role } from 'app/enum/role.enum';
import { CalculationResponse } from 'app/dto/interface/projectResponse.interface';
import { AddProjectDialog } from '../../service/dialog/add-project.dialog';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { formatDateFilter } from 'app/helpers/common.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [
    PagerModule,
    HeaderSearchBarComponent,
    InnoEmptyDataComponent,
    GridAllModule,
    SharedModule,
    DialogModule,
    MatMenuModule,
    AddProjectFormComponent,
    InnoInputSearchComponent,
    InnoDatepickerComponent,
    InnoUserListOverlapComponent,
    InnoTableActionComponent,
    InnoPopoverComponent,
    InnoSpinomponent,
    FormatNumberPipe,
    DecimalPipe,
  ],
  providers: [LayoutUtilsService],
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss'
})
export class ProjectsComponent implements OnInit, OnDestroy {
  public dateSelected?: Date;
  public sort: SortGird
  public Role = Role
  public isLoading: boolean = false
  private timeout: any
  private _subscriptions: Subscription[] = [];
  public search: string = ''
  public role!: string;
  private searchSubject = new Subject<string>();
  public idTime: any
  public columnName: string
  public direction: any
  public sortOptions: SortSettingsModel = { columns: [] };
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  public dataSource: any;
  public totalPages = 1;
  public ProjectResponse: CalculationResponse | null = null;
  public filterProjectDefault: number = 1
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  public listAvatar = [
    'https://picsum.photos/id/237/200/300',
    'https://picsum.photos/id/238/200/300',
    'https://picsum.photos/id/239/200/300',
  ]

  @ViewChild('grid') grid?: GridComponent;

  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
    public storeService: StoreService,
    private projectService: ProjectService,
    private toastService: ToastService,
    private addProjectDialog: AddProjectDialog,
  ) { }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize;
      this.handleCase(this.filterProjectDefault);
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }


  }
  GetAllProject(filter: any) {
    let query: GetProjectRequestParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      filterDate: formatDateFilter(this.dateSelected),
      ...filter,
    }
    this.isLoading = true;
    this.projectService.GetAllProjectAsync(query).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(
      {
        next: (res: any) => {
          this.totalPages = res.totalRecords
          const updatedfilterDates = res.data.map((item: any) => {
            let total = ""
            let totalunbilled = ""
            let totalbilled = 0
            let total_hours = 0;
            let total_minutes = 0;
            let total_hours_unbilled = 0;
            let total_minutes_unbilled = 0;
            item.timeTrackings.forEach((element: any) => {
              if (element.billable == true) {
                const [hours, minutes] = element.endTime.split(":").map(Number);
                total_hours_unbilled += hours;
                total_minutes_unbilled += minutes;
                if (total_minutes_unbilled >= 60) {
                  total_hours_unbilled += Math.floor(total_minutes_unbilled / 60);
                  total_minutes_unbilled = 0
                }
                totalunbilled = (total_hours_unbilled < 10 ? "0" + total_hours_unbilled : total_hours_unbilled) + ":" + (total_minutes_unbilled < 10 ? "0" + total_minutes_unbilled : total_minutes_unbilled)
              }
              const [hours, minutes] = element.endTime.split(":").map(Number);
              total_hours += hours;
              total_minutes += minutes;
              if (total_minutes >= 60) {
                total_hours += Math.floor(total_minutes / 60);
                total_minutes = 0
              }
              total = (total_hours < 10 ? "0" + total_hours : total_hours) + ":" + (total_minutes < 10 ? "0" + total_minutes : total_minutes)
            });
            item.invoices.forEach((element: any) => {
              totalbilled += element.totalAmount
            });
            return { ...item, totalLogger: total, totalunbilled: totalunbilled, totalbilled: totalbilled };

          });


          this.dataSource = updatedfilterDates;
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };
          }

        },
        complete: () => {
          this.isLoading = false
        }
      }
    )
  }


  handleSelectStatusProject($event: any) {
    this.currentPage = 1;
    this.filterProjectDefault = Number.parseInt(($event.target as HTMLSelectElement).value)
    this.handleCase(Number.parseInt(($event.target as HTMLSelectElement).value))

  }
  handleCase(caseValue: number, sort?: SortGird): void {
    switch (caseValue) {
      case 3:
        let filterArchive = {
          isArchive: true,
          ...this.sort
        }
        this.GetAllProject(filterArchive);
        break;
      case 2:
        let filterUnActive = {
          isActive: false,
          ...this.sort
        }
        this.GetAllProject(filterUnActive);
        break;
      default: let filterActive = {
        isActive: true,
        ...this.sort
      }
        this.GetAllProject(filterActive);

    }
  }
  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  CalculationProject() {
    this.projectService.CalculationProject().subscribe({
      next: (res) => {
        this.ProjectResponse = res;
      }
    });
  }
  ngOnInit(): void {
    this.CalculationProject();
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      if (search) {
        this.search = search;
        this.handleCase(this.filterProjectDefault);

      }
      else {
        this.search = "";
        this.handleCase(this.filterProjectDefault);
      }
    });

    this._subscriptions.push(sb)

    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        this.sort ? this.handleCase(this.filterProjectDefault, this.sort) : this.handleCase(this.filterProjectDefault);
      }
      else {
        this.handleCase(this.filterProjectDefault);
      }

    });

    this.storeService.getRoleBusinessAsObservable().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      this.role = res;

    }
    )
  }


  creaFormDelete(projectId: string) {
    const _title = this.translate.instant('PROJECT.DeleteProject');
    const _description = this.translate.instant('COMMON.ConfirmDelete');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      this.projectService.DeleteProject(projectId, false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.CalculationProject();
          this.handleCase(this.filterProjectDefault)
          this.toastService.showSuccess(this.translate.instant('TOAST.Delete'), this.translate.instant('TOAST.Success'));
        }
        else {
          this.toastService.showError(this.translate.instant('TOAST.Fail'), this.translate.instant('TOAST.Fail'));
        }
      })
    })
  }

  OpenDialog() {
    const dialogRef = this.addProjectDialog.open(null);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        let filterActive = {
          isActive: true,
          ...this.sort
        }
        if (res) {
          this.CalculationProject();
          this.GetAllProject(filterActive);
        }
      })
    });
  }

  handleEdit(idProject: string) {
    const dialogRef = this.addProjectDialog.open(idProject);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.sort ? this.handleCase(this.filterProjectDefault, this.sort) : this.handleCase(this.filterProjectDefault);
        }
      })
    });
  }

  handleDelete(item: any, isActive: boolean) {

    if (!item) return
    const _title = this.translate.instant(isActive ? 'PROJECT.ActiveProject' : 'PROJECT.DeleteProject');
    const _description = this.translate.instant(isActive ? 'PROJECT.DescriptionActive' : 'COMMON.ConfirmDelete');
    let dialog;
    if (isActive) {
      dialog = this.layoutUtilsService.alertConfirm({
        title: _title,
        description: _description,
        textSubmit: "Active",
      })
    } else {
      dialog = this.layoutUtilsService.alertDelete({
        title: _title,
        description: _description
      })
    }

    dialog.then(isConfirm => {
      if (!isConfirm) return

      this.projectService.DeleteProject([item.id], isActive).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.CalculationProject();
          this.handleCase(this.filterProjectDefault);
          this.toastService.showSuccess(isActive ? this.translate.instant("TOAST.Active") : this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
        }
        else {
          this.toastService.showError(this.translate.instant('TOAST.Fail'), this.translate.instant('TOAST.Fail'));
        }
      })
    })
  }

  handleArchive(item: any, isArchive: boolean) {
    this.projectService.UpdateArchive([item.id], isArchive).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.CalculationProject();
        this.handleCase(this.filterProjectDefault)
        this.toastService.showSuccess(this.translate.instant('TOAST.Update'), this.translate.instant('TOAST.Success'));
      }
      else {
        this.toastService.showError(this.translate.instant('TOAST.Fail'), this.translate.instant('TOAST.Fail'));
      }
    })
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.handleCase(this.filterProjectDefault, this.sort)
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.handleCase(this.filterProjectDefault)
    }
  }
  handleChangeDateFilter(dateSelected: any) {
    let filter = {
      isActive: true,
      ...this.sort
    }
    this.dateSelected = dateSelected;
    this.GetAllProject(filter);
  }
  ngOnDestroy(): void {
    if (this._subscriptions) {

      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
    if (this.idTime) {
      clearTimeout(this.idTime)
    }
  }
}
