import { Component, inject, Input } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { StoreService } from 'app/service/store.service';
import { AvatarModule } from 'ngx-avatars';

@Component({
  selector: 'app-inno-user-list-overlap',
  templateUrl: './inno-user-list-overlap.component.html',
  styleUrls: ['./inno-user-list-overlap.component.scss'],
  standalone: true,
  imports: [SharedModule, AvatarModule]
})
export class InnoUserListOverlapComponent {
  @Input() avatars: any[] = [];
  @Input() overLength: number = 0;
  public _storeService = inject(StoreService)
  avatarSize: number = 40;
  space: number = 10;
  realAvatarSize: number = this.avatarSize - this.space;
  maxWidth: string = '';

  constructor() { }

  ngOnInit(): void {
    this.calculateMaxWidth();
  }

  calculateMaxWidth(): void {
    let allAvatarWidth = this.avatars.length * this.realAvatarSize;
    if (this.overLength > 0) {
      allAvatarWidth += this.avatarSize;
    }
    this.maxWidth = `${allAvatarWidth}px`;
  }
}
