import { DecimalPipe } from './../../pipes/decimal.pipe';
import { Component, DestroyRef, inject, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { MatDialog } from '@angular/material/dialog';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { debounceHandler } from 'app/helpers/common.helper';
import { Subscription } from 'rxjs';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { StoreService } from 'app/service/store.service';
import { InvoiceService } from 'app/service/invoice.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { CalculationResponse } from 'app/dto/interface/projectResponse.interface';
import { AddEstimateDialog } from 'app/service/dialog/add-estimate.dialog';
import { EstimateSendToMeComponent } from './estimate-send-to-me/estimate-send-to-me.component';
import { EstimateManagementComponent } from './estimate-management/estimate-management.component';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-estimate',
  standalone: true,
  providers: [],
  imports: [
    SharedModule,
    InnoTabsComponent,
    InnoInputSearchComponent,
    EstimateSendToMeComponent,
    EstimateManagementComponent,
    FormatNumberPipe,
    DecimalPipe
  ],
  templateUrl: './estimate.component.html',
  styleUrl: './estimate.component.scss'

})
export class EstimateComponent implements OnInit, OnDestroy {

  public totalDraft: number = 0;
  public invoiceView = InvoiceViewEnum;
  public currentTypeView?: InvoiceViewEnum;
  public calculationInvoice: CalculationResponse | null = null
  public textSearch: string = '';
  public listTypeView = [
  ]

  private router = inject(Router);
  private _subscriptions: Subscription[] = [];
  private dataService = inject(DataService)
  private _storeService = inject(StoreService)
  public translate = inject(TranslateService)
  private _invoiceService = inject(InvoiceService)
  private addEstimateDialog = inject(AddEstimateDialog)
  private destroyRef = inject(DestroyRef);
  constructor() {
    this.listTypeView = [
      { label: this.translate.instant('INVOICES.Tabs.Created'), value: InvoiceViewEnum.Created_Tab },
      { label: this.translate.instant('INVOICES.Tabs.SentToMe'), value: InvoiceViewEnum.Sent_To_Me_Tab },
    ]
  }
  ngOnInit(): void {
    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().subscribe((data) => {
        this.currentTypeView = data?.typeView
        if (this.currentTypeView == InvoiceViewEnum.Created_Tab) {
          this.CalculationEstimate();
          return
        }
        this.CalculationEstimateSendToMe();
      })
    )
  }

  CalculationEstimateSendToMe() {
    this._invoiceService.CalculationEstimateSendToMe(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.calculationInvoice = res;
      }
    });
  }

  CalculationEstimate() {
    this._invoiceService.CalculationEstimate(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.calculationInvoice = res;
      }
    });
  }

  NewEstimate() {
    const dialogRef = this.addEstimateDialog.open(null)
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {

        }
      })
    });
  }
  removeQueryParams() {
    this.router.navigate([], {
      queryParams: {},
      replaceUrl: true,
    });
  }
  handleChangeTypeView(_typeView: InvoiceViewEnum) {
    this.removeQueryParams();
    const currentTypeView = this.dataService.GetInvoiceFilterValue().typeView
    if (currentTypeView === _typeView) return
    this.dataService.SetNewInvoiceFilter({ typeView: _typeView })
  }

  handleSearch = debounceHandler((textSearch = '') => {
    this.dataService.SetNewInvoiceFilter({ textSearch })
  }, 1000)

  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
