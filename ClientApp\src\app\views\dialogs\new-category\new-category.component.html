<app-innobook-modal-wrapper (onClose)="closeDialog()">
    <h4 class="w-full text-center"> {{'CATEGORY.TitleAdd'| translate}}</h4>
    <div class="p-3">
        <div>
            @if(listCategory.length>0)
            {
            <span (click)="RemoveAll()"
                class="text-blue-500 ml-2 cursor-pointer">
                {{'CATEGORY.RemoveAll'| translate}}
            </span>
            }
        </div>
        <hr>
        @for(sr of listCategory ; track sr; let
        i=$index)
        {
        <div class="relative group w-full border rounded-md p-2 mb-2">
            <div class="flex items-center w-full justify-between">
                {{sr.categoryName}}
                <div
                    (click)="RemoveService(i)"
                    class=" hidden group-hover:block text-end cursor-pointer">
                    <span class="material-icons">
                        delete_forever
                    </span>
                </div>
            </div>

        </div>
        }
        <div>
            <div class="mb-2">
                @if(isAddSvcMode) {
                <input type="text"
                    [(ngModel)]=categoryName
                    class="bg-white  border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    [placeholder]="'CATEGORY.GIRD.CategoryName'| translate" />
                }
                @if(categoryName)
                {
                <div (click)="CreatedCategory()"
                    class="bg-white max-w-68 flex items-center p-2 cursor-pointer hover:bg-gray-400 text-blue-500">
                    <span class="material-icons">
                        add
                    </span> {{'CATEGORY.CreateCategory'| translate}}
                    <span>" {{categoryName}} "</span>

                </div>
                }

            </div>

            <button type="button" (click)="addCategory()"
                class="btn btn-outline-secondary btn-dashed">
                <span class="material-icons">add</span>
                {{'CATEGORY.AddCateGory'| translate}}
            </button>

        </div>
        <div class="w-full flex justify-around mt-3">
            <button (click)="closeDialog()" type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">
                {{'BUTTON.Cancel'| translate}}</button>

            <button (click)="Save()" type="button"
                [disabled]="listCategory.length==0"
                [ngClass]="{'bg-green-700 hover:bg-green-800 text-white' :listCategory.length>0,'bg-gray-400':listCategory.length==0}"
                class="text-gray-900 focus:outline-none hover:bg-gray-300 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">{{'BUTTON.Save'|
                translate}}</button>

        </div>
    </div>
</app-innobook-modal-wrapper>