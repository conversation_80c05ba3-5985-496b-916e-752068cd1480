@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
  <app-inno-spin size="lg" />
</div>
}@else {
@if(dataSource?.length) {
<div class="w-full mt-[12px]"
  [ngClass]="{ 'mb-28':_storeService.getIsRunning() }">
  <ejs-grid
    #grid
    class="customTable"
    (rowDrop)="onDragStop($event)"
    [allowRowDragAndDrop]='true'
    [allowSorting]="true"
    (rowDragStart)="onDragStart()"
    [sortSettings]='sortOptions'
    (actionBegin)="onActionBegin($event)"
    [dataSource]="dataSource">
    <e-columns>
      <e-column [headerText]="'INVOICES.GIRD.InvoiceNumber' | translate"
        width="180" field="invoiceNumber">
        <ng-template #template let-data>
          <div class="w-full cursor-pointer" (click)="handleEdit(data)">
            <p class="text-text-primary text-text-md-semibold">
              {{ data.invoiceNumber ?? '' }}
            </p>
            <p class="text-text-tertiary text-text-xs-regular">
              {{ data.notes ?? '' }}
            </p>
          </div>
        </ng-template>
      </e-column>
      <e-column [headerText]="'INVOICES.GIRD.Clients' | translate" width="250"
        field="ClientName">
        <ng-template #template let-data>
          <div class="w-full flex gap-[12px] items-center">
            <ngx-avatars
              class="shrink-0"
              [size]="40"
              [name]="data?.client?.clientName" />
            <div class="w-full ">
              <p class="text-text-primary text-text-md-semibold">
                {{ data?.client?.clientName ?? '' }}
              </p>
              @if(data?.client?.emailAddress) {
              <p class="text-text-tertiary text-text-xs-regular">
                {{ data?.client?.emailAddress ?? '' }}
              </p>
              }
            </div>
          </div>
        </ng-template>

      </e-column>
      <e-column [headerText]="'INVOICES.GIRD.IssuedDate' | translate"
        width="150" field="invoiceDate">
        <ng-template #template let-data>
          <span>{{data.invoiceDate | date: _storeService.getdateFormat()
            }}</span>
        </ng-template>
      </e-column>
      <e-column [headerText]="'INVOICES.GIRD.DueDate' | translate" width="150"
        field="dueDate">
        <ng-template #template let-data>
          <span>{{data.dueDate | date: _storeService.getdateFormat() }}</span>
        </ng-template>
      </e-column>
      <e-column [headerText]="'INVOICES.GIRD.Status' | translate" width="120"
        field="status">
        <ng-template #template let-data>
          <app-inno-status [status]="data.status" />
        </ng-template>
      </e-column>
      <e-column [headerText]="'INVOICES.GIRD.Amount' | translate" width="120"
        field="totalAmount">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-bold">
            ${{data.totalAmount | decimal:2 | formatNumber}}
          </p>
        </ng-template>
      </e-column>
      @if(authenticationService.getBusinessRole()!=Role.Accountant)
      {
      <e-column field='action' width="100">
        <ng-template #template let-item>
          <app-inno-table-action
            (onEdit)="handleEdit(item)"
            (onDelete)="handleDelete(item)" />
        </ng-template>
      </e-column>
      }
    </e-columns>

  </ejs-grid>
  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>
} @else {
<div class="container-full mt-[24px]">
  <app-inno-empty-data
    title="No active invoice at the moment"
    description="You can create a new invoice" />
</div>
}
}
