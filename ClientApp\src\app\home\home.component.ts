import {
  Component,
  OnInit,
  OnD<PERSON>roy
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../module/shared.module';
import { MatMenuModule } from '@angular/material/menu';
import { MatDatepicker } from '@angular/material/datepicker';
import { DatePickerModule } from '@syncfusion/ej2-angular-calendars';
import {MatDatepickerModule} from '@angular/material/datepicker';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import { provideNativeDateAdapter } from '@angular/material/core';


@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  providers: [provideNativeDateAdapter()],
  imports: [
    RouterModule,
    SharedModule,
    MatMenuModule,
    DatePickerModule,
    MatFormFieldModule, MatInputModule, MatDatepickerModule
  ]
})
export class HomeComponent implements OnInit, OnDestroy {
  private unsubscribe: Subscription[] = [];

  constructor(
  ) { }

  ngOnInit() {
  }
  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }

}
