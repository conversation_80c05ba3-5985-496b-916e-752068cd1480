import { inject, Injectable } from "@angular/core";
import { TimeTrackingQueryParam } from "app/dto/interface/queryParameter.interface";
import { formatDateFilter, isValidDateRange } from "app/helpers/common.helper";
import { TimetrackingService } from "app/service/timetracking.service";
import { firstValueFrom } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class GenerateInvoiceProvider {

  private timeTrackingService = inject(TimetrackingService)

  fetchListProjectByClientAndDate = async (args: {
    clientId: string,
    startDate?: Date,
    endDate?: Date
    page?: number
  }) => {
    const {
      clientId,
      startDate,
      endDate,
      page
    } = args

    if (!clientId) return null;

    let params: TimeTrackingQueryParam = {
      Page: page,
      PageSize: 20,
      Search: "",
      Filter: { clientId },
    }

    if (startDate && endDate) {
      const isValidDate = isValidDateRange(startDate, endDate)
      if (!isValidDate) return null;
      params = {
        ...params,
        startDate: formatDateFilter(startDate),
        endDate: formatDateFilter(endDate),
      }
    }
    const res = await firstValueFrom(this.timeTrackingService.GetAllTimeTracking(params));
    return res ?? []
  }


}
