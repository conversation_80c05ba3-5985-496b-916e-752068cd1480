p {
  margin-bottom: 0;
}

.buttonCollapse {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: rotate(180deg);
}

.buttonCollapse.active {
  transform: rotate(0deg);
}

.overlayMobile {
  width: 100dvw;
  height: 100dvh;
  position: fixed;
  top: 0;
  left: 0;
  display: none;
}

.sidebar {
  width: 0;
  opacity: 0;
  transition: all .3s;
  pointer-events: none;
}

.sidebar.active {
  width: 192px;
  opacity: 1;
  transition: all .3s;
  pointer-events: unset;
}

@media screen and (max-width: 860px) {
  .horizontalSidebar {
    position: relative;
    z-index: 2;
  }

  .sidebar {
    position: absolute;
    left: 48px;
    z-index: 2;
  }

  .sidebar.active  ~ .overlayMobile {
    display: block;
    background-color: transparent;
    z-index: 1;
  }

}

.listMenu {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menuItem {
  width: 100%;
  display: flex;
  gap: 12px;
  padding: 10px 12px;
  cursor: pointer;
  border-radius: 8px;
  @apply text-text-white text-text-sm-medium;
  transition: all .3s;
  text-align: left;
}

.menuItem.active,
.menuItem:hover {
  @apply bg-bg-brand-strong-hover;
  transition: all .3s;
}

.menuItem.active {
  @apply text-text-sm-bold;
}

.menuItem.active.hasSubMenu {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.submenu {
  display: none;
  padding: 10px 12px;

}

.submenu .menuItem:hover,
.submenu .menuItem.active {
  @apply bg-bg-brand-strong;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}

.menuItem.active~.submenu {
  display: flex;
  @apply bg-bg-brand-strong-hover;
  padding-left: 30px;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}


.btnSwitchLanguage {
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
}

.btnSwitchLanguage .wrapIcon {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 50%;
}

.btnSwitchLanguage .wrapIcon .text {
  color: #ffffff;
  text-transform: uppercase;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.btnSwitchLanguage .txtLang {
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #ffffff;
  display: none;
  display: inline;
}

.btnSwitchLanguage img {
  width: 16px;
  height: 12px;
  object-fit: cover;
}

.activeSubSidebar {
  display: flex;
}

.img-icon-fr {
  background-image: url('../../../assets/img/svg/fr.svg');
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-left: 20px;
  height: 16px;
}

.img-icon-en {
  background-image: url('../../../assets/img/svg/en.svg');
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-left: 20px;
  height: 16px;
}

.e-dropdown-popup ul .e-item {
  align-items: center !important;
}
