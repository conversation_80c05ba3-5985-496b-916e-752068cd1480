import { InnoSpinomponent } from './../../component/inno-spin/inno-spin.component';
import { SortGird } from './../../dto/interface/SortGird.interface';
import { CalculationResponse } from 'app/dto/interface/projectResponse.interface';
import { DecimalPipe } from './../../pipes/decimal.pipe';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InnoPopoverComponent } from './../../component/inno-popover/inno-popover.component';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../core/services/layout-utils.service';
import { SwitchWorkspaceComponent } from 'app/views/dialogs/switch-workspace/switch-workspace.component';
import { StoreService } from 'app/service/store.service';
import { ClientService } from './../../service/client.service';
import { SharedModule } from './../../module/shared.module';
import { Component, DestroyRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { GridAllModule, PagerModule, Pager, PagerDropDown, GridComponent, ActionEventArgs, SortSettingsModel, DataStateChangeEventArgs } from '@syncfusion/ej2-angular-grids';
import { AddClientsFormComponent } from '../dialogs/add-clients-form/add-clients-form.component';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetClientQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { SwitchWorkspaceDialog } from '../../service/dialog/switch-workspace.dialog';
import { AddClientsDialog } from '../../service/dialog/add-clients.dialog';
import { AvatarModule } from 'ngx-avatars';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-clients',
  standalone: true,
  imports: [
    GridAllModule,
    PagerModule,
    SharedModule,
    MatMenuModule,
    AvatarModule,
    AddClientsFormComponent,
    SwitchWorkspaceComponent,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InnoTableActionComponent,
    InnoPopoverComponent,
    InnoEmptyDataComponent,
    InnoSpinomponent,
    FormatNumberPipe,
    DecimalPipe,
  ],
  providers: [LayoutUtilsService],
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.scss'
})
export class ClientsComponent implements OnInit, OnDestroy {
  public sort: SortGird
  public isLoading: boolean = false
  private _subscriptions: Subscription[] = [];
  public sortOptions: SortSettingsModel = { columns: [] };
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  public TYPE_TAB = {
    ALL_CLIENTS: 1,
    MAIL_HISTORY: 2,
  }
  public tabs: any[] = [];
  public currentTab = this.TYPE_TAB.ALL_CLIENTS
  public dataSource: any;
  public search: string = ''
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  private searchSubject = new Subject<string>();
  private timeoutId: any;
  public columnName: string
  public direction: any
  @ViewChild('grid') grid?: GridComponent;
  public clientResponse: CalculationResponse | null = null;
  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public storeService: StoreService,
    private clientService: ClientService,
    private toastService: ToastService,
    private destroyRef: DestroyRef,
    private switchWorkspaceDialog: SwitchWorkspaceDialog,
    private addClientsDialog: AddClientsDialog
  ) { }

  handleChangeTab(value: any) {
    this.currentTab = value;
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllClient();
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }
  }

  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  CalculationClient() {
    this.clientService.CalculationClient().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      this.clientResponse = res
    }
    )
  }
  ngOnInit(): void {
    this.tabs = [
      {
        label: this.translate.instant('CLIENT.AllClient'),
        value: this.TYPE_TAB.ALL_CLIENTS
      },
      {
        label: this.translate.instant('CLIENT.MailsHistory'),
        value: this.TYPE_TAB.MAIL_HISTORY
      }
    ];
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page;
      }
      this.GetAllClient();

    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      this.search = search ?? "";
      this.GetAllClient();
    });
    this._subscriptions.push(sb)
    this.CalculationClient();
  }

  GetAllClient() {
    this.isLoading = true;
    let query: GetClientQueryParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      ...this.sort
    }

    this.clientService.GetAllClient(query).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.isLoading = false;
        this.totalPages = res.totalRecords
        this.dataSource = res.data
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };
        }


      }
    }
    )

  }

  OpenDialogNewBusiness() {
    const dialogRef = this.switchWorkspaceDialog.open({});

    dialogRef.then((c) => {
      c.afterClosed().subscribe((value) => {
        if (this.storeService.getChooseBusiness()) {
          window.location.reload()
        }
      })
    })
  }

  NewClient() {
    this.storeService.getChooseBusiness() ? this.OpenDialog() : this.OpenDialogNewBusiness();
  }

  OpenDialog() {
    const dialogRef = this.addClientsDialog.open(null);
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) this.GetAllClient()
      })
    });
  };

  clearTimeout() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

  }

  handleEditClient(item: any) {

    const dialogRef = this.addClientsDialog.open(item.id);;
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetAllClient();
        }
      })
    });
  }

  handleDeleteClient(item: any) {
    this.layoutUtilsService.alertDelete({
      title: this.translate.instant('CLIENT.DeleteClient'),
      description: this.translate.instant('COMMON.ConfirmDelete')
    }).then(isConfirm => {
      if (!isConfirm) return

      this.clientService.DeleteClient([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.GetAllClient();
          this.toastService.showSuccess(this.translate.instant('TOAST.Delete'), this.translate.instant('TOAST.Success'));
          this.CalculationClient();
        }
      })
    })
  }

  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction
      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetAllClient();
        return;
      }
      this.sort = null
      this.sortOptions = {
        columns: []
      };
      this.GetAllClient();
    }
  }

  ngOnDestroy() {
    this.clearTimeout();
    if (this._subscriptions) {

      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
