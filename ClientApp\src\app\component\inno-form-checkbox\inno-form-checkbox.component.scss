.customCheckboxHTML {
  transform: translateY(1px);
  width: 16px;
  height: 16px;
  appearance: none;
  border: 1px solid;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
  @apply rounded-xs;
  @apply bg-object-white;
  @apply border-border-secondary;
}

.customCheckboxHTML:checked {
  @apply bg-object-brand-primary;
  @apply border-object-brand-primary;
}

.customCheckboxHTML::before {
  content: "\2713";
  position: absolute;
  font-weight: 700;
  font-size: 10px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.3s;
  @apply text-border-white;
}

.customCheckboxHTML:checked::before {
  transform: translate(-50%, -50%) scale(1);
  transition: all 0.3s;
}
