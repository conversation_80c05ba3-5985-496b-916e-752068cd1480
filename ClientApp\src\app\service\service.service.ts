import { environment } from 'environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { GetServiceRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { Observable } from 'rxjs';
import { ICreateService, Service } from '../dto/interface/service.interface';
import { formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class ServiceService {


  private http = inject(HttpClient)
  constructor() { }

  GetAllService(params: GetServiceRequestParam): Observable<PaginatedResponse<Service>> {
    const query = formatParamsQuery(params, false);
    return this.http.get<PaginatedResponse<Service>>(UrlApi + `/Service/GetAllService`, { params: query });
  }

  CreateService(payload: ICreateService): Observable<Service> {
    return this.http.post<Service>(UrlApi + '/Service/CreateService', payload);
  }
  GetServiceById(id: string): Observable<Service> {
    return this.http.get<Service>(UrlApi + `/Service/GetServiceById?id=${id}`);
  }

  DeleteServices(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Service/DeleteServices', payload);
  }
  UpdateArchive(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Service/UpdateArchive', payload);
  }
  Update(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Service/Update', payload);
  }
}
