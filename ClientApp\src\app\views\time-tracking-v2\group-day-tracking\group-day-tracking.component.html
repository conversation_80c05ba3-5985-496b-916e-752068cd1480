@if(dayName) {
<p class="container-full py-[12px] text-text-primary text-headline-sm-bold">
  {{ dayName }}
</p>
}

@if(dataSource?.length) {
<div class="w-full overflow-auto">
  <div class="gridLayoutWrapper w-full min-w-[900px]">
    @for(item of dataSource; track item) {
    <div class="gridLayout bg-bg-primary">
      <div class="grid grid-cols-3">

        <div class="w-full">
          <div class="w-full text-text-secondary text-text-md-semibold">
            {{ item.client?.clientName ?? '' }}
          </div>
          @if(item.description) {
          <p
            class="text-text-tertiary text-text-sm-regular whitespace-pre-wrap">
            {{ item.description }}
          </p>
          }
        </div>
        <div class="w-full text-text-secondary text-text-md-semibold">
          {{ item.project?.projectName ?? '' }}
        </div>
        <div class="w-full text-text-secondary text-text-md-semibold">
          {{ item.service?.serviceName ?? '' }}
        </div>
      </div>

      <div class="w-full">
        <app-inno-tags value="IOS platform, Web platform, Only InnoBooks" />
      </div>
      <div class="w-full">
        <app-inno-status
          [status]="item.isBilled?4:item.billable ? 1 : 2" class="mr-2" />
      </div>
      <div class="w-full">
        <p class="text-text-primary text-text-md-semibold">
          @if(item?.user?.firstName)
          {
          <div class="flex items-center">
            <ngx-avatars
              matTooltip="{{item?.user?.firstName}} {{item?.user?.lastName}} "
              [size]="35"
              bgColor="{{_storeService.getBgColor(item?.user?.firstName.slice(0,1))}}"
              [name]="item?.user.firstName.charAt(0) +' '+ (item?.user?.lastName ? item?.user?.lastName.charAt(0) : '')" />
            <span class="pl-1 line-clamp-1"> {{item?.user?.firstName}}
              {{item?.user?.lastName}}</span>
          </div>
          }
          @else{
          <div class="flex items-center">
            <ngx-avatars
              matTooltip="{{item?.user?.email}}"
              [size]="35"
              bgColor="{{_storeService.getBgColor(item?.user?.email.slice(0,1))}}"
              [name]="item?.user?.email.slice(0,1)" />
            <span class="pl-1 line-clamp-1"> {{item?.user?.email}}</span>
          </div>

          }
        </p>
      </div>
      <div class="w-full">
        <p class="text-text-primary text-headline-xs-bold">
          {{ item.endTime }}
        </p>
      </div>
      <div class="w-full flex justify-end">
        <app-inno-table-action
          (onResume)="handleResume(item)"
          (onEdit)="handleEdit(item)"
          (onDelete)="handleDelete(item)" />
      </div>
    </div>
    }
  </div>
</div>
} @else {
@if(!isDisableShowEmpty) {
<div class="container-full py-[24px] flex flex-col items-center">
  <img class="h-[120px]" src="../../../../assets/img/clock.png" alt="Icon">
  <div class="w-full">
    <p class="text-text-tertiary text-headline-xs-semibold text-center">
      {{'TIMETRACKING.NoTime'|
      translate }}
    </p>
    <p class="text-text-tertiary text-text-sm-regular text-center">
      {{'TIMETRACKING.DescribeNoTime'|
      translate }}
    </p>
  </div>
</div>
}
}
