.linkRedirect {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #0089EF;
    text-decoration: none;
    cursor: pointer;
}

.linkRedirect:hover {
    text-decoration: underline;
}

.pageLogin {
    width: 100vw;
    height: 100vh;
    background-image: url("../../../assets/img/bg_login.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;

    @media screen and (max-width: 860px) {
        transform: unset;
        width: 100vw;
    }

    p {
        margin-bottom: 0;
    }

    // .inputLabelerrors .mat-mdc-form-field-error {
    //   width: 100%;
    //   position: absolute;
    //   font-size: 13px;
    //   line-height: 18px;
    //   margin-top: 5px;
    //   font-weight: 400;
    //   bottom: -5px;
    //   left: 0;
    //   transform: translateY(100%);
    // }

    .linkRedirect {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #0089ef;
        text-decoration: none;
        cursor: pointer;
    }

    .linkRedirect:hover {
        text-decoration: underline;
    }

    .wrapForm {
        width: 90%;
        padding: 32px;
        background: #ffffff;
        border-radius: 16px;
        max-width: 420px;
    }

    .wrapForm .wrapLogo {
        height: auto;
        margin-left: auto;
        margin-right: auto;
        width: 100%;
        display: block;
        justify-content: center;
        align-items: center;
        background-color: #9199af;
    }

    .wrapForm .wrapLogo .logo {
        height: 100%;
        width: auto;
        object-fit: contain;
        background: #ffffff;
    }

    .formLogin {
        width: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .formLogin .wrapTitleForm {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .formLogin .wrapTitleForm .titleForm {
        font-weight: bold;
        font-size: 28px;
        line-height: 36px;
        text-align: center;
        color: #0f182e;
        width: 100%;
    }

    .wrapTitleForm .desForm {
        font-weight: 500;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        color: #9199af;
    }

    .wrapInputForm {
        width: 100%;
        margin-top: 15px;
        display: flex;
        flex-direction: column;
        gap: 14px;
    }

    .rowRemember {
        margin-top: 6px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 10px;
        align-items: center;
    }

    .rowRemember .customCheckbox {
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #0f182e;
    }

    .actionForm {
        margin-top: 15px;
        width: 100%;
    }

    .actionForm .buttonActionItem.colorPrimary {
        width: 100%;
        height: 48px;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
    }

    .actionForm .textFooterForm {
        font-weight: 500;
        font-size: 14px;
        line-height: 18px;
        color: #0f182e;
        text-align: center;
        margin-top: 34px;
    }

    .actionForm .textFooterForm .linkRedirect {
        margin-left: 5px;
    }
}