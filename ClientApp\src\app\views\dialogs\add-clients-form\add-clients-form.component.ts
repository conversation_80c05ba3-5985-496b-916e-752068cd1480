import { ClientService } from './../../../service/client.service';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, FormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { ToastService } from 'app/service/toast.service';
import { SpinnerService } from 'app/service/spinner.service';
import { COUNTRIES } from 'app/utils/country-items';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { StoreService } from 'app/service/store.service';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { Client } from 'app/dto/interface/client.interface';

@Component({
  selector: 'app-add-clients-form',
  standalone: true,
  imports: [
    SharedModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormInputComponent,
    InnoFormSelectSearchComponent
  ],
  providers: [],
  templateUrl: './add-clients-form.component.html',
  styleUrl: './add-clients-form.component.scss'
})
export class AddClientsFormComponent implements OnInit {
  public newclientForm!: FormGroup;
  public countriesOption: IFilterDropdownOption[] = COUNTRIES.map(item => ({
    value: item.code,
    label: item.name
  }));
  objectClient: Client

  public _storeService = inject(StoreService)
  private _spinnerService = inject(SpinnerService)
  private destroyRef = inject(DestroyRef);
  private _clientService = inject(ClientService)
  private _toastService = inject(ToastService)
  private formBuilder = inject(UntypedFormBuilder)

  static getComponent(): typeof AddClientsFormComponent {
    return AddClientsFormComponent;
  }

  constructor(public dialogRef: MatDialogRef<AddClientsFormComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.newclientForm = this.formBuilder.group({
      clientname: [
        "",
        Validators.compose([Validators.required])],
      email: [
        "",
        Validators.compose([
          Validators.required,
          Validators.email
        ])],
      mobilephone: [
        "",
        Validators.compose([
          Validators.pattern('[0-9]*'),
          Validators.minLength(10),
          Validators.maxLength(10)
        ])],
      businessphone: [
        "",
        Validators.compose([
          Validators.pattern('[0-9]*'),
          Validators.minLength(10),
          Validators.maxLength(10)]
        )],
      phone: [
        "",
        Validators.compose([
          Validators.pattern('[0-9]*'),
          Validators.minLength(10),
          Validators.maxLength(10)
        ])],
      firstname: [""],
      lastname: [""],
      country: [''],
      address1: [''],
      address2: [''],
      tow_city: [''],
      state_province: [''],
      postal_code: [''],
      postePhoneNumber: [''],
      posteBusinessPhoneNumber: [''],
      posteMobilePhoneNumber: [''],
    });
  }
  ngOnInit(): void {
    if (this.data) {
      this.GetClientById(this.data)
    }
  }
  handleData(data: Client) {
    this.f["clientname"].setValue(data.clientName),
      this.f["firstname"].setValue(data.firstName),
      this.f["lastname"].setValue(data.lastName),
      this.f["email"].setValue(data.emailAddress),
      this.f["phone"].setValue(data.phoneNumber),
      this.f["businessphone"].setValue(data.businessPhoneNumber),
      this.f["mobilephone"].setValue(data.mobilePhoneNumber),
      this.f["address1"].setValue(data.addressLine1),
      this.f["address2"].setValue(data.addressLine2),
      this.f["tow_city"].setValue(data.townCity),
      this.f["state_province"].setValue(data.stateProvince),
      this.f["postal_code"].setValue(data.postalCode),
      this.f["country"].setValue(data.country),
      this.f["postePhoneNumber"].setValue(data.postePhoneNumber),
      this.f["posteBusinessPhoneNumber"].setValue(data.posteBusinessPhoneNumber),
      this.f["posteMobilePhoneNumber"].setValue(data.posteMobilePhoneNumber)
  }

  GetClientById(id) {
    this._clientService.GetClientById(id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        this.objectClient = res;
        this.handleData(res)
      }
      )

  }
  handleClose() {
    this.dialogRef.close();
  }
  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  onSubmit() {
    if (this.newclientForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }

    const payload: Record<string, any> = {
      clientName: this.f["clientname"].value,
      firstName: this.f["firstname"].value,
      lastName: this.f["lastname"].value,
      emailAddress: this.f["email"].value,
      phoneNumber: this.f["phone"].value,
      businessPhoneNumber: this.f["businessphone"].value,
      mobilePhoneNumber: this.f["mobilephone"].value,
      addressLine1: this.f["address1"].value,
      addressLine2: this.f["address2"].value,
      townCity: this.f["tow_city"].value,
      stateProvince: this.f["state_province"].value,
      postalCode: this.f["postal_code"].value,
      country: this.f["country"].value,
      postePhoneNumber: this.f["postePhoneNumber"].value,
      posteBusinessPhoneNumber: this.f["posteBusinessPhoneNumber"].value,
      posteMobilePhoneNumber: this.f["posteMobilePhoneNumber"].value,

    }

    if (this.data) {
      this._spinnerService.show();
      payload['id'] = this.data
      this._clientService.UpdateClient(payload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(res => {
          if (res) {
            this._toastService.showSuccess("Save", "Create Success")
            this.dialogRef.close(res);
          } else {
            this._toastService.showError("Fail", "Client name already exists")
          }
          this._spinnerService.hide();
        })
    } else {
      this._spinnerService.show();
      this._clientService.CreateClient(payload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(res => {
          if (res) {
            this._toastService.showSuccess("Save", "Create Success")
            this.dialogRef.close(res);
          } else {
            this._toastService.showError("Fail", "Client name already exists")
          }
          this._spinnerService.hide();
        })
    }
  }

  get f() {
    return this.newclientForm.controls as Record<string, FormControl>;;
  }

  closeDialog() {
    this.dialogRef.close();
  }

  handleCancel() {
    this.dialogRef.close();
  }
}
