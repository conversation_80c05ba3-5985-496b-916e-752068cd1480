import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { ToastService } from 'app/service/toast.service';
import { Component, DestroyRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { GridComponent, GridModule, PagerModule, Pager, PagerDropDown, GridAllModule, ActionEventArgs, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { ActivatedRoute, Router } from '@angular/router';
import { StoreService } from 'app/service/store.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetAllUserBusinessQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { UserBusinessService } from 'app/service/user-business.service';
import { AvatarModule } from 'ngx-avatars';
import { Role } from 'app/enum/role.enum';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { AddMemberBusinessDialog } from '../../../service/dialog/add-member-business.dialog';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-member',
  standalone: true,
  imports: [
    GridAllModule,
    PagerModule,
    MatMenuModule,
    AvatarModule,
    SharedModule,
    GridModule,
    InnoTableActionComponent,
    InnoInputSearchComponent,
    InnoSpinomponent
  ],
  providers: [LayoutUtilsService],
  templateUrl: './member.component.html',
  styleUrl: './member.component.scss'
})
export class MemberComponent implements OnInit, OnDestroy {
  public isLoading = false;
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  private _subscriptions: Subscription[] = [];
  public search: string = ''
  private searchSubject = new Subject<string>();
  public data?: object[];
  public columnSelection = false;
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any
  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
    public storeService: StoreService,
    private userbusiness_services: UserBusinessService,
    private toastService: ToastService,
    private addMemberBusinessDialog: AddMemberBusinessDialog
  ) { }

  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
      }
      this.GetAllMemberInBusiness(this.currentPage);

    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      if (search) {
        this.search = search;
      }
      this.GetAllMemberInBusiness(this.currentPage)
    });
    this._subscriptions.push(sb)
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllMemberInBusiness(this.currentPage)
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }
  RouterDetail(id: string) {
    this.router.navigate([`/members/detail/${id}`])
  }
  GetAllMemberInBusiness(page: number) {
    this.isLoading = true
    let query: GetAllUserBusinessQueryParam = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      ...this.sort,
    }
    this.userbusiness_services.GetAllUserBusiness(query).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.totalPages = res.totalRecords
        this.dataSource = res.data
        this.isLoading = false
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };

        }

      }
    }
    )
  }


  OpenDialog() {

    const dialogRef = this.addMemberBusinessDialog.open({});
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetAllMemberInBusiness(this.currentPage)
        }
      });
    });
  }

  creaFormDelete(data: any) {
    const _title = this.translate.instant('TEAMMEMBERS.DeleteMember');
    const _description = this.translate.instant('COMMON.ConfirmDelete');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      this.userbusiness_services.DeleteMemberInBusiness(data.user.id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          if (res) {
            this.GetAllMemberInBusiness(this.currentPage)
            this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
          }
        }
      });
    })
  }

  RouterInvite(member: any) {
    const encodedUrl = `email=${encodeURIComponent(member?.user.email)}&firstName=${encodeURIComponent(member?.user.firstName)}&lastName=${encodeURIComponent(member?.user.lastName)}&role=${encodeURIComponent(member?.role)}`;
    this.router.navigateByUrl(`invite-members?${encodedUrl}`);
  }


  handleDelete(item: any) {
    this.creaFormDelete(item);
  }

  handleArchive(item: any) {
  }
  GetFullName(data: any) {
    return data.firstName + " " + data.lastName
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetAllMemberInBusiness(this.currentPage)
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllMemberInBusiness(this.currentPage)
    }

  }
  ngOnDestroy(): void {

    if (this._subscriptions) {
      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
