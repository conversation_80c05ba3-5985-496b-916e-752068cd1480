<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
    <div
        class="container-full flex justify-between items-center flex-wrap gap-2">
        <div class="flex items-center gap-[8px]">
            <p class="text-text-primary text-headline-lg-bold">
                Invoice {{ InforInvoice?.invoiceNumber ?? '' }}
            </p>
            <app-inno-status [status]="InforInvoice?.status" />
        </div>

        <div class="flex items-center gap-[12px] flex-wrap">
            <app-inno-popover
                position="bottom-end"
                [content]="templateSearchProject">
                <button target class="button-size-md button-outline">
                    <img
                        src="../../../../assets/img/icon/ic_three_dot_horizontal.svg"
                        alt="icon">
                    More
                </button>
            </app-inno-popover>
            <ng-template #templateSearchProject>
                <div class="block">

                    <div class="flex flex-col gap-[2px]">
                        <button (click)="handleDownloadPDF()"
                            class="button-size-md button-transparent">
                            <img
                                src="../../../../assets/img/icon/ic_download.svg"
                                alt="icon">
                            Download PDF
                        </button>

                    </div>
                </div>
            </ng-template>
        </div>
    </div>
</div>

<div class="container-full mt-[24px]">
    <div class="w-full w-fit mx-auto bg-bg-primary p-[32px] relative">
        <div class="flex w-full gap-[18px] md:flex-row flex-col">
            <!-- Thumbnail -->
            <div class="w-[160px] h-[100px] shrink-0 mx-auto md:mx-[unset]">
                <img class="rounded-md w-full h-full object-cover"
                    [src]="imageUrl"
                    onerror="this.src='../../../../assets/img/image_default.svg'"
                    alt="image">
            </div>

            <div class="w-full flex flex-col gap-[16px]">
                <div class="w-full">
                    @if(!businessInfo.businessName)
                    {
                    <p class="text-text-md-semibold text-text-primary mb-[1px]">
                        Loading...
                    </p>
                    }
                    <p
                        class="text-text-secondary text-text-sm-semibold mb-[2px] font-bold">{{InforInvoice?.company?.businessName}}
                    </p>
                    @if(businessInfo.businessPhoneNumber) {
                    <div class="flex items-center gap-[8px]">
                        <p class="text-text-sm-regular text-text-secondary">
                            {{ businessInfo.businessPhoneNumber | phoneMask }}
                        </p>
                    </div>
                    }
                    <p class="w-full text-text-sm-regular text-text-secondary">
                        {{InforInvoice?.company?.email
                        }}
                    </p>

                    @if(businessInfo.businessAddress){
                    <p class="w-full text-text-sm-regular text-text-secondary">
                        {{ businessInfo.businessAddress }}
                    </p>
                    }
                    <p class="w-full text-text-sm-regular text-text-secondary">
                        {{InforInvoice?.company?.country }}
                    </p>
                </div>

                <div
                    class="w-full grid sm:grid-cols-2 lg:grid-cols-4 gap-[16px]">
                    <div class="w-full">
                        <p
                            class="text-text-brand-primary text-text-sm-semibold mb-[2px] ">Billed
                            to</p>
                        <p class="text-text-secondary text-text-md-regular">
                            {{ InforInvoice?.client?.clientName ?? '' }}
                        </p>
                        <p class="text-text-secondary text-text-md-regular">
                            {{ businessInfoClient?.businessAddress ?? '' }}
                        </p>

                    </div>
                    <div class="w-full">
                        <p
                            class="text-text-brand-primary text-text-sm-semibold mb-[2px]">Invoice
                            Number</p>
                        <p class="text-text-secondary text-text-md-regular">
                            {{ InforInvoice?.invoiceNumber ?? '' }}
                        </p>
                    </div>
                    <div class="w-full">
                        <p
                            class="text-text-brand-primary text-text-sm-semibold mb-[2px]">Date
                            of Issue</p>
                        <p class="text-text-secondary text-text-md-regular">
                            {{ selectedDate }}
                        </p>
                    </div>
                    <div class="w-full">
                        <p
                            class="text-text-brand-primary text-text-sm-semibold mb-[2px]">Due
                            Date</p>
                        <p class="text-text-secondary text-text-md-regular">
                            {{ selectedDueDate }}
                        </p>
                    </div>
                </div>

                <div class="w-full">
                    <p
                        class="text-text-brand-primary text-text-sm-semibold mb-[2px] ">Description</p>
                    <p
                        class="text-text-secondary text-text-md-regular whitespace-pre-line">
                        {{ InforInvoice?.notes ?? '' }}
                    </p>
                </div>
            </div>
        </div>

        <div
            class="w-full mt-[16px] border-t border-dashed border-border-primary">
            <div class="overflow-auto w-full">
                <div class="invoiceTableLayout">
                    <p
                        class="text-text-brand-primary text-text-sm-semibold font-bold">
                        Invoice Item
                    </p>
                    <p
                        class="text-text-brand-primary text-text-sm-semibold font-bold">
                        Rate
                    </p>
                    <p
                        class="text-text-brand-primary text-text-sm-semibold font-bold">
                        Quantity
                    </p>
                    <p
                        class="text-text-brand-primary text-text-sm-semibold text-right font-bold">
                        Line Total
                    </p>
                </div>
                <div class="overflow-auto w-full">
                    @for(pay of InforInvoice?.itemInvoices;track pay; let i =
                    $index) {

                    <div class="invoiceTableLayout">

                        <div class=" flex flex-col">
                            <!-- <p class="text-text-primary text-text-md-semibold">
                  {{ pay?.project?.projectName ?? '' }}
                </p>
                @if(pay?.item)
                {
                <p class="text-text-primary text-text-md-semibold">
                  {{ pay?.item?.itemName?? '' }}
                </p>
                }
                <p class="text-text-primary text-text-md-regular">
                  @if(pay
                  ?.service?.serviceName)
                  {
                  {{ pay
                  ?.service?.serviceName ?? '' }} -
                  }
                  {{getFullName(pay?.user)}} -
                  {{pay.dateSelectItem | date:'MMM, d y'}}
                </p> -->
                            <ul class="list-disc" class="flex items-center">
                                <span
                                    class="material-icons cursor-pointer mr-3 !text-[8px]">
                                    fiber_manual_record
                                </span>
                                <p
                                    class="text-text-primary text-text-md-regular  whitespace-pre-line">
                                    {{ pay?.description ?? '' }}
                                </p>
                            </ul>

                        </div>
                        <p class="text-text-primary text-text-md-regular">
                            ${{pay.rate | formatNumber}}
                        </p>
                        <div
                            class="text-text-primary text-text-md-regular flex flex-col">
                            <p>
                                {{ pay.qty | decimal:2 | formatNumber}}
                            </p>
                            <p> {{ getNameTaxes(pay?.taxes, true) }}</p>
                        </div>

                        <p
                            class="text-text-primary text-text-md-bold text-right">
                            ${{calculateTotal(pay) | decimal:2 | formatNumber}}

                        </p>
                    </div>
                    }
                </div>
            </div>
        </div>

        <div class="w-full flex flex-col items-end mt-[16px]">
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Subtotal
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{subtotal | decimal:2 | formatNumber}}
                </p>
            </div>
            <!--  list taxs -->
            @for(tax of taxArray; track tax ;let i=$index)
            {
            <div class="flex justify-end items-start gap-[8px] mb-2">
                <div class=" flex  flex-col pl-2">
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        {{tax.name}} ({{tax.amount}}%)
                    </p>
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        #{{tax.numberTax}}
                    </p>
                </div>

                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ tax.total | decimal:2 | formatNumber }}
                </p>
            </div>
            }
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Tax
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ CheckIsNaN(sumtax) | decimal:2 | formatNumber }}
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Discount
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    $0
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Amount Due ({{_storeService.curencyCompany | async}})
                </p>
                <p
                    class="text-text-primary text-headline-md-bold text-right w-[160px] shrink-0">
                    ${{ InforInvoice?.totalAmount | decimal:2 | formatNumber }}
                </p>
            </div>
        </div>

        <img class="invoiceDetailsFooter"
            src="../../../../assets/img/bg_footer_invoice_details.png"
            alt="Footer">
    </div>
</div>
