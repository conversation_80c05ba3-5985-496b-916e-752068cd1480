export interface RoleMenu {
    name: string,
    value: string;
    depression: string;
    role: any[]
}
export const RoleMenu: RoleMenu[] = [
    { name: "Admin", value: 'Admin', depression: "Full access to your InnoBook account", role: [{ text: "Clients", check: true }, { text: "Invoices", check: true }, { text: "Reports", check: true }, { text: "Expenses", check: true }, { text: "Time Tracking", check: true }, { text: "Projects", check: true }, { text: "Payments", check: true }] },
    { name: "Manager", value: 'Manager', depression: "Full access to your InnoBook account with exceptions", role: [{ text: "Clients", check: true }, { text: "Invoices", check: true }, { text: "Reports", check: true }, { text: "Expenses", check: true }, { text: "Time Tracking", check: true }, { text: "Projects", check: true }, { text: "Payments", check: true }] },
    { name: "Employee", value: 'Employee', depression: "Track their own time and expenses", role: [{ text: "Clients", check: true }, { text: "Invoices", check: true }, { text: "Reports", check: false }, { text: "Expenses", check: false }, { text: "Time Tracking", check: true }, { text: "Projects", check: false }, { text: "Payments", check: true }] },
    { name: "Contractor", value: 'Contractor', depression: "Track their own time and expenses and send you invoices", role: [{ text: "Clients", check: false }, { text: "Invoices", check: true }, { text: "Reports", check: false }, { text: "Expenses", check: false }, { text: "Time Tracking", check: true }, { text: "Projects", check: false }, { text: "Payments", check: false }] },
    { name: "Accountant(Free)", value: 'Accountant', depression: "Access reports, expenses and create journal entries", role: [{ text: "Clients", check: false }, { text: "Invoices", check: true }, { text: "Reports", check: false }, { text: "Expenses", check: true }, { text: "Time Tracking", check: false }, { text: "Projects", check: false }, { text: "Payments", check: false }] },
];