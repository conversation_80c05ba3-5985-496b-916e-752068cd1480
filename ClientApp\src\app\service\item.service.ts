import { PaginatedResponse } from './../dto/interface/paginatedResponse.interface';
import { environment } from 'environments/environment';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { Item } from 'app/dto/interface/Item.interface';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class ItemService {

  private http = inject(HttpClient)
  constructor() { }

  GetAllItem(payload: Parameter): Observable<PaginatedResponse<Item>> {
    return this.http.post<PaginatedResponse<Item>>(UrlApi + `/Item/GetAllItem`, payload);
  }
  GetItemById(id: string): Observable<Item> {
    return this.http.get<Item>(UrlApi + `/Item/GetItemById?id=${id}`);
  }
  CreateItem(payload: any): Observable<Item> {
    return this.http.post<Item>(UrlApi + '/Item/CreateItem', payload);
  }
  DeleteItem(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Item/DeleteItem', payload);
  }
  // UpdateArchive(payload: any): Observable<boolean> {
  //   return this.http.post<boolean>(UrlApi + '/Service/UpdateArchive', payload);
  // }
  Update(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Item/Update', payload);
  }
}
