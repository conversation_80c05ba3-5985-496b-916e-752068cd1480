import { Component, Input } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';

@Component({
  selector: 'app-inno-tags',
  templateUrl: './inno-tags.component.html',
  styleUrls: ['./inno-tags.component.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class InnoTagsComponent {

  @Input() public placeholder: string = 'Add tags';
  @Input() public value: string = '';

  constructor() {}
}
