import { StoreService } from './../../../service/store.service';
import { Component, DestroyRef, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { InnoDatepickerComponent } from 'app/component/inno-datepicker/inno-datepicker.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { InnoSelectSearchTagsComponent } from 'app/component/inno-select-search-tags/inno-select-search-tags.component';
import { InnoSelectSearchUserComponent } from 'app/component/inno-select-search-user/inno-select-search-user.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { Role } from 'app/enum/role.enum';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { SharedModule } from 'app/module/shared.module';
import { DataService } from 'app/service/data.service';

@Component({
  selector: 'app-filter-tracking',
  templateUrl: './filter-tracking.component.html',
  styleUrls: ['./filter-tracking.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoSelectSearchProjectComponent,
    InnoSelectSearchUserComponent,
    InnoSelectSearchTagsComponent,
    InnoDatepickerComponent
  ]
})
export class FilterTrackingComponent implements OnInit {
  public role!: string;
  public isAll: boolean = false;
  @Output() onCancel = new EventEmitter<any>();

  public timeTrackingView = TimeTrackingViewEnum;
  public projectIdSelected?: string;
  public projectLabelSelected?: string;
  public clientIdSelected?: string;
  public clientLabelSelected?: string;
  public userIdSelected?: string;
  public userLabelSelected?: string;
  public startDateSelected?: Date;
  public endDateSelected?: Date;
  public typeViewSelected?: TimeTrackingViewEnum;

  private dataService = inject(DataService)
  private storeService = inject(StoreService)
  destroyRef = inject(DestroyRef);
  constructor() { }

  ngOnInit(): void {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    this.projectIdSelected = currentFilter.projectSelected
    this.clientIdSelected = currentFilter.clientSelected;
    this.userIdSelected = currentFilter.userSelected
    this.startDateSelected = currentFilter.startDate
    this.endDateSelected = currentFilter.endDate
    this.typeViewSelected = currentFilter.typeView,
      this.clientLabelSelected = currentFilter.clientLabelSelected
    this.storeService.getRoleBusinessAsObservable().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      this.role = res;
      if (this.role == Role.Admin || this.role == Role.Manager) {
        this.isAll = true;
        return;
      }
    })
  }

  handleSelectProject(item: IFilterDropdownOption) {
    let projectIdSelected = undefined
    let projectLabelSelected = undefined
    let clientIdSelected = undefined
    let clientLabelSelected = undefined

    if (item?.metadata?.type == 'client') {
      clientIdSelected = item.value
      clientLabelSelected = item.label
    }

    if (item?.metadata?.type == 'project') {
      clientLabelSelected = item.metadata.objectClient.clientName
      projectIdSelected = item.value
      projectLabelSelected = item.label
    }
    this.projectIdSelected = projectIdSelected
    this.projectLabelSelected = projectLabelSelected
    this.clientIdSelected = clientIdSelected
    this.clientLabelSelected = clientLabelSelected

  }

  handleSelectUser(item: IFilterDropdownOption) {
    this.userIdSelected = item.value
    this.userLabelSelected = item.label == '' ? item.metadata.email : item.label
  }

  handleCancel() {
    this.onCancel.emit()
  }

  handleApply() {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const newValueFilter = {
      ...currentFilter,
      clientSelected: this.clientIdSelected ?? undefined,
      projectSelected: this.projectIdSelected ?? undefined,
      userSelected: this.userIdSelected ?? undefined,
      startDate: this.startDateSelected ?? undefined,
      endDate: this.endDateSelected ?? undefined,
      clientLabelSelected: this.clientLabelSelected
    }
    this.dataService.SetNewTimeTrackingFilter(newValueFilter)
    this.onCancel.emit()
  }

  handleChangeStartDate(value: any) {
    this.startDateSelected = value
  }

  handleChangeEndDate(value: any) {
    this.endDateSelected = value
  }

  handleResetDefault() {
    this.projectIdSelected = undefined
    this.projectLabelSelected = undefined
    this.clientIdSelected = undefined
    this.clientLabelSelected = undefined
    this.userIdSelected = undefined
    this.userLabelSelected = undefined
    this.startDateSelected = undefined
    this.endDateSelected = undefined
  }
}
