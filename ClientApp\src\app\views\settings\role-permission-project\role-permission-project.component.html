<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <div class="flex items-center gap-[8px]">
      <button class="button-icon button-size-md" (click)="handleBack()">
        <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
      </button>
      <p class="text-text-primary text-headline-lg-bold">
        {{'ROLE.RolePermission'|translate}}
      </p>
    </div>
  </div>
</div>

<div class="container-full mt-[24px]">
  <app-inno-tabs
    [tabs]="tabs"
    [value]="currentTab"
    (onChange)="handleChangeTab($event)" />
</div>

<div class="w-full mt-[24px]">
  @if(currentTab === TYPE_TAB.USER_GROUP) {
  <div class="container-full">
    <div class="flex justify-between">
      <h6>
        {{'ROLE.UserGroupAuthorization'|translate}}
      </h6>
      <button type="button"
        (click)="OpenDialog()"
        class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">New</button>
    </div>
    <hr>

  </div>
  } @else if(currentTab === TYPE_TAB.USER) {
  <!-- <div class="p-3">
        <p class="fw-bold fs-6 m-0">All Sent Emails</p>
        <div class="text-center">
            <h5>Subscribe to View Sent Emails</h5>
            <button class="btn btn-primary">Subscribe</button>
        </div>
    </div> -->
  } @else if(currentTab === TYPE_TAB.ROLE) {
  <ejs-grid [dataSource]="dataSource"
    [allowPaging]="false" [allowSorting]="true"
    [allowFiltering]="false">
    <e-columns>
      <e-column field="roleName" [headerText]="'ROLE.GIRD.RoleName' | translate"
        width="70">
      </e-column>
      <e-column field="description"
        [headerText]="'ROLE.GIRD.Description' | translate" width="70">
      </e-column>
      <e-column field="code" [headerText]="'ROLE.GIRD.Code' | translate"
        width="100">
      </e-column>
    </e-columns>
  </ejs-grid>
  }
</div>
