import { ToastService } from 'app/service/toast.service';
import { SpinnerService } from 'app/service/spinner.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ConfirmPasswordValidator } from 'app/auth/register/confirm-password.validator';
import { User } from 'app/dto/interface/user.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SharedModule } from 'app/module/shared.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TIMEZONE, timezoneMapping } from 'app/utils/timezone';
import { JwtHelperService } from '@auth0/angular-jwt';
import { StoreService } from 'app/service/store.service';
import { catchError } from 'rxjs';

@Component({
  selector: 'app-activate-member',
  standalone: true,
  imports: [SharedModule, MatFormFieldModule,
    RouterModule],
  templateUrl: './activate-member.component.html',
  styleUrl: './activate-member.component.scss'
})
export class ActivateMemberComponent implements OnInit {
  listCompany: any[] = []
  public isValidating = true;
  protected activatedRoute = inject(ActivatedRoute);
  public showPassword!: boolean;
  public registerForm!: UntypedFormGroup;
  destroyRef = inject(DestroyRef);
  private router = inject(Router)
  private auth_services = inject(AuthenticationService)
  private spinnerService = inject(SpinnerService)
  private formBuilder = inject(UntypedFormBuilder)
  private _toastService = inject(ToastService)
  private _storeService = inject(StoreService)
  token: string = '';

  constructor(
  ) {

    this.registerForm = this.formBuilder.group({
      firstname: ["", Validators.compose([Validators.required])],
      lastname: ["", Validators.compose([Validators.required])],
      email: ["", Validators.compose([Validators.required, Validators.email])],
      password: ["", Validators.compose([Validators.required, Validators.minLength(6)])],
      confirmPassword: ["", Validators.compose([Validators.required])],
      // company: ["", Validators.compose([Validators.required])],
      agree: [false, Validators.compose([Validators.required])],
    },
      {
        validator: ConfirmPasswordValidator("password", "confirmPassword")
      }
    );

  }
  togglePassword() {
    const passwordInput = document.getElementById("password") as HTMLInputElement;
    const showIcon = document.getElementById("showIcon");
    const hideIcon = document.getElementById("hideIcon");

    if (passwordInput?.type === "password") {
      passwordInput.type = "text";
      showIcon?.classList.remove("!hidden");
      hideIcon?.classList.add("!hidden");
    } else {
      passwordInput.type = "password";
      showIcon?.classList.add("!hidden");
      hideIcon?.classList.remove("!hidden");
    }
  }
  ngOnInit() {
    this.spinnerService.show();
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.email) {
        this.registerForm.controls["email"].setValue(params.email);
        this.registerForm.controls["email"].disable();
      }
      if (params?.firstName) {
        this.registerForm.controls["firstname"].setValue(params.firstName);
      }
      if (params?.lastName) {
        this.registerForm.controls["lastname"].setValue(params.lastName);
      }
      if (params?.token) {
        this.token = params.token;
      }
      if (params?.token && params?.email) {
        this.auth_services.ValidateInvitation({ email: params.email, token: params.token })
          .pipe(
            takeUntilDestroyed(this.destroyRef),
            catchError((err) => {
              this.spinnerService.hide();
              this.isValidating = false;
              return err;
            })
          )
          .subscribe((res: any) => {
            this._storeService.setChooseBusiness({ businessId: res?.businessId });
            // Check user has account
            if (res?.validAccount) {
              // Account already logged in and have the same userId with invited user
              if (this.auth_services.getAccessToken() && res?.userId === this.auth_services.getIdUser()) {
                this.router.navigate(['/']);
              } else {
                // Logout current user
                this.auth_services.logout();

                // Navigate to login page to login with invited user
                this.router.navigate(['/login']);
              }
            }
            this.isValidating = false;
            this.spinnerService.hide();
          });
      }
    });
  }



  get f() {
    return this.registerForm.controls;
  }
  onSubmit() {
    const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const userZoneCode = timezoneMapping[userTimeZone] || userTimeZone;
    
    const userZone = TIMEZONE.find(zone => zone.Moment.includes(userZoneCode));
    this.spinnerService.show();
    let payload: User = {
      firstName: this.registerForm.controls["firstname"].value,
      lastName: this.registerForm.controls["lastname"].value,
      email: this.registerForm.controls["email"].value,
      password: this.registerForm.controls["password"].value,
      token: this.token,
      timeZoneId: userZone?.Code
    }
    this.auth_services.Register(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.spinnerService.hide();
        this.auth_services.saveToken_cookie(res.accessToken, res.refreshToken);
        const jwtHelper = new JwtHelperService();
        const jwt = jwtHelper.decodeToken(res.accessToken);
        
        this.router.navigate(['/']);
      }
      else {
        this.spinnerService.hide();
        this._toastService.showError("Email already exists", "Error")
      }
    }
    )
  }
}
