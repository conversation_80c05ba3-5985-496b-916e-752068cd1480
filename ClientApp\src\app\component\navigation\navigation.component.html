 <div class="relative h-dvh flex">

  <!-- NAVIGATION BAR -->
  <div
    class="w-[48px] h-full bg-bg-brand-strong-hover shrink-0 horizontalSidebar">
    <div class="w-full h-full pt-[10px] flex flex-col items-center">
      <button class="buttonCollapse" [class.active]="isOpenSidebar"
        (click)="handleToggleSidebar()">
        <img class="w-[20px]"
          src="../../../assets/img/icon/ic_collapse_sidebar.svg" alt="Icon">
      </button>

      <div class="w-full flex flex-col grow overflow-auto mt-[20px]">
        <div class="overflow-auto">
          <div [class.activeSubSidebar]="!isOpenSidebar"
            class="w-full hidden flex-col gap-[12px]">
            @for(groupMenuItem of listGroupMenu; track groupMenuItem.group;let i
            = $index){
            @if(i != 0) {
            <hr class="w-[80%] mx-auto">
            }
            <div class="w-full flex flex-col items-center gap-[8px]">
              @if(groupMenuItem?.menus?.length) {
              @for(item of groupMenuItem.menus; track item.id){
              <button
                [matTooltip]="item.text | translate"
                matTooltipPosition="right"
                [routerLink]="item.url"
                class="w-[32px] h-[32px] rounded-md flex justify-center items-center hover:bg-bg-brand-strong flex-shrink-0"
                [ngClass]="{'bg-bg-brand-strong': item.active!!}">
                <img class="w-[20px]"
                  src="../../../assets/img/icon/{{ item.icon }}" alt="Icon">
              </button>
              }
              }
            </div>
            }
          </div>
        </div>
        <div class="grow flex justify-center items-center pt-[24px]">
          <img class="w-[16px]" src="../../../assets/img/logo_verticle.png"
            alt="Logo">
        </div>
      </div>

      <div class="py-[24px] flex flex-col items-center gap-[12px]">
        <button
          class="w-[32px] h-[32px] rounded-md flex justify-center items-center hover:bg-bg-brand-strong">
          <img class="w-[20px]" src="../../../assets/img/icon/ic_question.svg"
            alt="Icon">
        </button>
        <button
          class="w-[32px] h-[32px] rounded-md flex justify-center items-center hover:bg-bg-brand-strong">
          <img class="w-[20px]"
            src="../../../assets/img/icon/ic_notification.svg" alt="Icon">
        </button>
        <button
          class="w-[32px] h-[32px] rounded-full flex justify-center items-center"
          ejs-dropdownbutton [items]="userOptions"
          (select)='handleClickOnUser($event)'
          iconCss="e-icons e-user user-icon" cssClass="e-caret-hide">
        </button>
      </div>
    </div>
  </div>

  <!-- SIDEBAR -->
  <div [class.active]="isOpenSidebar"
    class="sidebar h-full flex flex-col bg-bg-brand-strong">

    <!-- Select company -->
    <div (click)="openOtherWorkSpace()"
      class="px-[24px] py-[15px] w-full border-b border-border-in-tertiary cursor-pointer">
      <div class="flex w-full items-center gap-[12px] ">
        <p class="line-clamp-1 text-text-md-bold text-text-white">
          {{ objectBusiness?.businessName ?? 'WelCome InnoBook' }}
        </p>
        <img class="shrink-0 w-[20px]"
          src="../../../assets/img/icon/ic_arrow_down_white.svg"
          alt="Select company" />
      </div>
      <p class="line-clamp-1 text-text-xs-regular text-text-brand-tertiary">
        @if(objectBusiness) {
        {{ objectBusiness.role + (objectBusiness.isOwner? '
        (' + ('COMMON.Owner' | translate) + ')' : '') }}
        } @else {
        Create business now
        }
      </p>
    </div>

    <!-- List menu -->
    <div class="mt-[20px] px-[12px] grow overflow-auto">
      <div class="w-full flex flex-col gap-[16px]">

        @for(groupMenuItem of listGroupMenu; track groupMenuItem.group;){
        <div class="w-full">

          @if(groupMenuItem?.group) {
          <p
            class="px-[12px] !mb-[4px] text-[12px] font-bold uppercase text-text-brand-tertiary">
            {{ groupMenuItem.group }}
          </p>
          }

          @if(groupMenuItem?.menus?.length) {
          <div class="listMenu">
            @for(item of groupMenuItem.menus; track item.id;){
            <div class="w-full">
              <button class="menuItem"
                (click)="ClearCache(item.id)"
                [class.hasSubMenu]="item.children?.length!!"
                [class.active]="item.active!!" [routerLink]="item.url">
                <img class="w-[20px] shrink-0"
                  src="../../../assets/img/icon/{{ item.icon }}"
                  alt="icon menu">
                {{ item.text | translate}}

                @if(item.children?.length) {
                <img (click)="expand(item, $event)"
                  class="ml-auto w-[20px] shrink-0"
                  src="../../../assets/img/icon/ic_arrow_down_white.svg"
                  alt="icon menu">
                }
              </button>

              @if(item.children?.length) {
              <div class="listMenu submenu" [class.active]="item.expand!!">
                @for(child of item.children; track child;) {
                <button class="menuItem" [routerLink]="child.url">
                  {{ child.text | translate}}
                </button>
                }
              </div>
              }
            </div>
            }
          </div>
          }
        </div>
        }

      </div>
    </div>

    <!-- Switch language -->
    <div class="w-full p-[12px]">
      <button class="btnSwitchLanguage" ejs-dropdownbutton [items]="languages"
        cssClass="e-caret-hide"
        (select)='selectLanguage($event)'
        [attr.aria-label]="('Zipplex' | translate) + ' ' + ('Language' | translate)">
        <div class="wrapIcon">
          @if (currentLanguage === 'English') {
          <span>EN</span>
          }
          @if (currentLanguage === 'Français') {
          <span>FR</span>
          }
        </div>
        <span class="txtLang">
          {{ currentLanguage }}
        </span>
      </button>
    </div>
  </div>

  <div class="overlayMobile" (click)="handleToggleSidebar()"></div>
</div>
