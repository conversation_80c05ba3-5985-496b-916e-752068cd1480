<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <p class="text-text-primary text-headline-lg-bold">
      {{'ITEMS_SERVICES.Title' |translate}}
    </p>

    <app-inno-popover
      position="bottom-start"
      [content]="contentCreateNew">
      <button target class="button-size-md button-primary">
        <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
        {{'ITEMS_SERVICES.Buttons.CreateNew' |translate}}
      </button>
      <ng-template #contentCreateNew>
        <div class="w-[300px]">
          <div class="button-transparent" (click)="handleCreateNewItem()">
            <!-- <img src="../../../../assets/img/icon/ic_duplicate.svg" alt="icon"> -->
            <div class="block">
              <span
                class="text-text-md-semibold">{{'ITEMS_SERVICES.Tabs.Items'|translate}}</span>
              <p class="text-text-secondary text-text-xs-regular">
                {{'ITEMS_SERVICES.Tabs.DescribeItem'|translate}}
              </p>
            </div>
          </div>
          <div class="button-transparent" (click)="handleCreateNewService()">
            <!-- <img src="../../../../assets/img/icon/ic_duplicate.svg" alt="icon"> -->
            <div class="block">
              <span
                class="text-text-md-semibold">{{'ITEMS_SERVICES.Tabs.Services'|translate}}</span>
              <p class="text-text-secondary text-text-xs-regular">
                {{'ITEMS_SERVICES.Tabs.DescribeServices'|translate}}
              </p>
            </div>
          </div>
        </div>
      </ng-template>
    </app-inno-popover>
  </div>
</div>

<div
  class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">
  <div class="flex w-full items-center gap-[14px] flex-wrap justify-between">
    <app-inno-tabs
      [tabs]="listTypeView"
      [value]="currentTypeView"
      (onChange)="handleChangeTypeView($event)" />
    <div class="flex items-center gap-[8px]">
    </div>
  </div>
</div>

<div class="w-full mt-[24px]">
  @if(currentTypeView === itemServiceView.Item) {
  <app-item-management />
  } @else {
  <app-service-management />
  }
</div>
