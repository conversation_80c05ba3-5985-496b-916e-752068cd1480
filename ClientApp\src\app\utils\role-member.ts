export interface RoleMember {
    name: string,
    value: string;
    depression: string;

}
export const RoleMember: RoleMember[] = [
    { name: "Admin", value: 'Admin', depression: "Full access to your InnoBook account" },
    { name: "Manager", value: 'Manager', depression: "Full access to your InnoBook account with exceptions" },
    { name: "Employee", value: 'Employee', depression: "Track their own time and expenses" },
    { name: "Contractor", value: 'Contractor', depression: "Track their own time and expenses and send you invoices" },
    { name: "Accountant(Free)", value: 'Accountant', depression: "Access reports, expenses and create journal entries" },


];