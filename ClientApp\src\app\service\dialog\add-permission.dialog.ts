import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddPermissionDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/settings/role-permission-project/dialog/add-permission/add-permission.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddPermissionComponent.getComponent(),
      {
      panelClass: 'custom_dialog',
      scrollStrategy: new NoopScrollStrategy(),
      disableClose: true
    }
    );
  }
}
