<app-inno-modal-wrapper [title]="titlePopup" (onClose)="handleClose()">
  <form [formGroup]="itemForm">
    <div class="w-full p-[16px]">
      <div class="w-full flex flex-col gap-[16px] ">
        <app-inno-form-input
          [label]="'ITEMS_SERVICES.NEW_ITEM_FORM.Name' | translate"
          [placeholder]="'ITEMS_SERVICES.NEW_ITEM_FORM.NamePlaceholder' | translate"
          [formControl]="f['name']"
          [value]="f['name'].value"
          [errorMessages]="{
            required: 'ITEMS_SERVICES.NEW_ITEM_FORM.NameRequired' | translate
          }" />

        <app-inno-form-textarea
          [label]="'ITEMS_SERVICES.NEW_ITEM_FORM.Description' | translate"
          [formControl]="f['description']"
          [value]="f['description'].value"
          [placeholder]="'ITEMS_SERVICES.NEW_ITEM_FORM.DescriptionPlaceholder' | translate" />

        <app-inno-form-input
          [label]="'ITEMS_SERVICES.NEW_ITEM_FORM.Rate' | translate"
          type="number"
          [placeholder]="'ITEMS_SERVICES.NEW_ITEM_FORM.RatePlaceholder' | translate"
          [formControl]="f['rate']"
          [value]="f['rate'].value" />
      </div>
      <div class="w-full mt-[16px]">
        <p
          class="text-text-secondary text-text-sm-semibold !mb-[10px]"> {{
          'ITEMS_SERVICES.NEW_ITEM_FORM.Taxes' | translate }}</p>
        <div class="w-full flex flex-col gap-[6px]">
          @for( tax of taxes; track tax; let i = $index) {
          <app-input-tax (onSelectedTax)="handleSelectedTax($event, i)"
            [tax]="tax" (onDelete)="Delete(i)" [isHideDeleteButton]="true" />
          }
        </div>

        <div class="flex justify-start mt-[10px]">
          <button (click)="addTax()" class="button-link-primary">
            <img src="../../../../../../../assets/img/icon/ic_add_green.svg"
              alt="Icon">
            {{ 'ITEMS_SERVICES.NEW_ITEM_FORM.AddTax' | translate }}
          </button>
        </div>
      </div>
    </div>

  </form>
  <div footer>
    <app-inno-modal-footer
      (onCancel)="handleCancel()"
      (onSubmit)="handleSubmit()" />
  </div>
</app-inno-modal-wrapper>
