export class ProjectDto {
  public id!: string;
  public projectName?: string;
  public clientId?: string;
  public companyId?: string;
  public startDate?: Date;
  public endDate?: Date;
  public description?: string;
  public type?: number;
  public flatRate?: number;
  public totalHours?: number;
  public actualHours?: number;
  public budget?: number;
  public status?: number;
  public company?: any;
  public members?: any;
  public services?: any[];
  public invoices?: any;
  public createdAt?: Date;
  public updatedAt?: Date;
  public createdBy?: string;
  public updatedBy?: string | null;

  public static fromJson(json: object): ProjectDto {
    const project = new ProjectDto();
    Object.assign(project, json);
    return project;
  }
}
