import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-inno-tabs',
  templateUrl: './inno-tabs.component.html',
  styleUrls: ['./inno-tabs.component.scss'],
  standalone: true,
  imports: []
})
export class InnoTabsComponent {

  @Input() public tabs: Array<{ label: string, value: any }> = [];
  @Input() public value: any;
  @Output() onChange = new EventEmitter<any>();

  constructor() {}

  handleSelectTab(value: any) {
    this.onChange.emit(value ?? null);
  }
}
