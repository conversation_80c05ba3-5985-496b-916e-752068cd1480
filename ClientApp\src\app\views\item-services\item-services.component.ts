import { Component, OnInit } from '@angular/core';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { ItemManagementComponent } from './item-management/item-management.component';
import { ServiceManagementComponent } from './service-management/service-management.component';
import { ModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';
import { DataService } from 'app/service/data.service';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-item-services',
  templateUrl: './item-services.component.html',
  styleUrls: ['./item-services.component.scss'],
  standalone: true,
  imports: [
    TranslateModule,
    InnoTabsComponent,
    InnoPopoverComponent,
    ItemManagementComponent,
    ServiceManagementComponent
  ]
})
export class ItemServicesComponent implements OnInit {
  public currentTypeView?: ItemAndServiceViewEnum = ItemAndServiceViewEnum.Item;
  public itemServiceView = ItemAndServiceViewEnum;
  public listTypeView = [
  ]
  
  constructor(
    private modifyItemAndServiceDialog: ModifyItemsAndServiceDialog,
    private dataService: DataService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService
  ) {
    this.listTypeView = [
      { label: this.translate.instant('ITEMS_SERVICES.Tabs.Items'), value: ItemAndServiceViewEnum.Item },
      { label: this.translate.instant('ITEMS_SERVICES.Tabs.Services'), value: ItemAndServiceViewEnum.Service },
    ]
  }

  ngOnInit(): void {
    // Get current tab from URL query params
    this.activatedRoute.queryParams.subscribe(params => {
      const tab = params['tab'];
      if (tab) {
        // Convert string to enum value
        this.currentTypeView = ItemAndServiceViewEnum[tab as keyof typeof ItemAndServiceViewEnum] || ItemAndServiceViewEnum.Item;
      }
    });
  }

  handleChangeTypeView(_typeView: ItemAndServiceViewEnum) {
    if (this.currentTypeView === _typeView) return;
    this.currentTypeView = _typeView;
    
    // Update URL query params with current tab
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: { tab: ItemAndServiceViewEnum[_typeView] },
      queryParamsHandling: 'merge'
    });
  }

  removeQueryParams() {
    this.router.navigate([], {
      queryParams: {},
      replaceUrl: true,
      queryParamsHandling: ''
    });
  }

  handleCreateNewItem() {
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Item
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.dataService.reloadItem.next(true)
        }
      })
    });
  }

  handleCreateNewService() {
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Service,
      isShowProject: true
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.dataService.reloadService.next(true)
        }
      })
    });
  }
}
