import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class NewInvoiceDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/invoice/dialog/new-invoice/new-invoice.component'
    );

    return this.matDialog.open(
      importedModuleFile.NewInvoiceComponent.getComponent(),
      {
        data,
        width: "80vw",
        maxWidth: "100%",
        maxHeight: "100%",
        panelClass: 'custom_dialog',
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
