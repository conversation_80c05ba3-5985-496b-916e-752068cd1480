import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { DataService } from 'app/service/data.service';
import { Component, inject, Inject, OnDestroy, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { formatTimeHoursFromSeconds } from 'app/helpers/common.helper';
import { Subscription } from 'rxjs';
import { AuthenticationService } from 'app/auth/service/authentication.service';

@Component({
  selector: 'app-confirm-timer',
  standalone: true,
  imports: [InnoModalWrapperComponent],
  templateUrl: './confirm-timer.component.html',
  styleUrl: './confirm-timer.component.scss'
})

export class ConfirmTimerComponent implements OnInit, OnDestroy {
  private dataService = inject(DataService)
  private authenticationService = inject(AuthenticationService)
  private unsubscribe: Subscription[] = [];
  public timerHours: string = '00:00:00';
  static getComponent(): typeof ConfirmTimerComponent {
    return ConfirmTimerComponent;
  }

  constructor(
    public dialogRef: MatDialogRef<ConfirmTimerComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { }

  handleClose() {
    this.dialogRef.close();
  }
  ngOnInit(): void {
    const sb = this.dataService.GetTimeTrackingCreateTimerInfo()
      .subscribe(addTimerInfo => {
        this.timerHours = formatTimeHoursFromSeconds(addTimerInfo?.totalSeconds ?? 0)
      })
    this.unsubscribe.push(sb)
  }
  onNoClick(): void {
    this.dialogRef.close();
    this.handleDestroy();
  }

  async onYesClick(): Promise<void> {

    const payload = {
      isRunning: false,
      timer: null,
      timerStartTime: null,
    }
    this.authenticationService.UpdateTimer(payload).subscribe()
    this.dialogRef.close(true);
    this.dataService.SetNewTimeTrackingShowingTimer(false)
    this.handleDestroy();
  }
  handleDestroy() {
    if (this.unsubscribe) {
      this.unsubscribe.forEach(element => {
        element.unsubscribe();
      });
    }
  }
  ngOnDestroy(): void {
    this.handleDestroy()
  }
}
