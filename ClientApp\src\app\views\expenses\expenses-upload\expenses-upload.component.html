<!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary">
    <div
        class="container-full flex justify-between items-center flex-wrap gap-2">
        <p class="text-text-primary text-headline-lg-bold">
            {{'FILEUPLOAD.Title'|translate}}
        </p>
    </div>
</div>
<!-- End Header page -->
@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
    <app-inno-spin size="lg" />
</div>
}@else {
<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
    <div class="w-full max-w-[300px]">
        <app-inno-input-search [value]="search"
            (onChange)="handleSearch($event)" />
    </div>

    <select id="project" [(ngModel)]="expensesId"
        (change)="handleSelectExpenses($event)"
        [ngModelOptions]="{standalone: true}"
        class="dropdown-md min-w-52">

        @for(item of listExpenses; track item; let i = $index ){
        <option [value]="item.id">
            {{ item.expensesName }}
        </option>
        }
    </select>
</div>

<div class="w-full mt-[12px]">
    <ejs-grid #grid class="customTable"
        [dataSource]="dataSource"
        [allowSorting]="true"
        [sortSettings]='sortOptions'
        (actionBegin)="onActionBegin($event)"
        [allowSelection]="true"
        [selectionSettings]="selectionOptions">
        <e-columns>
            <!-- <e-column type="checkbox" width="30"></e-column> -->
            <e-column [headerText]="'FILEUPLOAD.FileName' | translate"
                width="200" field="filename">
                <ng-template #template let-data>
                    <div class="flex items-center">
                        <img class="w-4 mr-2"
                            [src]="storeService.getIconFile(data.type)">
                        <p
                            class="text-text-md-regular text-text-primary line-clamp-1">
                            {{data.filename}}</p>
                    </div>

                </ng-template>
            </e-column>
            <e-column [headerText]="'FILEUPLOAD.Size' | translate" width="200"
                field="size">
                <ng-template #template let-data>
                    <p class="text-text-md-regular text-text-primary">
                        {{data.size | size}}</p>
                </ng-template>
            </e-column>
            <e-column [headerText]="'FILEUPLOAD.Type' | translate" width="200"
                field="type">
                <ng-template #template let-data>
                    <p class="text-text-md-regular text-text-primary">
                        {{data.type}}</p>
                </ng-template>
            </e-column>
            <e-column headerText [headerText]="'FILEUPLOAD.Action' | translate"
                width="100">
                <ng-template #template let-data>
                    <app-inno-table-action
                        (onDelete)="handleDelete(data)"
                        (onDowload)="handleDowload(data)"
                        (onArchive)="handleArchive(data)" />
                </ng-template>
            </e-column>
        </e-columns>
    </ejs-grid>
    <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
</div>

}
