<div
  class="w-full shadow-md rounded-[8px] bg-bg-primary border border-border-primary-slight">
  <div class="w-full flex flex-col gap-[16px] p-[16px]">
    <div class="w-full">
      <p
        class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'FILTER.ClientProjects'|translate}}</p>
      <ng-template #templateTriggerSelectProject>
        <button class="dropdown-md w-full text-text-placeholder-slight"
          [ngClass]="{'text-text-primary': projectLabelSelected!! || clientLabelSelected!! }">
          @if(clientLabelSelected&&!projectLabelSelected)
          {
          {{clientLabelSelected}}
          }
          @else{
          {{ clientLabelSelected ? clientLabelSelected + ' - ' +
          (projectLabelSelected ?? 'Select Client/Projects') :
          (projectLabelSelected ?? 'Select Client/Projects') }}

          }

          <img src="../../../../assets/img/icon/ic_arrow_down_gray.svg"
            alt="Icon">
        </button>
      </ng-template>
      <app-inno-select-search-project
        [templateTrigger]="templateTriggerSelectProject"
        [isShowCreateButton]="false"
        (onSelect)="handleSelectProject($event)"
        [value]="projectIdSelected"
        [isOnlySelectProject]="false"
        (onGetInfoSelectedValue)="handleSelectProject($event)" />
    </div>

    <div class="flex items-center mxw600:flex-col gap-[16px]">
      <div class="w-full">
        <p
          class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'FILTER.LoggedBy'|translate}}
        </p>
        <ng-template #templateTriggerSelectUser>
          <button class="dropdown-md w-full text-text-placeholder-slight"
            [ngClass]="{'text-text-primary': userLabelSelected!! }">
            {{ userLabelSelected ?? 'FILTER.SelectUser'|translate }}
            <img src="../../../../assets/img/icon/ic_arrow_down_gray.svg"
              alt="Icon">
          </button>
        </ng-template>
        <app-inno-select-search-user
          [templateTrigger]="templateTriggerSelectUser"
          (onSelect)="handleSelectUser($event)"
          [value]="userIdSelected"
          [isAll]="isAll"
          (onGetInfoSelectedValue)="handleSelectUser($event)" />
      </div>
    </div>

    @if(typeViewSelected === timeTrackingView.All) {
    <div class="flex items-center mxw600:flex-col gap-[16px]">
      <div class="w-full">
        <p
          class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'FILTER.StartDate'|translate}}
        </p>
        <app-inno-datepicker
          [placeholder]="'FILTER.SelectStartDate'|translate"
          [value]="startDateSelected"
          (onChange)="handleChangeStartDate($event)" />
      </div>
      <div class="w-full">
        <p
          class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'FILTER.EndDate'|translate}}
        </p>
        <app-inno-datepicker
          [placeholder]="'FILTER.SelectEndDate'|translate"
          [value]="endDateSelected"
          (onChange)="handleChangeEndDate($event)" />
      </div>
    </div>
    }

  </div>
  <div
    class="border-t border-border-primary-slight w-full flex justify-between items-center gap-[12px] flex-wrap p-[16px]">
    <button class="button-link-danger" (click)="handleResetDefault()">
      {{'FILTER.ResetDefault'|translate}}
    </button>
    <div class="flex items-center gap-[12px]">
      <button class="button-size-md button-outline" (click)="handleCancel()">
        {{'BUTTON.Cancel'|translate}}
      </button>
      <button class="button-size-md button-primary" (click)="handleApply()">
        {{'BUTTON.Apply'|translate}}
      </button>
    </div>
  </div>
</div>
