import { AfterViewInit, Component, OnD<PERSON>roy, ViewChild } from '@angular/core';
import {
  ToastAllModule,
  ToastAnimationSettingsModel,
  ToastComponent
} from '@syncfusion/ej2-angular-notifications';
import { Subscription } from 'rxjs';
import { SharedModule } from '../module/shared.module';
import { ToastService } from '../service/toast.service';

@Component({
  selector: 'app-toast',
  templateUrl: 'toast.html',
  styleUrls: ['toast.scss'],
  standalone: true,
  imports: [SharedModule, ToastAllModule]
})
export class ToastShowComponent implements AfterViewInit, OnDestroy {
  @ViewChild('toasttype')
  private toastObj!: ToastComponent;
  public position = { X: 'Center', Y: 'Top' };
  public toastAnimation: ToastAnimationSettingsModel = {
    show: {
      effect: 'SlideTopIn',
      duration: 1000
    },
    hide: {
      effect: 'SlideTopOut',
      duration: 600
    }
  };
  public dataToast!: any;
  public title: string | null = null;
  public content: string | null = null;
  private unsubscribe: Subscription[] = [];
  constructor(private toastService: ToastService) { }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }

  ngAfterViewInit() {
    this.unsubscribe.push(
      this.toastService.target.subscribe((val) => {
        if (val && val !== '') this.toastObj.target = val;
      })
    );

    this.unsubscribe.push(
      this.toastService.visibility.subscribe((val) => {
        if (val && val.length > 0) {
          if (!this.toastObj.target) this.toastObj.target = 'toast_target';
          this.title = val[0]['title']?.toString();
          this.content = val[0]['content']?.toString();
          this.dataToast = val[0];

          this.toastObj.show();
        }
      })
    );
  }

  closeToast() {
    this.toastObj.hide();
  }
}
