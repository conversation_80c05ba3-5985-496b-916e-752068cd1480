import { CUSTOM_ELEMENTS_SCHEMA, EventEmitter, Output, Component, Input, OnChanges, SimpleChanges, forwardRef } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { DatePickerModule } from '@syncfusion/ej2-angular-calendars';
import { InnoErrorMMessageComponent } from '../inno-error-message/inno-error-message.component';

@Component({
  selector: 'app-inno-form-datepicker',
  templateUrl: './inno-form-datepicker.component.html',
  styleUrls: ['./inno-form-datepicker.component.scss'],
  standalone: true,
  imports: [
    DatePickerModule,
    InnoErrorMMessageComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormDatepickerComponent),
      multi: true
    }
  ]
})

export class InnoFormDatepickerComponent implements OnChanges, ControlValueAccessor {

  @Input() isRequired?: boolean;
  @Input() label?: string = '';
  @Input() mode?: 'day' | 'week' | 'month' = 'day';
  @Input() id?: string;
  @Input() enableMask?: boolean;
  @Input() name?: string;
  @Input() format?: string = "dd-MM-yyyy"
  @Input() placeholder?: string
  @Input() value?: Date
  @Input() errorMessages?: { [key: string]: string };
  @Input() formControl?: FormControl;

  @Output() onChange = new EventEmitter<any>();

  public datePickerValue?: Date
  public fields: object = { text: 'label', value: 'value' };
  public start: string = 'Month';
  public depth: string = 'Month';
  public showTodayButton = true;
  public weekNumber = false;
  public invalidDate = false

  constructor() { }

  registerOnChange(fn: (value: string) => void): void { }

  registerOnTouched(fn: () => void): void { }

  setDisabledState(isDisabled: boolean): void { }

  writeValue(value: string): void { }

  ngOnChanges(changes: SimpleChanges) {
    const _mode = changes['mode']?.currentValue ?? changes['value']?.currentValue
    if (!_mode) return
    this.updateModeSettings(_mode);
  }

  updateModeSettings(mode: 'day' | 'week' | 'month' | undefined) {
    switch (mode) {
      case 'day':
        this.start = 'Month';
        this.depth = 'Month';
        this.showTodayButton = true;
        this.weekNumber = false;
        break;

      case 'week':
        this.start = 'Month';
        this.depth = 'Month';
        this.showTodayButton = false;
        this.weekNumber = true;
        break;

      case 'month':
        this.start = 'Year';
        this.depth = 'Year';
        this.showTodayButton = false;
        this.weekNumber = false;
        break;

      default:
        break;
    }
  }

  private touchControl() {
    if (!this.formControl) return

    this.formControl.markAsDirty()
    this.formControl.markAsTouched()
  }


  hasError() {
    return (this.formControl?.invalid && (this.formControl.dirty || this.formControl.touched));
  }

  getErrorMessage(): string {
    if (!this.hasError()) return '';

    if (this.formControl?.errors && this.errorMessages) {
      for (const errorType in this.formControl.errors) {
        if (this.errorMessages[errorType]) {
          return this.errorMessages[errorType];
        }
      }
    }
    return '';
  }
  onBlur(e) {
    if (e.model.inputWrapper.container.classList.contains('e-error')) {
      this.invalidDate = true
      if (this.formControl) this.formControl.setValue(e.model.inputElement.value)
      this.onChange.emit(e.model.inputElement.value)
    }
    else {
      this.invalidDate = false
    }
  }
  handleChangeValue(e: any) {
    this.touchControl()
    const _dateSelected = e?.value ?? undefined
    this.onChange.emit(_dateSelected)
    if (this.formControl) this.formControl.setValue(_dateSelected)

  }
}
