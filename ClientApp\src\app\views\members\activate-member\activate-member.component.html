<div class="pageRegister">
    @if(!isValidating) {
    <div class="wrapForm">
        <h2 class="text-center">InnoBooks</h2>
        <form autocomplete="off" [formGroup]="registerForm"
            (ngSubmit)="onSubmit()">
            <div class="grid gap-6 mb-6 md:grid-cols-2">
                <div>
                    <input type="text"
                        autocomplete="off"
                        formControlName="firstname"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="First name"
                        required />
                </div>
                <div>

                    <input type="text" id="last_name"
                        formControlName="lastname"
                        autocomplete="off"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Last name" required />
                </div>
            </div>
            <div class="form-group mb-3">
                <div>

                    <input type="email" id="_email"
                        formControlName="email"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Email" required />
                </div>
                @if (
                (f['email'].dirty || f['email'].touched) &&
                f['email'].hasError('required')
                ) {
                <mat-error class="matError">email is required</mat-error>
                }
                @if (
                (f['email'].dirty || f['email'].touched) &&
                f['email'].hasError('email')
                ) {
                <mat-error class="matError">Invalid email</mat-error>
                }
            </div>

            <div class="form-group mb-3">

                <div class="relative w-full">
                    <input
                        type="password"
                        id="password"
                        autocomplete="new-password"
                        formControlName="password"
                        class="w-full p-2.5 border text-sm bg-gray-50 border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500 block "
                        placeholder="Enter password" />

                    <button
                        type="button"
                        (click)="togglePassword()"
                        class="absolute right-2 top-2 text-gray-600 hover:text-gray-900 focus:outline-none">

                        <span id="showIcon"
                            class="material-icons !hidden text-sm">
                            visibility
                        </span>

                        <span id="hideIcon" class="material-icons text-sm">
                            visibility_off
                        </span>
                    </button>
                </div>

                @if (
                (f['password'].dirty || f['password'].touched) &&
                f['password'].hasError('required')
                ) {
                <mat-error class="matError">Password is required</mat-error>
                }
                @if (
                (f['password'].dirty || f['password'].touched) &&
                f['password'].hasError('minlength')
                ) {
                <mat-error class="matError">Password minlength 6</mat-error>
                }
            </div>

            <div class="form-group mb-3">

                <div>

                    <input type="password" id="_confirmPassword"
                        formControlName="confirmPassword"
                        autocomplete="off"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Confirm Password" required />
                </div>
                @if (
                (f['confirmPassword'].touched) &&
                f['confirmPassword'].hasError('required')
                ) {
                <mat-error class="matError">ConfirmPassword is
                    required</mat-error>
                }

                @if (
                (f['confirmPassword'].dirty ) &&
                f['confirmPassword'].hasError('confirmPasswordValidator')
                ) {
                <mat-error class="matError">Passsword and
                    Confirm Password didn't match.</mat-error>
                }

            </div>
            <!-- <div class="form-group mb-3">

                <div>

                    <input type="text" id="_company"
                        formControlName="company"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                        placeholder="Company" required />
                </div>
                @if (
                (f['company'].touched) &&
                f['company'].hasError('required')
                ) {
                <mat-error class="matError">Company is
                    required</mat-error>
                }

            </div> -->
            <div>
                <input type="checkbox" formControlName="agree" name="agree"
                    class="form-check-input text-lg" />
                <span style="font-size: 12px;">
                    I confirm that I have read and agree to InnoBooks Terms of
                    Service and Privacy Policy.
                </span>

            </div>

            <button
                [disabled]="!registerForm.valid || !registerForm.controls['agree'].value"
                type="submit"
                class="btn btn-success mt-3 text-center w-full">Get
                Started</button>
        </form>
    </div>
    }
</div>