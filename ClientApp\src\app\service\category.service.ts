import { Category } from './../dto/interface/category.interface';
import { CategoryItem } from './../dto/interface/categoryItem.interface';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class CategoryService {

  private http = inject(HttpClient)
  constructor() { }

  CreateCategory(payload: any): Observable<object> {
    return this.http.post(UrlApi + '/Category/CreateCategory', payload);
  }
  GetAllCategoryAsync(payload): Observable<Category> {
    return this.http.post<Category>(UrlApi + `/Category/GetAllCategoryAsync`, payload);
  }

  CreateCategoryItem(payload: CategoryItem[]): Observable<object> {
    return this.http.post(UrlApi + '/Category/CreateCategoryItem', payload);
  }
  DeleteCategory(payload: any): Observable<object> {
    return this.http.post(UrlApi + '/Category/DeleteCategory', payload);
  }
  DeleteCategoryItem(payload: any): Observable<object> {
    return this.http.post(UrlApi + '/Category/DeleteCategoryItem', payload);
  }
  UpdateCategory(payload: Category): Observable<Category> {
    return this.http.post<Category>(UrlApi + '/Category/UpdateCategory', payload);
  }
  GetCategory(): Observable<Category[]> {
    return this.http.get<Category[]>(UrlApi + `/Category/GetCategory`);
  }
  GetCategoryById(id: string): Observable<Category> {
    return this.http.get<Category>(UrlApi + `/Category/GetCategoryById?id=${id}`);
  }


}
