import { Category } from "./category.interface";
import { CategoryItem } from "./categoryItem.interface";
import { Client } from "./client.interface";
import { Merchant } from "./merchant.interface";
import { Tax } from "./tax.interface";

export interface Expenses {
  expensesName?: string;
  clientId?: string;
  projectId?: any;
  categoryId?: string;
  merchantId?: string;
  categoryItemId?: string;
  note?: string;
  paidAmount?: number;
  img?: any;
  date?: string;
  base64?: any;
  filename?: any;
  category?: Category;
  project?: any;
  company?: any;
  client?: Client;
  merchant?: Merchant;
  attachments?: any;
  itemExpense?: any;
  categoryItem?: CategoryItem;
  merchants?: any;
  id?: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: any;
  taxes?: Tax[]
  itemName?: string
  categoryName?: string
}
