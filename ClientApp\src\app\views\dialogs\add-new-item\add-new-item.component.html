
<div class="w-full">
    <div class="p-[16px]">

        <div class="w-full sticky top-0">
            <div
                class="w-full  bg-bg-primary border-b border-border-primary-slight">
                <p class="text-headline-sm-bold text-text-primary">
                    {{'SELECTTIMETRACKING.Title'|translate}}
                </p>
            </div>
            <button
                type="button"
                class="button-icon absolute top-1 right-1"
                (click)="handleClose()">
                <img src="../../../assets/img/icon/ic_remove.svg" alt="Icon">
            </button>
        </div>
        <div
            class="flex w-full items-center gap-[14px] flex-wrap justify-between mt-[8px]">
            <app-inno-tabs
                [tabs]="listTypeView"
                [value]="currentTypeView"
                (onChange)="handleChangeTypeView($event)" />
            <div class="flex items-center gap-[8px]">
            </div>
        </div>
    </div>

    <div class="w-full flex flex-col relative">
        <div class="w-full">
            @if(isFetchingProject) {
            <div class="w-full py-2 flex justify-center items-center">
                <app-inno-spin />
            </div>
            }
            <div class="w-full">
                @if(currentTypeView === itemServiceView.Item) {
                <app-load-item (submit)="handleSubmitItem($event)"
                    (cancel)="handleCancel()" />
                } @else {
                <app-load-service (submit)="handleSubmitItem($event)"
                    (cancel)="handleCancel()" />
                }
            </div>
        </div>
    </div>
</div>
