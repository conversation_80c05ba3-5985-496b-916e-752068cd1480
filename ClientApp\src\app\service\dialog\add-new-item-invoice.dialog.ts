import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddNewItemDialog extends AsyncDialog<any> {
    async open(data): Promise<MatDialogRef<any>> {
        const importedModuleFile = await import(
            '../../views/dialogs/add-new-item/add-new-item.component'
        );

        return this.matDialog.open(
            importedModuleFile.AddNewItemComponent.getComponent(),
            {
                panelClass: 'custom_dialog',
                width: "100%",
                maxWidth: "60vw",
                scrollStrategy: new NoopScrollStrategy(),
            }
        );
    }
}
