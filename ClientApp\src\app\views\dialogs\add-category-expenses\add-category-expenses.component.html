
<app-innobook-modal-wrapper (onClose)="closeDialog()">
    <div class="relative group w-full  rounded-md p-2">
        <div class="text-start">
            <label for="client"
                class="block mb-2 text-sm font-medium text-gray-900">Select
                Category
            </label>
            <select
                (change)="handleSelectCategory($event)"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                @for(option of listCategory; track option; let i =
                $index
                )
                {
                <option [value]="option.id">
                    <label>{{ option.categoryName }}</label>
                </option>
                }

            </select>
        </div>
    </div>

    <h6 class="text-start pl-3">Category Item</h6>
    <div class="p-3">
        <div class="flex items-center text-blue-500" (click)="RemoveAll() ">
            @if(listCategoryItem.length>0)
            {
            <span class="material-icons">
                remove
            </span>
            <span
                class="ml-2 cursor-pointer">Remove
                All</span>
            }
        </div>
        <hr>
        @for(sr of listCategoryItem ; ; track sr; let
        i=$index)
        {
        <div class="relative group w-full border rounded-md p-2 mb-2">
            <div class="flex items-center w-full justify-between">
                {{sr.itemName}}
                <div
                    (click)="RemoveService(i)"
                    class=" hidden group-hover:block text-end cursor-pointer">
                    <span class="material-icons">
                        delete_forever
                    </span>
                </div>
            </div>

        </div>
        }
        <div>
            <div class="mb-2">
                @if(isAddSvcMode) {
                <input type="text"
                    [(ngModel)]=categoryNameItem
                    class="bg-white  border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder="Category Item" />
                }
                @if(categoryNameItem)
                {
                <div (click)="CreatedCategoryItem()"
                    class="bg-white max-w-68 flex items-center p-2 cursor-pointer hover:bg-gray-400 text-blue-500">
                    <span class="material-icons">
                        add
                    </span> Created Category Item
                    <span>" {{categoryNameItem}} "</span>

                </div>
                }

            </div>

            <button type="button" (click)="AddCategoryItem()"
                class="btn btn-outline-secondary btn-dashed">
                <span class="material-icons">add</span>
                Add a new item Category
            </button>

        </div>
        <div class="w-full flex justify-around mt-3">
            <button (click)="closeDialog()" type="button"
                class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Cancel</button>

            <button (click)="Save()" type="button"
                [disabled]="listCategoryItem.length===0&& listItemCategoryRemove.length===0&&categoryId==''"
                [ngClass]="{'bg-green-700 hover:bg-green-800 text-white' :(listCategoryItem.length>0||listItemCategoryRemove.length>0) &&categoryId,'bg-gray-400':listCategoryItem.length==0||listItemCategoryRemove.length==0||categoryId==''}"
                class="text-gray-900 focus:outline-none hover:bg-gray-300 focus:ring-4 focus:ring-gray-100 font-medium cursor-pointer rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Save</button>

        </div>
    </div>
</app-innobook-modal-wrapper>
