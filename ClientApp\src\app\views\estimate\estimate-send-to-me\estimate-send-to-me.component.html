@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
    <app-inno-spin size="lg" />
</div>
}@else {
@if(dataSource?.length) {
<div class="w-full mt-[12px]"
    [ngClass]="{ 'mb-28':_storeService.getIsRunning() }">
    <ejs-grid
        #grid
        class="customTable"
        (actionBegin)="onActionBegin($event)"
        [allowSorting]="true"
        [sortSettings]='sortOptions'
        [dataSource]="dataSource">
        <e-columns>
            <e-column headerText="Sent Estimate Date" width="150">
                <ng-template #template let-data>
                    <span>Unknown</span>
                    <!-- <span>{{data.sendDate | date: 'MM/dd/yyyy'}}</span> -->
                </ng-template>
            </e-column>
            <e-column headerText="Estimate Number" width="180"
                field="invoiceNumber">
                <ng-template #template let-data>
                    <div class="w-full">
                        <p class="text-text-primary text-text-md-semibold">
                            {{ data.invoiceNumber ?? '' }}
                        </p>
                        <p class="text-text-tertiary text-text-xs-regular">
                            {{ data.notes ?? '' }}
                        </p>
                    </div>
                </ng-template>
            </e-column>
            <e-column headerText="Sender" width="250">
                <ng-template #template let-data>
                    <div class="w-full flex gap-[12px] items-center">
                        <ngx-avatars
                            class="shrink-0"
                            [size]="40"
                            [name]="data?.client?.clientName" />
                        <div class="w-full ">
                            <p class="text-text-primary text-text-md-semibold">
                                {{ data?.company?.businessName ?? '' }}
                            </p>
                            @if(data?.company?.email) {
                            <p class="text-text-tertiary text-text-xs-regular">
                                {{ data?.company?.email ?? '' }}
                            </p>
                            }
                        </div>
                    </div>
                </ng-template>

            </e-column>

            <e-column headerText="Issued Estimate Date" width="150"
                field="invoiceDate">
                <ng-template #template let-data>
                    <span>{{data.invoiceDate | date:
                        _storeService.getdateFormat() }}</span>
                </ng-template>
            </e-column>
            <e-column headerText="Status" width="120">
                <ng-template #template let-data>
                    <app-inno-status [status]="data.status" />
                </ng-template>
            </e-column>
            <e-column headerText="Amount" width="120" field="paidAmount">
                <ng-template #template let-data>
                    <p class="text-text-primary text-text-md-bold">
                        ${{data.paidAmount | decimal:2}}

                    </p>
                </ng-template>
            </e-column>
            <e-column field='action' headerText width="100">
                <ng-template #template let-item>
                    <app-inno-table-action
                        (onEdit)="handleEdit(item)"
                        (onDelete)="handleDelete(item)" />
                </ng-template>
            </e-column>
        </e-columns>

    </ejs-grid>
    <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
</div>
} @else {
<div class="container-full mt-[24px]">
    <app-inno-empty-data
        title="No active estimate at the moment"
        description="You can create a new estimate" />
</div>
}
}
