import { environment } from 'environments/environment';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { Tax } from '../dto/interface/tax.interface';
import { Observable } from 'rxjs';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class TaxService {


  private http = inject(HttpClient)
  constructor() { }

  CreateTax(payload: any): Observable<Tax[]> {
    return this.http.post<Tax[]>(UrlApi + '/Tax/CreateTax', payload);
  }
  UpdateTax(payload: any): Observable<Tax[]>{
    return this.http.post<Tax[]>(UrlApi + '/Tax/UpdateTax', payload);
  }
  GetAllTax(payload: Parameter): Observable<PaginatedResponse<Tax>> {
    return this.http.get<PaginatedResponse<Tax>>(UrlApi + `/Tax/GetAllTax?Page=${payload.Page}&PageSize=${payload.PageSize}&Search=${payload.Search}&InvoiceId=${payload.InvoiceId}`);
  }


}
