import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { Subscription } from 'rxjs';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { AddClientsDialog } from '../../service/dialog/add-clients.dialog';


@Component({
  selector: 'app-inno-select-search-client',
  templateUrl: './inno-select-search-client.component.html',
  styleUrls: ['./inno-select-search-client.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    AvatarModule
  ]
})

export class InnoSelectSearchClientComponent implements OnDestroy, OnInit {

  @Input() public templateTrigger: TemplateRef<any> | null = null
  @Input() public defaultTextSearch: string = ''
  @Input() public isShowCreateButton: boolean = true;
  @Input() public value?: string = ''
  @Input() public placeholderSearch?: string

  @Output() public onSelect = new EventEmitter<IFilterDropdownOption>();
  @Output() public onGetInfoSelectedValue = new EventEmitter<IFilterDropdownOption>();

  public listOptionPreview: IFilterDropdownOption[] = []
  public listOptionOriginal: IFilterDropdownOption[] = []
  public isLoading: boolean = false

  private dropdownOptionService = inject(DropdownOptionsService)
  protected unsubscribe: Subscription[] = [];

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor(private addClientsDialog: AddClientsDialog) { }

  ngOnInit(): void {
    if (this.value) this.loadData(true)
  }

  isSelectedValue(item: IFilterDropdownOption): boolean {
    if (!item?.value) return false

    const listValue = this.value?.split(',') ?? []
    return listValue.some(x => x == item.value)
  }

  async loadData(isGetInfoSelectedValue?: boolean) {
    this.isLoading = true
    const dropdownOption = await this.dropdownOptionService.getDropdownOptionsProjectAndClient({
      isOnlyClient: true
    })
    this.listOptionOriginal = dropdownOption
    this.handleSearch(this.defaultTextSearch)
    if (isGetInfoSelectedValue && this.value) {
      const selectedValue = this.listOptionOriginal.find(x => x.value == this.value)
      this.onGetInfoSelectedValue.emit(selectedValue)
    }
    this.isLoading = false
  }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal.filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)
  }

  handleCreateNew() {
    this.addClientsDialog.open({});

    this.handleCloseSearchResult()
  }

  private handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  handleChooseOption(item: IFilterDropdownOption) {
    if (item.metadata?.type != 'client') return
    if (this.isSelectedValue(item)) return

    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
