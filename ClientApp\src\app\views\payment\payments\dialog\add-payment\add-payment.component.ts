import { InvoiceService } from './../../../../../service/invoice.service';
import { PaymentService } from './../../../../../service/payment.service';
import { SpinnerService } from 'app/service/spinner.service';
import { ToastService } from 'app/service/toast.service';
import { StoreService } from 'app/service/store.service';
import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { SharedModule } from 'app/module/shared.module';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { Component, DestroyRef, Inject, inject } from '@angular/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PaymentMethod } from 'app/utils/payment-method';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import moment from 'moment-timezone';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { Invoice } from 'app/dto/interface/invoice.interface';
@Component({
  selector: 'app-add-payment',
  standalone: true,
  imports: [SharedModule,
    CommonModule,
    MatFormFieldModule,
    CurrencyPipe,
    InnobookModalWrapperComponent],
  providers: [CurrencyPipe],
  templateUrl: './add-payment.component.html',
  styleUrl: './add-payment.component.scss'
})
export class AddPaymentComponent {
  public invoiceId!: string;
  listInvoice: Invoice[] = []
  public newPaymentForm!: UntypedFormGroup;
  destroyRef = inject(DestroyRef);
  router = inject(Router);
  private _spinnerService = inject(SpinnerService)
  public _storeService = inject(StoreService)
  private _toastService = inject(ToastService)
  private _paymentService = inject(PaymentService)
  private formBuilder = inject(UntypedFormBuilder)
  private currencyPipe = inject(CurrencyPipe)
  private _invoiceService = inject(InvoiceService)
  PaymentMethod = PaymentMethod;

  static getComponent(): typeof AddPaymentComponent {
    return AddPaymentComponent;
  }

  constructor(public dialogRef: MatDialogRef<AddPaymentComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.newPaymentForm = this.formBuilder.group({
      paymentAmount: ["", Validators.compose([Validators.required])],
      paymentDate: [this.formatDate(new Date()), Validators.compose([Validators.required])],
      paymentMethod: ["", Validators.compose([Validators.required])],
      paymentNote: [""],
      paymentSendNotifi: [false],
    },

    );


  }
  handleSelectInvoice($event: any) {
    let index = this.listInvoice.findIndex(x => x.id.toString() == ($event.target as HTMLSelectElement).value.toString())
    if (index >= 0) {
      this.invoiceId = this.listInvoice[index].id
      this.newPaymentForm.controls["paymentAmount"].setValue(this.listInvoice[index].paidAmount)
    }
  }
  transformAmount(element: any) {
    const paymentAmountControl = this.newPaymentForm.controls["paymentAmount"];
    let paymentAmountValue = paymentAmountControl.value;
    const numericValue = parseFloat(paymentAmountValue.toString().replace(/[^0-9.-]+/g, ''));
    if (!isNaN(numericValue)) {
      paymentAmountControl.setValue(this.currencyPipe.transform(numericValue, '$'));
    } else {
    }
  }
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  handleSelectMethod($event: any) {
    let index = this.PaymentMethod.findIndex(x => x.value.toString() == ($event.target as HTMLSelectElement).value.toString())
    if (index >= 0) {
      this.newPaymentForm.controls["paymentMethod"].setValue(this.PaymentMethod[index].value)
    }
  }
  onSubmit() {
    this._spinnerService.show();
    let payload = {
      note: this.newPaymentForm.controls["paymentNote"].value,
      datePayment: moment.utc(this.newPaymentForm.controls["paymentDate"].value).toDate(),
      idPaymentMethod: this.newPaymentForm.controls["paymentMethod"].value,
      notificationSent: this.newPaymentForm.controls["paymentSendNotifi"].value,
      paidAmount: this.newPaymentForm.controls["paymentAmount"].value,
      invoiceId: this.invoiceId,
    }
    if (this.invoiceId) {
      this._paymentService.CreatedPayment(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this.dialogRef.close(res)
          this._spinnerService.hide();
          this._toastService.showSuccess("Save", "Create Success")
          return;
        }
        this._spinnerService.hide();
      }
      )
    }
    else {
      this._spinnerService.hide();
      this.dialogRef.close(payload)
    }

  }
  _handleData(_data: any) {
    if (_data) {
      this.newPaymentForm.controls["paymentAmount"].setValue(this.data.id ? _data.paidAmount : this.data)
    }

  }
  GetAllInvoice() {
    let payload: Parameter = {
      Page: 0,
      PageSize: 100,
      Search: "",

    }
    this._invoiceService.GetAllInvoice(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.listInvoice = res.data
        }
      }
    });
  }
  ngOnInit() {
    if (this.data) {
      this.invoiceId = this.data.id
      this._handleData(this.data)
      this.newPaymentForm.controls["paymentMethod"].setValue(this.PaymentMethod[0].value)
    } else {
      this.newPaymentForm.controls["paymentMethod"].setValue(this.PaymentMethod[0].value)
      this.GetAllInvoice();
    }
  }

  get f() {
    return this.newPaymentForm.controls;
  }
  closeDialog() {
    this.dialogRef.close();
  }
}
