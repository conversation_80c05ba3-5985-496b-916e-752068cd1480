<div class="choose-users-box">
    <!-- <mat-form-field class="mat-form-field-fluid "
        (click)="stopPropagation($event)">
        <input matInput [formControl]="clientFilterCtrl"
            autocomplete="off" />
        <span class="material-icons">
            search
        </span>
    </mat-form-field> -->
    <div class="relative mb-2" (click)="stopPropagation($event)">
        <div
            class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
            <svg class="w-4 h-4 text-gray-500 dark:text-gray-400"
                aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round"
                    stroke-linejoin="round" stroke-width="2"
                    d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
            </svg>
        </div>
        <input type="search" id="default-search"
            [formControl]="clientFilterCtrl"
            class="block w-full p-2  !pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search..." required />

    </div>
    @for( item of filteredClient | async; track item;let i=$index)
    {
    <div class="users-container">
        <div class="pl-2 mb-2 cursor-pointer"
            (click)="select(item)">
            <div class="flex items-center">
                <div
                    class="relative inline-flex items-center justify-center w-8 h-8 overflow-hidden bg-gray-400 rounded-full">
                    <span
                        class="font-medium text-gray-200">{{item.clientName.slice(0,1)}}</span>
                </div>
                <div class="pl-3">
                    <div>{{item.clientName}}
                    </div>
                </div>
            </div>

        </div>
    </div>
    }

</div>
