import { Client } from "./client.interface";

export interface Project {
    projectName?: string;
    clientId?: string;
    companyId?: string;
    startDate?: string;
    endDate?: string;
    description?: string;
    type?: number;
    option?: number;
    flatRate?: number;
    totalHours?: number;
    hourlyRate?: number;
    actualHours?: number;
    budget?: number;
    status?: number;
    client?: Client;
    company?: any;
    members?: any;
    services?: any;
    invoices?: any;
    id?: string;
    createdAt?: string;
    updatedAt?: string;
    createdBy?: string;
    updatedBy?: any;
    billable: boolean
}
