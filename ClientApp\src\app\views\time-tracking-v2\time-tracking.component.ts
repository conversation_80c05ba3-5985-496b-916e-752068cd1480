import { LayoutUtilsService } from '../../core/services/layout-utils.service';
import { ClientService } from '../../service/client.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnD<PERSON>roy, OnInit, ViewChild } from "@angular/core";
import { MatMenuModule, MatMenuTrigger } from "@angular/material/menu";
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ReplaySubject, Subject, Subscription } from 'rxjs';
import { SharedModule } from 'app/module/shared.module';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { DataService } from 'app/service/data.service';
import { InnoDatepickerComponent } from 'app/component/inno-datepicker/inno-datepicker.component';
import { AddTimeTrackingRecordComponent } from './add-time-tracking-record/add-time-tracking-record.component';
import { ViewDayComponent } from './view-day/view-day.component';
import { ViewWeekComponent } from './view-week/view-week.component';
import { ViewAllComponent } from './view-all/view-all.component';
import { ViewMonthComponent } from './view-month/view-month.component';
import { TimeTrackingProvider } from './time-tracking.provider';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { FilterTrackingComponent } from './filter-tracking/filter-tracking.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { debounceHandler } from 'app/helpers/common.helper';
import { NewInvoiceDialog } from '../../service/dialog/new-invoice.dialog';
import { GenerateInvoiceDialog } from '../../service/dialog/generate-invoice.dialog';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "app-time-tracking",
  standalone: true,
  imports: [
    SharedModule,
    MatMenuModule,
    InnoTabsComponent,
    InnoDatepickerComponent,
    InnoInputSearchComponent,
    AddTimeTrackingRecordComponent,
    FilterTrackingComponent,
    ViewDayComponent,
    ViewWeekComponent,
    ViewAllComponent,
    ViewMonthComponent,
    FilterTrackingComponent,
    InnoPopoverComponent,
  ],
  templateUrl: "./time-tracking.component.html",
  styleUrl: "./time-tracking.component.scss",
  providers: [LayoutUtilsService],
})
export class TimeTrackingComponent implements OnInit, OnDestroy {

  public timeTrackingView = TimeTrackingViewEnum;
  public listTypeView = []
  public currentTypeView?: TimeTrackingViewEnum;
  public rate!: number;
  public isRate: boolean = false
  public listClient: any[] = [];
  public search: string = ''
  public filteredClient: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public datePickerMode: 'day' | 'week' | 'month' = 'day'

  public previewDateSelected?: Date;
  public previewUserSelected?: IFilterDropdownOption;
  public textSearch: string = '';
  private selectedClient!: string;

  private _subscriptions: Subscription[] = [];
  private storeService = inject(StoreService)
  private clientService = inject(ClientService)
  private searchSubject = new Subject<string>();
  private dataService = inject(DataService)
  private translate = inject(TranslateService)
  private destroyRef = inject(DestroyRef);
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private authServices = inject(AuthenticationService)

  @ViewChild('actiontMenuTrigger') actiontMenuTrigger!: MatMenuTrigger;
  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor(private newInvoiceDialog: NewInvoiceDialog, private generateInvoiceDialog: GenerateInvoiceDialog) { }

  ngOnInit(): void {
    this.listTypeView = [
      { label: this.translate.instant('TIMETRACKING.Day'), value: TimeTrackingViewEnum.Day },
      { label: this.translate.instant('TIMETRACKING.Week'), value: TimeTrackingViewEnum.Week },
      { label: this.translate.instant('TIMETRACKING.Month'), value: TimeTrackingViewEnum.Month },
      { label: this.translate.instant('TIMETRACKING.All'), value: TimeTrackingViewEnum.All },
    ]
    this.authServices.GetUser().subscribe((res: any) => {
      this.storeService.set_InforUser(res)
      const defaultUserSelected = {
        label: res.firstName + " " + res.lastName,
        value: res.id
      }
      this.previewUserSelected = defaultUserSelected
    })

    this._subscriptions.push(
      this.dataService.GetTimeTrackingFilter().subscribe((data) => {
        this.currentTypeView = data?.typeView
        this.previewDateSelected = data?.dateSelected
        this.textSearch = data?.textSearch ?? ''
        switch (this.currentTypeView) {
          case TimeTrackingViewEnum.Day:
            this.datePickerMode = 'day'
            break;
          case TimeTrackingViewEnum.Week:
            this.datePickerMode = 'week'
            break;
          case TimeTrackingViewEnum.Month:
            this.datePickerMode = 'month'
            break;
          default:
            this.datePickerMode = 'day'
            break;
        }
      })
    )
  }

  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }

  stopPropagation(event: any) {
    event.stopPropagation();
  }

  NewInvoice() {
    const result = this.listClient.filter(x => x.clientId.toString() == this.selectedClient.toString())
    if (result) {
      result[0].rate = this.rate
      result[0].newfromTimeTrackng = true
    }
    const dialogRef = this.newInvoiceDialog.open(result[0]);

    dialogRef.then((c) => {
      c.afterClosed().subscribe(result => {
      });
    });
  }

  Cancel() {
    this.actiontMenuTrigger.closeMenu();
  }

  Continue(event: any) {
    this.stopPropagation(event)
    this.isRate = true
  }

  handleSearch(search: string) {
    this.searchSubject.next(search);
  }

  ChooseClient() {
    this.clientService.ClientTimeTracking().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.listClient = res;
        const updatedfilterDates = this.listClient.map((item: any) => {
          let total = ""
          let total_hours = 0;
          let total_minutes = 0;
          item.timeTrackings.forEach((element: any) => {
            const [hours, minutes] = element.endTime.split(":").map(Number);
            total_hours += hours;
            total_minutes += minutes;
            if (total_minutes >= 60) {
              total_hours += Math.floor(total_minutes / 60);
              total_minutes = 0
            }
            total = (total_hours < 10 ? "0" + total_hours : total_hours) + ":" + (total_minutes < 10 ? "0" + total_minutes : total_minutes)
          });

          return { ...item, content: total };
        });
        this.listClient = updatedfilterDates;
        this.filteredClient.next(this.listClient.slice());
      }
    })
  }

  SelectCient($event: any) {
    const inputElement = $event.target as HTMLInputElement;
    this.selectedClient = inputElement.value;
  }

  handleChangeDateFilter(dateSelected: any) {
    this.timeTrackingProvider.handleChangeDateFilter(dateSelected)
  }

  handleNextDate() {
    this.timeTrackingProvider.handleNextDate()
  }

  handlePreDate() {
    this.timeTrackingProvider.handlePreDate()
  }

  handleChangeTypeView(_typeView: TimeTrackingViewEnum) {
    this.timeTrackingProvider.handleChangeTypeView(_typeView)
  }

  handleCloseFilter() {
    this.searchResultComponent.handleHideContent()
  }

  handleSearchTracking = debounceHandler((textSearch = '') => {

    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const newValueFilter = { ...currentFilter, textSearch }
    this.dataService.SetNewTimeTrackingFilter(newValueFilter)
  }, 1000)

  handleGenerateInvoice() {
    this.generateInvoiceDialog.open({});
  }
}
