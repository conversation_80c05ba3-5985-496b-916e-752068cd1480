import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { MemberService } from 'app/service/member.service';
import { Role } from 'app/enum/role.enum';
import { ToastService } from './../../../service/toast.service';
import { StoreService } from 'app/service/store.service';
import { UserBusinessService } from 'app/service/user-business.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { SharedModule } from 'app/module/shared.module';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { RoleMember } from 'app/utils/role-member';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { SpinnerService } from 'app/service/spinner.service';

@Component({
  selector: 'app-add-member-business',
  standalone: true,
  imports: [InnoModalWrapperComponent, SharedModule,
    CommonModule,
    MatFormFieldModule,],
  templateUrl: './add-member-business.component.html',
  styleUrl: './add-member-business.component.scss'
})
export class AddMemberBusinessComponent implements OnInit {
  RoleMember = RoleMember
  destroyRef = inject(DestroyRef);
  selected: string = Role.Accountant;
  router = inject(Router);
  private _toastService = inject(ToastService)
  private _spinnerService = inject(SpinnerService)
  private _memberService = inject(MemberService);
  private formBuilder = inject(UntypedFormBuilder)
  public memberForm!: UntypedFormGroup;
  public _storeService = inject(StoreService)
  private _userBusinessService = inject(UserBusinessService);

  static getComponent(): typeof AddMemberBusinessComponent {
    return AddMemberBusinessComponent;
  }

  constructor(public dialogRef: MatDialogRef<AddMemberBusinessComponent>,) {
    this.memberForm = this.formBuilder.group({
      firstname: ["", Validators.compose([Validators.required])],
      lastname: ["", Validators.compose([Validators.required])],
      email: ["", Validators.compose([Validators.required, Validators.email])],
    },


    );
  }
  ngOnInit(): void {

  }

  get f() {
    return this.memberForm.controls;
  }
  onSubmit() {
    this._spinnerService.show();
    let payload = {
      email: this.memberForm.controls['email'].value,
      role: this.selected,
      firstname: this.memberForm.controls['firstname'].value,
      lastname: this.memberForm.controls['lastname'].value
    }
    this._userBusinessService.AddMemberBusiness(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this._spinnerService.hide();
          this.dialogRef.close(res);
        }
        else {
          this._toastService.showError("Fail", "Member already exists")
        }
      }

    })

  }
  closeDialog() {
    this.dialogRef.close();
  }
}
