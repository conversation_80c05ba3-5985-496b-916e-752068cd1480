import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { InforUser } from './../dto/interface/inforUser.interface';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { AddTax } from 'app/dto/interface/addTax.interface';

@Injectable({
  providedIn: 'root'
})
export class StoreService {
  constructor() { }
  public roleBusiness!: string;
  private behaviorRoleBusines = new BehaviorSubject<string>("");
  public curencyCompany = new BehaviorSubject<string>("USD");
  public dateFormat = new BehaviorSubject<string>("dd/MM/YYYY");
  public _InforUser!: InforUser
  public UserBusiness!: UserBusiness
  ApplyTaxAll: boolean = false
  public Tax: AddTax[] = []
  setdateFormat(format: string) {
    this.dateFormat.next(format)
  }
  getdateFormat() {
    return this.dateFormat.value;
  }
  setCurencyCompany(curency: string) {
    this.curencyCompany.next(curency)
  }
  getRoleBusinessAsObservable() {
    return this.behaviorRoleBusines.asObservable()
  }
  set_ApplyTaxAll(data: boolean) {
    this.ApplyTaxAll = data
  }
  get_ApplyTaxAll() {
    return this.ApplyTaxAll;
  }
  set_UserBusiness(data: UserBusiness) {
    this.UserBusiness = data
  }
  get_UserBusiness() {
    return this.UserBusiness;
  }
  set_InforUser(data: InforUser) {
    this._InforUser = data
  }
  get_InforUser() {
    return this._InforUser;
  }
  setRoleBusiness(role: string) {
    this.roleBusiness = role
    this.behaviorRoleBusines.next(role)
  }
  getIsRunning() {
    return localStorage.getItem('isRunning') === 'true';
  }

  getRoleBusiness() {
    return this.roleBusiness;
  }
  setChooseBusiness(data: any) {
    localStorage.setItem("business", JSON.stringify(data))
  }
  getChooseBusiness() {
    return localStorage.getItem("business")
  }
  getIdBusiness() {
    let data = JSON.parse(localStorage.getItem("business")!);
    return data?.businessId
  }
  getBgColor(value: any) {
    let result = '';
    switch (value?.toUpperCase()) {
      case 'A':
        result = 'rgb(51 152 219)';
        break;
      case 'B':
        result = '#0cb929';
        break;
      case 'C':
        result = 'rgb(91, 101, 243)';
        break;
      case 'D':
        result = 'rgb(44, 62, 80)';
        break;
      case 'E':
        result = 'rgb(26, 188, 156)';
        break;
      case 'F':
        result = 'rgb(26, 65, 127)';
        break;
      case 'G':
        result = 'rgb(241, 196, 15)';
        break;
      case 'H':
        result = 'rgb(248, 48, 109)';
        break;
      case 'I':
        result = 'rgb(142, 68, 173)';
        break;
      case 'K':
        result = '#2209b7';
        break;
      case 'L':
        result = 'rgb(44, 62, 80)';
        break;
      case 'M':
        result = 'rgb(127, 140, 141)';
        break;
      case 'N':
        result = 'rgb(197, 90, 240)';
        break;
      case 'O':
        result = 'rgb(51 152 219)';
        break;
      case 'P':
        result = '#02c7ad';
        break;
      case 'Q':
        result = 'rgb(211, 84, 0)';
        break;
      case 'R':
        result = 'rgb(44, 62, 80)';
        break;
      case 'S':
        result = 'rgb(127, 140, 141)';
        break;
      case 'T':
        result = '#bd3d0a';
        break;
      case 'U':
        result = 'rgb(51 152 219)';
        break;
      case 'Y':
        result = 'rgb(241, 196, 15)';
        break;
      case 'V':
        result = '#759e13';
        break;
      case 'X':
        result = 'rgb(142, 68, 173)';
        break;
      case 'W':
        result = 'rgb(211, 84, 0)';
        break;
    }
    return result;

  }
  getIconFile(type: any): string {
    if (!type || typeof type !== 'string') {
      return './../../assets/file-icon/file.png'; // Default icon for invalid types
    }

    // Map MIME types to file extensions
    const mimeToExtensionMap: { [key: string]: string } = {
      "application/msword": "doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
      "application/vnd.ms-excel": "xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
      "application/vnd.ms-powerpoint": "ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
      "application/pdf": "pdf",
      "text/plain": "txt",
      "application/zip": "zip",
      "application/x-rar-compressed": "rar",
      "video/mp4": "mp4",
      "video/x-msvideo": "avi",
      "video/quicktime": "mov",
      "video/x-matroska": "mkv"
    };

    // Convert MIME type to extension if needed
    const normalizedType = mimeToExtensionMap[type] || type.toLowerCase();

    // Map extensions to icon paths
    const extensionToIconMap: { [key: string]: string } = {
      doc: './../../assets/file-icon/word.png',
      docx: './../../assets/file-icon/word.png',
      word: './../../assets/file-icon/word.png',
      w: './../../assets/file-icon/word.png',
      xls: './../../assets/file-icon/excel.png',
      xlsx: './../../assets/file-icon/excel.png',
      excel: './../../assets/file-icon/excel.png',
      ppt: './../../assets/file-icon/ppt.png',
      pptx: './../../assets/file-icon/ppt.png',
      powerpoint: './../../assets/file-icon/ppt.png',
      pdf: './../../assets/file-icon/pdf.png',
      txt: './../../assets/file-icon/txt.png',
      zip: './../../assets/file-icon/zip.png',
      rar: './../../assets/file-icon/zip.png',
      '7z': './../../assets/file-icon/zip.png',
      mp4: './../../assets/file-icon/video.png',
      avi: './../../assets/file-icon/video.png',
      mov: './../../assets/file-icon/video.png',
      mkv: './../../assets/file-icon/video.png'
    };

    return extensionToIconMap[normalizedType] || './../../assets/file-icon/file.png'; // Default icon
  }

}
