<div class="mt-8 pb-4 w-full">
    <div class="mt-4 gap-4 grid grid-cols-4 justify-between ">
        <div>
            <label
                class="block text-lg font-medium text-gray-700">Description</label>
            <textarea
                type="text"
                rows="1"
                [(ngModel)]="note"
                (ngModelChange)="NoteChanged($event)"
                placeholder="Description"
                class="w-70 border-gray-300 rounded-md shadow-sm p-2 focus:ring-blue-500 focus:border-blue-500">
        </textarea>

        </div>
        <div class="flex flex-col">
            <label
                class="block text-lg font-medium text-gray-700">Rate</label>
            <input
                type="number"
                [(ngModel)]="rate"
                (ngModelChange)="rateChanged($event)"
                placeholder="0"
                class="w-full border-gray-300 rounded-md shadow-sm p-2 focus:ring-blue-500 focus:border-blue-500" />
            <button [matMenuTriggerFor]="menu"
                #actiontMenuTrigger="matMenuTrigger"
                class="text-blue-600 mt-2 underline text-start">

                @if(nameTax!='')
                {
                {{nameTax}}

                }
                @else{
                Add Taxes
                }
            </button>
            <mat-menu class="customize" #menu="matMenu"
                yPosition="below">
                <div class="page-work-detail">
                    <app-add-taxes [listTax]="listTax"
                        [ApplyAll]="ApplyAll"
                        (emitTax)="handleEmitTax($event)"
                        (oncancel)="CloseTax($event)"
                        (onstop)="stopPropagation($event)">
                    </app-add-taxes>
                </div>
            </mat-menu>
        </div>
        <div>
            <label
                class="block text-lg font-medium text-gray-700">Qty</label>
            <input
                type="number"
                (ngModelChange)="rateChangedQty($event)"
                [(ngModel)]="qty"
                placeholder="0"
                class="w-full border-gray-300 rounded-md shadow-sm p-2 focus:ring-blue-500 focus:border-blue-500" />
        </div>
        <div>
            <label
                class="block text-lg font-medium text-gray-700">Line
                Total</label>
            <div class="flex items-center">
                $
                <input
                    type="number"
                    [(ngModel)]="total"
                    placeholder="0"
                    class="w-full border-gray-300 p-2 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" />
            </div>

        </div>

    </div>

</div>