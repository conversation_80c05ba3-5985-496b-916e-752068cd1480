import { PermissionService } from './../../../service/permission.service';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { GridAllModule } from '@syncfusion/ej2-angular-grids';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AddPermissionDialog } from '../../../service/dialog/add-permission.dialog';
import { Location } from '@angular/common';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-role-permission-project',
  standalone: true,
  imports: [
    InnoTabsComponent,
    GridAllModule,
    SharedModule,
  ],
  templateUrl: './role-permission-project.component.html',
  styleUrl: './role-permission-project.component.scss'
})
export class RolePermissionProjectComponent implements OnInit {

  public TYPE_TAB = {
    USER_GROUP: 1,
    USER: 2,
    ROLE: 3
  }
  public tabs = [
  ]
  public currentTab = this.TYPE_TAB.USER_GROUP
  private translate = inject(TranslateService)
  private destroyRef = inject(DestroyRef);
  private _permissionService = inject(PermissionService)
  public dataSource: any;

  constructor(
    private addPermissionDialog: AddPermissionDialog,
    private location: Location
  ) {

    this.tabs = [
      { label: this.translate.instant('ROLE.RolePermission'), value: this.TYPE_TAB.USER_GROUP },
      { label: this.translate.instant('ROLE.User'), value: this.TYPE_TAB.USER },
      { label: this.translate.instant('ROLE.Role'), value: this.TYPE_TAB.ROLE },
    ]
  }

  ngOnInit(): void { }

  handleChangeTab(value: any) {
    this.currentTab = value;
  }

  GetAllPermission() {
    this._permissionService.GetPermission().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.dataSource = res.data
      }
    }
    )
  }
  OpenDialog() {
    const dialogRef = this.addPermissionDialog.open({});
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {

        }
      });
    })
  }

  handleBack() {
    this.location.back();
  }
}
