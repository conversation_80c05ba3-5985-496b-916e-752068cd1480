import { environment } from 'environments/environment';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { Payment } from '../dto/interface/payment.interface';
import { Observable } from 'rxjs';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class PaymentService {


  private http = inject(HttpClient)
  constructor() { }

  CreatedPayment(payload: any) {
    return this.http.post(UrlApi + '/Payment/CreatedPayment', payload);
  }

  GetAllPayment(payload: Parameter): Observable<PaginatedResponse<Payment>> {
    return this.http.get<PaginatedResponse<Payment>>(UrlApi + `/Payment/GetAllPayment?Page=${payload.Page}&PageSize=${payload.PageSize}&Search=${payload.Search}&InvoiceId=${payload.InvoiceId}`);
  }
  GetAllPaymentCompany(payload: Parameter): Observable<PaginatedResponse<Payment>> {
    return this.http.get<PaginatedResponse<Payment>>(UrlApi + `/Payment/GetAllPaymentCompany?Page=${payload.Page}&PageSize=${payload.PageSize}&Search=${payload.Search}`);
  }


}
