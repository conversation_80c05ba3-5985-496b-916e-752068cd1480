<div class="w-full min-h-dvh pb-3 flex flex-col">
  <div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
    <div
      class="container-full flex justify-between items-center flex-wrap gap-2">
      <p class="text-text-primary text-headline-lg-bold">
        {{'TIMETRACKING.Title'|
        translate }}
      </p>

      <!-- <button class="button-size-md button-primary" (click)="ChooseClient()"
        #actiontMenuTrigger="matMenuTrigger" [matMenuTriggerFor]="menuinvoice">
        <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
        Generate Invoice Old
      </button> -->

      <button class="button-size-md button-primary"
        (click)="handleGenerateInvoice()">
        <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
        {{'TIMETRACKING.TitleGenerate'|
        translate }}
      </button>
      <mat-menu class="customize" #menuinvoice="matMenu">
        @if(!isRate)
        {
        <div class="p-2" (click)="stopPropagation($event)">
          <div>
            <span class="font-bold"> {{'TIMETRACKING.GenerateInvoice'|
              translate }}</span>
            <br>
            <label class="font-bold text-lg">{{'TIMETRACKING.ChooseClient'|
              translate }}</label>
          </div>
          <div>
            <div class="flex w-full justify-start mb-3">
              <div class="flex items-center max-w-sm justify-end">
                <label for="simple-search"
                  class="sr-only">{{'TIMETRACKING.Search'|
                  translate }}</label>
                <div class="relative w-full">
                  <div
                    class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
                    <svg class="w-4 h-4" aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 20 20">
                      <path stroke="currentColor"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z" />
                    </svg>
                  </div>
                  <input type="text" id="simple-search"
                    [(ngModel)]="search"
                    (ngModelChange)="handleSearch($event)"
                    [ngModelOptions]="{standalone: true}"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5  "
                    placeholder="Client..." required />
                </div>

              </div>
            </div>
            @for( option of filteredClient | async; track option;let
            i=$index)
            {

            <div class="flex items-center">
              <input
                (change)="SelectCient($event)"
                type="radio"
                [value]="option.clientId"
                name="radioGroup"
                class="w-5 h-10" />
              <div class="flex flex-col pl-3">
                <label>{{ option.clientName }}</label>
                <span
                  class="text-sm text-gray-400">Total:
                  {{option.content}}</span>
              </div>

            </div>
            }
            <hr>
            <div class="flex justify-between">
              <button type="button"
                (click)="Cancel()"
                class="py-2.5 px-5 me-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100">Cancel</button>

              <button type="button"
                (click)="Continue($event)"
                class="text-white bg-green-600 hover:bg-green-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Continue</button>
            </div>
          </div>
        </div>
        }@else{
        <div class="p-2" (click)="stopPropagation($event)">
          <div>
            <span class="font-bold"> {{'TIMETRACKING.GenerateInvoice'|
              translate }}</span>
            <br>
            <label class="font-bold text-lg">Set a standard rate for
              your time</label>
          </div>
          <div>
            <div>
              <div>
                <label for="rate"
                  class="block mb-1 text-sm font-medium text-gray-900">Standard
                  Rate</label>
                <input type="text" id="rate"
                  [(ngModel)]="rate"
                  [ngModelOptions]="{standalone: true}"
                  class="bg-gray-50 border w-40 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"
                  placeholder="$0.00" required />
              </div>
              <span>/hr</span>
              <div class="flex items-center">
                <span class="material-icons">
                  error
                </span>
                <span class="pl-2 w-fit">
                  Time you track in innoBook will use this rate
                  by default, but you can customize rates for each
                  project.
                </span>
              </div>
            </div>
            <hr class="text-white">
            <div class="flex justify-end">

              <button type="button"
                [disabled]="!rate"
                (click)="NewInvoice()"
                [ngClass]="{'bg-green-600' : rate,'bg-gray-500': !rate}"
                class="text-white focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2">Set
                and Continue</button>
            </div>
          </div>
        </div>
        }
      </mat-menu>
    </div>
  </div>

  <div
    class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">
    <div class="flex items-center gap-[14px] flex-wrap">
      <app-inno-tabs
        [tabs]="listTypeView"
        [value]="currentTypeView"
        (onChange)="handleChangeTypeView($event)" />
      @if(currentTypeView !== timeTrackingView.All) {
      <div class="flex items-center gap-[10px]">
        <div class="flex items-center gap-[2px]">
          <button class="button-icon" (click)="handlePreDate()">
            <img src="../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
          </button>
          <button class="button-icon" (click)="handleNextDate()">
            <img src="../../../assets/img/icon/ic_arrow_right.svg" alt="Icon">
          </button>
        </div>
        <div class="w-[180px]">
          <app-inno-datepicker
            [value]="previewDateSelected"
            placeholder="Select date"
            [mode]="datePickerMode"
            (onChange)="handleChangeDateFilter($event)" />
        </div>
      </div>
      }
    </div>
    <div class="flex items-center gap-[8px]">
      <div class="w-full w-[340px] mxw600:w-[240px]">
        <app-inno-input-search
          height="36px"
          [value]="textSearch"
          (onChange)="handleSearchTracking($event)" />
      </div>
      <app-inno-popover
        position="bottom-start"
        [content]="contentFilter"
        [isClearPadding]="true"
        [isClickOnContentToClose]="false">
        <button target class="button-icon border-2 border-border-primary">
          <img src="../../../assets/img/icon/ic_filter.svg" alt="Icon">
        </button>
        <ng-template #contentFilter>
          <div class="w-[480px] mxw600:w-[350px]">
            <app-filter-tracking (onCancel)="handleCloseFilter()" />
          </div>
        </ng-template>
      </app-inno-popover>
    </div>

  </div>

  <div class="container-full mt-[24px]">
    <app-add-time-tracking-record />
  </div>

  <div class="w-full mt-[24px] grow flex flex-col">
    @if(currentTypeView === timeTrackingView.Day) {
    <app-view-day class="grow flex flex-col" />
    }

    @if(currentTypeView === timeTrackingView.Week) {
    <app-view-week class="grow flex flex-col" />
    }

    @if(currentTypeView === timeTrackingView.Month) {
    <app-view-month />
    }

    @if(currentTypeView === timeTrackingView.All) {
    <app-view-all class="grow flex flex-col" />
    }
  </div>
</div>
