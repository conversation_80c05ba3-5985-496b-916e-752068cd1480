import { Component, ElementRef, EventEmitter, Input, Output, SimpleChanges, ViewChild } from '@angular/core';

@Component({
  selector: 'app-inno-enter-edit-hours',
  standalone: true,
  imports: [],
  templateUrl: './inno-enter-edit-hours.component.html',
  styleUrl: './inno-enter-edit-hours.component.scss'
})
export class InnoEnterEditHoursComponent {
  @ViewChild('inputField') inputField!: ElementRef;
  @Input() value?: string = '';
  @Input() dynamicWidth?: number = 0;
  @Output() onChange = new EventEmitter<{ newHours: string, isAlreadyEdit: boolean }>();
  public previewTimeEnd?: string = '';
  private isAlreadyEdit = false

  constructor() { }

  handleSetTimeEnd(event: any) {
    this.isAlreadyEdit = true
    this.previewTimeEnd = event?.target?.value ?? ''
  }

  ngAfterViewInit(): void {
    if (this.inputField) {
      this.inputField.nativeElement.focus();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    let newValue = '00:00'
    const _value = changes['mode']?.currentValue ?? changes['value']?.currentValue
    if (_value) newValue = _value
    this.previewTimeEnd = newValue === '00:00' ? '' : newValue.substring(0, 5);
  }

  updateTimeEnd(value: string) {
    this.onChange.emit({
      newHours: value,
      isAlreadyEdit: this.isAlreadyEdit
    })
    this.isAlreadyEdit = false
    this.previewTimeEnd = value === '00:00' ? '' : value
  }

  onBlur() {

    if (!this.previewTimeEnd) {
      return this.updateTimeEnd('00:00')
    }

    let value = this.previewTimeEnd;
    // Remove any non-numeric characters except the colon
    value = value.replace(/[^0-9:]/g, '');

    // Format to HH:MM
    if (value.indexOf(':') !== -1) {
      const parts = value.split(':');
      if (parts.length === 2 && parts[1] === '') {
        // Case where input is like "4:" (missing minutes)
        value = parts[0] + ':00';  // Default minutes to '00'
      } else if (parts.length > 2) {
        // Case where input contains more than one colon (e.g. "4::5")
        value = parts[0] + ':' + (parts[1] || '00');
      }
    }
    // Split into hours and minutes
    let [hours, minutes] = value.split(':');
    // If no minutes are entered, set it to '00'
    if (!minutes) {
      minutes = '00';
    }

    // Ensure hours are within 00-23 and minutes within 00-59
    if (hours && Number(hours) > 23) {
      hours = '23';
    } else if (hours && Number(hours) < 10 && hours.length === 1) {
      // If hours is a single digit, pad it with '0'
      hours = '0' + hours;
    }

    if (minutes && Number(minutes) > 59) {
      minutes = '59';
    } else if (minutes && Number(minutes) < 10 && minutes.length === 1) {
      // If minutes is a single digit, pad it with '0'
      minutes = '0' + minutes;
    }

    const resultHours = `${hours || '00'}:${minutes || '00'}`
    this.updateTimeEnd(resultHours)
  }
}
