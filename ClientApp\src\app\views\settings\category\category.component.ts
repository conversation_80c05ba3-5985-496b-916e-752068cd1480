import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { CategoryService } from './../../../service/category.service';
import { SharedModule } from 'app/module/shared.module';
import { StoreService } from 'app/service/store.service';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { Component, DestroyRef, inject, ViewChild } from '@angular/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ActionEventArgs, GridAllModule, GridComponent, PagerModule, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { DetailCategoryDialog } from '../../../service/dialog/detail-category.dialog';
import { NewCategoryDialog } from '../../../service/dialog/new-category.dialog';
import { Location } from '@angular/common';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';

@Component({
  selector: 'app-category',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    PagerModule,
    SharedModule,
    GridAllModule,
    InnoInputSearchComponent,
    InnoTableActionComponent,
    InnoSpinomponent
  ],
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss'
})
export class CategoryComponent {
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public listChooseCategory: any[] = []
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  public search: string = ''
  public _storeService = inject(StoreService)
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public isLoading = false;
  private _subscriptions: Subscription[] = [];

  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private translate = inject(TranslateService);
  private layoutUtilsService = inject(LayoutUtilsService)
  private _toastService = inject(ToastService)
  private _categoryService = inject(CategoryService)
  private activatedRoute = inject(ActivatedRoute);
  private searchSubject = new Subject<string>();

  constructor(
    private detailCategoryDialog: DetailCategoryDialog,
    private newCategoryDialog: NewCategoryDialog,
    private location: Location
  ) { }

  handleSearch(search: string) {
    this.searchSubject.next(search);
  }

  ngOnInit(): void {
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.sort ? this.GetAllCategoryAsync(this.currentPage, "", filter) : this.GetAllCategoryAsync(this.currentPage, "")
      }
      else {
        this.GetAllCategoryAsync(this.currentPage, "")
      }

    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      if (search) {
        this.search = search;
        this.GetAllCategoryAsync(this.currentPage, this.search)
      }
      else {
        this.search = "";
        this.GetAllCategoryAsync(this.currentPage, "")
      }
    });
    this._subscriptions.push(sb)
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllCategoryAsync(this.currentPage, "")
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }

  GetAllCategoryAsync(page: number, search: string, filter?: any) {
    this.isLoading = true
    let payload: Parameter = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      Filter: filter

    }
    this._categoryService.GetAllCategoryAsync(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.isLoading = false
        this.totalPages = res.totalRecords
        this.dataSource = res.data
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };
        }
      }
    }
    )
  }

  onRowSelecting(event: any): void {
    if (event?.data?.length > 0) {
      event?.data.forEach((element: any) => {
        let index = this.listChooseCategory.findIndex(x => x == element?.id)
        if (index < 0) {
          this.listChooseCategory.push(element?.id)
        }
      });

    } else {
      let index = this.listChooseCategory.findIndex(x => x == event?.data?.id)
      if (index < 0) {
        this.listChooseCategory.push(event?.data?.id)
      }
    }

  }

  onRowDeselecting(event: any): void {
    if (event?.data?.length > 0) {
      this.listChooseCategory = [];
    }
    else {

      let index = this.listChooseCategory.findIndex(x => x == event.data?.id)
      if (index >= 0) {
        this.listChooseCategory.splice(index, 1)
      }
    }

  }

  handleEdit(item: any) {
    const dialogRef = this.detailCategoryDialog.open(item);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetAllCategoryAsync(this.currentPage, "")
        }
      });
    });
  }

  OpenDialog() {
    const dialogRef = this.newCategoryDialog.open({});
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (res) {
          this.GetAllCategoryAsync(this.currentPage, "")
        }
      });
    });
  }

  handleDelete(item: any) {
    this.layoutUtilsService.alertDelete({
      title: this.translate.instant('CATEGORY.DeleteCategory'),
      description: this.translate.instant('COMMON.ConfirmDelete')
    }).then(isConfirm => {
      if (!isConfirm) return

      this._categoryService.DeleteCategory([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.GetAllCategoryAsync(this.currentPage, "")
          this._toastService.showSuccess(this.translate.instant('TOAST.Delete'), this.translate.instant('TOAST.Success'));
        }
      });
    })
  }

  handleBack() {
    this.location.back();
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.GetAllCategoryAsync(this.currentPage, "", filter)
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllCategoryAsync(this.currentPage, "")
    }

  }
  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
