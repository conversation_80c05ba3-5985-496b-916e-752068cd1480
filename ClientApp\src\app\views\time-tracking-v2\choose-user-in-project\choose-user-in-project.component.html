<div>
    <div class="max-h-72">

        @for( item of filteredMember | async; track item;let i=$index)
        {
        <div class="users-container">
            <div
                class="pl-2 cursor-pointer  hover:bg-gray-300 hover:rounded-md mb-3"
                (click)="select(item)">
                <div class="flex items-center">

                    @if(item?.firstName)
                    {
                    <ngx-avatars [size]="30"
                        [name]="item.firstName.slice(0,1)"></ngx-avatars>
                    <span class="ml-2"> </span>
                    }
                    <div class="pl-2 pr-2">
                        <div>{{item.firstName}}
                            {{item.lastName}}</div>
                    </div>
                </div>

            </div>
        </div>
        }
    </div>

    @if(empty)
    {
    <div class="text-center">
        No result
    </div>
    }

</div>
