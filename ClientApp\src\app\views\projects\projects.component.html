<!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <p class="text-text-primary text-headline-lg-bold">
      Projects
    </p>
    @if(role!=Role.Employee)
    {
    <button class="button-size-md button-primary" (click)="OpenDialog()">
      <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
      {{'PROJECT.NewProject'|translate}}
    </button>
    }

  </div>
</div>
@if(dataSource?.length==0)
{
<div class="w-full">
  <app-inno-empty-data
    [title]="'EMPTY.NoResult'" />
</div>
}

<!-- End Header page -->
@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
  <app-inno-spin />
</div>
} @else {
<div
  class="container-full mt-[24px] grid grid-cols-4 mxw1100:grid-cols-2 mxw600:grid-cols-1">
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'PROJECT.ActiveProject'|translate}}</p>
    <p class="text-text-primary text-headline-lg-bold">{{
      ProjectResponse?.totalActive }}</p>
  </div>
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'PROJECT.TotalLogged'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">{{ProjectResponse?.totalLoggedFormatted}}</p>
  </div>
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'PROJECT.TotalUnbilled'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">{{ProjectResponse?.totalUnbilled}}</p>
  </div>
  @if(role!=Role.Accountant)
  {
  <div
    class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
    <p
      class="text-text-tertiary text-text-md-semibold">{{'PROJECT.TotalAmount'|translate}}</p>
    <p
      class="text-text-primary text-headline-lg-bold">${{ProjectResponse?.totalAmount
      | decimal:2 | formatNumber}}</p>
  </div>
  }
</div>

<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
  <div class="w-full max-w-[300px]">
    <app-inno-input-search [value]="search" (onChange)="handleSearch($event)" />
  </div>

  <select (change)="handleSelectStatusProject($event)"
    [(ngModel)]="filterProjectDefault" class="dropdown-md">
    <option value="1">{{"PROJECT.ActiveProject"|translate}}</option>
    @if(role === Role.Admin) {
    <option value="2">{{"PROJECT.UnActiveProject"|translate}}</option>
    }
    <option value="3">{{"PROJECT.ArchiveProjects"|translate}}</option>
  </select>

  <div class="w-[200px]">
    <app-inno-datepicker [placeholder]="'COMMON.DatePlaceholder'|translate"
      [value]="dateSelected"
      (onChange)="handleChangeDateFilter($event)" />
  </div>
</div>

<div class="w-full mt-[12px]"
  [ngClass]="{ 'mb-28':storeService.getIsRunning() }">
  <ejs-grid
    #grid
    class="customTable"
    [dataSource]="dataSource"
    [allowSelection]="true"
    [allowSorting]="true"
    [sortSettings]='sortOptions'
    (actionBegin)="onActionBegin($event)">
    <e-columns>
      <!-- <e-column type="checkbox" width="30"></e-column> -->
      <e-column [headerText]="'PROJECT.GIRD.ProjectName' | translate"
        width="200" field="ProjectName">
        <ng-template #template let-data>
          <div class="w-full">
            <p class="text-text-primary text-text-md-semibold line-clamp-1">
              {{ data.projectName ?? '' }}
            </p>
            @if(data.description) {
            <p class="text-text-xs-regular text-text-tertiary line-clamp-1">
              {{ data.description }}
            </p>
            }
          </div>
        </ng-template>
      </e-column>
      <e-column [headerText]="'PROJECT.GIRD.Client' | translate" width="180"
        field="ClientName">
        <ng-template #template let-data>
          <p class="text-text-md-regular text-text-primary">
            {{ data?.clientName ?? '' }}
          </p>
        </ng-template>
      </e-column>
      <e-column [headerText]="'PROJECT.GIRD.EndDate' | translate" width="120"
        field="EndDate">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-regular"> {{data.endDate |
            date: 'MM/dd/yyyy'}}</p>
        </ng-template>
      </e-column>
      <e-column [headerText]="'PROJECT.GIRD.Members' | translate" width="150">
        <ng-template #template let-data>
          <app-inno-user-list-overlap [overLength]="data.members?.length-2"
            [avatars]="data.members.slice(0,2)" />
        </ng-template>
      </e-column>
      <e-column [headerText]="'PROJECT.GIRD.Logged' | translate" width="100">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-regular">
            {{ data.totalLogger }}
          </p>
        </ng-template>
      </e-column>
      <e-column [headerText]="'PROJECT.GIRD.Unbilled' | translate" width="100">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-regular">
            {{ data.totalunbilled }}
          </p>
        </ng-template>
      </e-column>
      @if(role!=Role.Employee)
      {
      <e-column [headerText]="'PROJECT.GIRD.Amount' | translate" width="150">
        <ng-template #template let-data>
          <div class="w-full flex gap-[8px] flex-wrap">
            <p class="text-text-primary text-text-md-bold">
              ${{data.totalbilled | decimal:2 | formatNumber}}
            </p>
          </div>
        </ng-template>
      </e-column>
      }
      @if(role!=Role.Employee&& role!=Role.Accountant &&
      role!=Role.Contractor)
      {
      <e-column headerText width="100">
        <ng-template #template let-data>
          <div class="flex items-center">
            @if(!data.isArchive) {
            <button class="button-icon" (click)="handleEdit(data.id)">
              <img class="w-[20px]" src="../../../assets/img/icon/ic_edit.svg"
                alt="Icon">
            </button>
            }
            <app-inno-popover [content]="contentPopover">
              <button target class="button-icon">
                <img class="w-[20px]"
                  src="../../../assets/img/icon/ic_three_dots_verticel.svg"
                  alt="Icon">
              </button>
            </app-inno-popover>
            <ng-template #contentPopover>
              @if(data.isActive&&!data.isArchive)
              {
              <div class="flex w-[78px] flex-col">
                <button
                  class="w-full h-[32px] text-text-sm-regular hover:bg-bg-secondary"
                  (click)="handleArchive(data,true)">
                  {{'COMMON.Archive' |translate}}
                </button>
                <button
                  class="w-full h-[32px] text-text-sm-regular text-text-danger hover:bg-bg-secondary"
                  (click)="handleDelete(data,false)">
                  {{'COMMON.Delete' |translate}}
                </button>
              </div>
              } @else if(data.isActive&&data.isArchive)
              {
              <div class="flex w-[78px] flex-col">
                <button
                  class="w-full h-[32px] text-text-sm-regular hover:bg-bg-secondary"
                  (click)="handleArchive(data,false)">
                  {{'COMMON.Activate' |translate}}
                </button>
              </div>
              }
              @else if(!data.isActive){
              <div class="flex w-[78px] flex-col">
                <button
                  class="w-full h-[32px] text-text-sm-regular hover:bg-bg-secondary"
                  (click)="handleDelete(data,true)">
                  {{'COMMON.Activate' |translate}}
                </button>
              </div>
              }

            </ng-template>
          </div>
        </ng-template>

      </e-column>
      }
    </e-columns>
  </ejs-grid>
  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>
}
