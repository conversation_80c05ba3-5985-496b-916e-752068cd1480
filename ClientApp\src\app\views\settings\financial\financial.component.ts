import { StoreService } from './../../../service/store.service';
import { ToastService } from './../../../service/toast.service';
import { SpinnerService } from './../../../service/spinner.service';
import { CompanyService } from './../../../service/company.service';
import { CURRENCY } from './../../../utils/currency-item';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Location } from '@angular/common';
import { SharedModule } from 'app/module/shared.module';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';

@Component({
  selector: 'app-financial',
  standalone: true,
  imports: [
    SharedModule,
    InnoFormSelectSearchComponent,
    InnoFormInputComponent
  ],
  templateUrl: './financial.component.html',
  styleUrl: './financial.component.scss'
})
export class FinancialComponent implements OnInit {

  public currencyOptions: IFilterDropdownOption[] = CURRENCY.map(e => ({
    label: e.name,
    value: e.code
  }))
  public monthOptions: IFilterDropdownOption[] = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ]
  public dayOptions: IFilterDropdownOption[] = []
  public rate: number = 0
  public selectedCurrency: string = "CAD"
  public _storeService = inject(StoreService)
  public selectedMonth: number = new Date().getMonth() + 1;

  private _companyServices = inject(CompanyService)
  private spiner_services = inject(SpinnerService)
  private destroyRef = inject(DestroyRef);
  private _toastService = inject(ToastService)
  private selectedDay: number = 1


  constructor(private location: Location) {}

  ngOnInit(): void {
    this.dayOptions = this.getDaysInMonth(this.selectedMonth)
  }

  handleChangeCurrency(itemSelected: IFilterDropdownOption) {
    this.selectedCurrency = itemSelected.value
  }

  handleChangeMonth(itemSelected: IFilterDropdownOption) {
    this.selectedMonth = itemSelected.value
    this.dayOptions = this.getDaysInMonth(this.selectedMonth)
  }

  handleChangeDay(itemSelected: IFilterDropdownOption) {
    this.selectedDay = itemSelected.value;
  }

  handleChangeRate(value: any) {
    this.rate = value
  }

  getDaysInMonth(month: number): IFilterDropdownOption[] {
    const date = new Date(this.selectedMonth, month - 1, 1);
    const days: number[] = [];
    while (date.getMonth() === month - 1) {
      days.push(date.getDate());
      date.setDate(date.getDate() + 1);
    }

    return days.map(day => ({
      value: day,
      label: day.toString()
    }))
  }

  Submit() {
    this.spiner_services.show();
    let payload = {
      rate: this.rate,
      fiscalMonth: this.selectedMonth,
      currency: this.selectedCurrency,
      fiscalDay: this.selectedDay
    }
    this._storeService.setCurencyCompany(this.selectedCurrency)
    this._companyServices.UpdateFinancial(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      this._toastService.showSuccess("Success", "Update tax and financial information successfully.");
      this.spiner_services.hide();
    }
    )
  }

  handleBack() {
    this.location.back();
  }
}
