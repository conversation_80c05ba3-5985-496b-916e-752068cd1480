import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-inno-tabs-v2',
  templateUrl: './inno-tabs-v2.component.html',
  styleUrls: ['./inno-tabs-v2.component.scss'],
  standalone: true,
  imports: []
})
export class InnoTabsV2Component {

  @Input() public tabs: Array<{ label: string, value: any }> = [];
  @Input() public value: any;
  @Output() onChange = new EventEmitter<any>();

  constructor() {}

  handleSelectTab(value: any) {
    this.onChange.emit(value ?? null);
  }
}
