import { Injectable } from '@angular/core';
import { AlertConfirmDialog } from '../../service/dialog/alert-confirm.dialog';

export enum MessageType {
  Create,
  Read,
  Update,
  Delete
}

@Injectable()
export class LayoutUtilsService {
  constructor(
    private alertConfirmDialog: AlertConfirmDialog
  ) { }

  alertDelete(args: {
    title?: string,
    description?: string,
    textSubmit?: string,
    textCancel?: string,
  }): Promise<boolean> {

    const { title, description, textSubmit = "COMMON.Delete", textCancel, } = args

    return new Promise((resolve) => {

      const dialogRef = this.alertConfirmDialog.open({
        title,
        description,
        textSubmit,
        textCancel,
        classNameSubmitButton: 'bg-object-danger-primary hover:bg-bg-danger-strong-hover'
      });
      dialogRef.then((c) => {
        c.afterClosed().subscribe((res) => {
          resolve(res ?? false)
        })
      });
    });
  }

  alertConfirm(args: {
    title?: string,
    description?: string,
    textSubmit?: string,
    textCancel?: string,
  }): Promise<boolean> {

    const { title, description, textSubmit, textCancel, } = args

    return new Promise((resolve) => {

      const dialogRef = this.alertConfirmDialog.open({ title, description, textSubmit, textCancel });
      dialogRef.then((c) => {
        c.afterClosed().subscribe((res) => {
          resolve(res ?? false)
        })
      });
    });
  }
}
