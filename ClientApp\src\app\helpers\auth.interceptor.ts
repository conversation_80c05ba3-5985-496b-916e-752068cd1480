// import { AuthenticationService } from 'app/auth/service/authentication.service';
// import { inject } from '@angular/core';
// import { HttpInterceptorFn, HttpRequest, HttpHandlerFn } from '@angular/common/http';
// import { Router } from '@angular/router';
// import { BehaviorSubject, throwError, Observable } from 'rxjs';
// import { catchError, switchMap, filter, take } from 'rxjs/operators';

// let isRefreshing = false;
// const refreshTokenSubject: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

// export const AuthInterceptor: HttpInterceptorFn = (request: HttpRequest<any>, next: HttpHandlerFn): Observable<any> => {
//     const authService = inject(AuthenticationService);
//     const router = inject(Router);
//     const token = authService.getAccessToken();

//     if (token) {
//         request = addToken(request, token);
//     }

//     return next(request).pipe(
//         catchError(err => {
//             if (err.status === 401) {
//                 return handle401Error(request, next, authService, router);
//             }
//             return throwError(() => err);
//         })
//     );
// };


// function addToken(request: HttpRequest<any>, token: string): HttpRequest<any> {
//     return request.clone({
//         setHeaders: {
//             Authorization: `Bearer ${token}`
//         }
//     });
// }


// function handle401Error(
//     request: HttpRequest<any>,
//     next: HttpHandlerFn,
//     authService: AuthenticationService,
//     router: Router
// ): Observable<any> {
//     if (!isRefreshing) {
//         isRefreshing = true;
//         refreshTokenSubject.next(null);

//         return authService.refreshAccessToken({ refreshToken: authService.getRefreshToken() }).pipe(
//             switchMap((response: any) => {
//                 isRefreshing = false;
//                 authService.saveToken_cookie(response.accessToken, response.refreshToken)
//                 refreshTokenSubject.next(authService.getAccessToken());
//                 return next(addToken(request, authService.getAccessToken()));
//             }),
//             catchError(() => {
//                 isRefreshing = false;
//                 authService.logout();
//                 return throwError(() => new Error('Refresh token failed.'));
//             })
//         );
//     } else {

//         return refreshTokenSubject.pipe(
//             filter(token => token != null),
//             take(1),
//             switchMap((token: string) => {
//                 return next(addToken(request, token));
//             })
//         );
//     }
// }
