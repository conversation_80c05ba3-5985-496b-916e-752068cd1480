import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit } from '@angular/core';
import { SharedModule } from './module/shared.module';
import { ProgressSpinnerComponent } from './spinner/spinner.module';
import { ToastShowComponent } from './spinner/toast.module';
import { RouterModule } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  standalone: true,
  imports: [
    SharedModule,
    ProgressSpinnerComponent,
    ToastShowComponent,
    RouterModule
  ]
})
export class AppComponent implements OnInit {
  public translate = inject(TranslateService)
  constructor() {
    if (localStorage.getItem("lang")) {
      this.translate.use(localStorage.getItem("lang"))
    }
    else {
      this.translate.setDefaultLang('en');
      localStorage.setItem("lang", "en")
    }

  }

  ngOnInit() {
  }

  title = 'innologiciel';
}
