<div class="w-full pb-3">
  <!-- Header page -->
  <div class="w-full py-[24px] border-b border-border-primary">
    <div
      class="container-full flex justify-between items-center flex-wrap gap-2">
      <p class="text-text-primary text-headline-lg-bold">
        Clients
      </p>

      <button class="button-size-md button-primary" (click)="NewClient()">
        <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
        {{'CLIENT.NewClient' | translate}}
      </button>
    </div>
  </div>
  <!-- End Header page -->
  @if(isLoading) {
  <div class="flex justify-center items-center grow py-3">
    <app-inno-spin />
  </div>
  } @else {
  <div
    class="container-full mt-[24px] flex items-center justify-between flex-wrap gap-2">
    <app-inno-tabs [tabs]="tabs" [value]="currentTab"
      (onChange)="handleChangeTab($event)" />
  </div>

  <div class="w-full mt-[24px]">
    @if(currentTab === TYPE_TAB.ALL_CLIENTS) {
    <div
      class="container-full grid grid-cols-4 mxw1100:grid-cols-2 mxw600:grid-cols-1">
      <div
        class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
        <p
          class="text-text-tertiary text-text-md-semibold">{{'CLIENT.ActiveClient'
          | translate}}</p>
        <p class="text-text-primary text-headline-lg-bold">{{totalPages}}</p>
      </div>
      <div
        class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
        <p
          class="text-text-tertiary text-text-md-semibold">{{'CLIENT.TotalOverdue'
          | translate}}</p>
        <p
          class="text-text-primary text-headline-lg-bold">${{clientResponse?.totalOverdue
          | decimal:2 | formatNumber}}</p>
      </div>
      <div
        class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
        <p
          class="text-text-tertiary text-text-md-semibold">{{'CLIENT.TotalDraft'
          | translate}}</p>
        <p
          class="text-text-primary text-headline-lg-bold">${{clientResponse?.totalDraft
          | decimal:2 | formatNumber}}</p>
      </div>
      <div
        class="w-full min-h-[112px] border-[4px] border-border-primary flex flex-col justify-end gap-[8px] pb-[16px] px-[24px] bg-bg-primary">
        <p class="text-text-tertiary text-text-md-semibold">
          {{'CLIENT.TotalOutstanding'
          | translate}}</p>
        <p
          class="text-text-primary text-headline-lg-bold">${{clientResponse?.totalAmount
          | decimal:2 | formatNumber}}</p>
      </div>
    </div>

    <div class="container-full mt-[32px]">
      <div class="w-full max-w-[300px]">
        <app-inno-input-search [value]="search"
          (onChange)="handleSearch($event)" />
      </div>
    </div>

    <div class="w-full mt-[12px]"
      [ngClass]="{ 'mb-28':storeService.getIsRunning() }">
      <ejs-grid
        #grid
        class="customTable"
        [dataSource]="dataSource"
        [allowSorting]="true"
        [sortSettings]='sortOptions'
        (actionBegin)="onActionBegin($event)">
        <e-columns>
          <e-column headerText="Clients" width="170" field="clientName">
            <ng-template #template let-data>
              <div class="w-full flex gap-[12px] items-center">
                <ngx-avatars
                  class="shrink-0"
                  [size]="35"
                  [name]="data?.clientName" />
                <div class="w-full ">
                  <p class="text-text-primary text-text-md-semibold">
                    {{ data?.clientName ?? '' }}
                  </p>
                </div>
              </div>
            </ng-template>
          </e-column>
          <e-column field="CompanyName"
            [headerText]="'CLIENT.GIRD.Company' | translate" width="170">
          </e-column>
          <e-column [headerText]="'CLIENT.GIRD.Credit' | translate"
            width="100">
          </e-column>
          <e-column
            field="totalOutstanding"
            [headerText]="'CLIENT.TotalOutstanding' | translate"
            width="180">
            <ng-template #template let-data>
              <span class="text-text-primary text-text-md-semibold">
                ${{ data?.totalOutstanding | decimal:2 | formatNumber }}
              </span>
            </ng-template>
          </e-column>
          <e-column [headerText]="'COMMON.Action' | translate" width="100">
            <ng-template #template let-item>
              <app-inno-table-action
                (onEdit)="handleEditClient(item)"
                (onDelete)="handleDeleteClient(item)" />
            </ng-template>
          </e-column>
        </e-columns>
      </ejs-grid>

      <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
      </ejs-pager>
    </div>
    }

    @if(currentTab === TYPE_TAB.MAIL_HISTORY) {
    <div class="container-full pt-[20dvh]">
      <app-inno-empty-data
        title="The feature is in development." />
    </div>
    }
  </div>
  }
</div>
