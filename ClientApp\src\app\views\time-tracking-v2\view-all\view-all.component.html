@if(isLoading) {
<div class="flex justify-center items-center grow py-3">
  <app-inno-spin />
</div>
} @else {
@if(!dataSource.length) {
<div class="container-full py-[24px] flex flex-col items-center">
  <img class="h-[120px]" src="../../../../assets/img/clock.png" alt="Icon">
  <div class="w-full">
    <p class="text-text-tertiary text-headline-xs-semibold text-center">
      {{'TIMETRACKING.NoTime'|
      translate }}
    </p>
    <p class="text-text-tertiary text-text-sm-regular text-center">
      {{'TIMETRACKING.DescribeNoTime'|
      translate }}
    </p>
  </div>
</div>
} @else {
@if(showTotalTime)
{
<div
  class="w-fit gap-[36px] ml-[22px] bg-bg-primary rounded-md border-2 border-dashed py-[16px] px-[12px] border-border-secondary flex justify-between items-start flex-wrap transition-all">
  <p class="container-full text-text-primary text-headline-sm-bold">
    {{'TIMETRACKING.TotalTime'|translate}} : {{totalTime}}
  </p>
</div>
}
<div class="mt-[24px] flex flex-col gap-[36px]"
  [ngClass]="{ 'mb-10':storeService.getIsRunning() }">
  @for(item of dataSource; track item) {
  <app-group-day-tracking
    [dayName]="item.labelDate"
    [dataSource]="item.listTracking"
    [isDisableShowEmpty]="true" />
  }
</div>
@if(dataSource.length) {
<div class="container-full mt-[12px]">
  <app-inno-pagination
    [currentPagesize]="20"
    [totalPages]="totalPages"
    (onChangePagesize)="handleChangePagesize($event)"
    (callbackGoToPage)="triggerRefreshListTimeTracking()" />
</div>
}
}
}
