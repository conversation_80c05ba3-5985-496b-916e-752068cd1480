
<app-inno-modal-wrapper [title]="'TEAMMEMBERS.AddMemberForm.ProfileMember'"
    (onClose)="handleClose()">

    <div class="input-content rounded-md  w-full border border-gray-300">

        <form class="p-3"
            [formGroup]="profileForm" (ngSubmit)="onSubmit()">
            <div class="grid gap-6 mb-6 md:grid-cols-2">
                <app-inno-form-input
                    [label]="'TEAMMEMBERS.AddMemberForm.FirstName' | translate"
                    [placeholder]="'TEAMMEMBERS.AddMemberForm.FirstName' | translate"
                    [formControl]="f['firstname']"
                    [value]="f['firstname'].value"
                    [errorMessages]="{
              required: 'TEAMMEMBERS.AddMemberForm.FirstNameRequired' | translate
            }" />

                <app-inno-form-input
                    [label]="'TEAMMEMBERS.AddMemberForm.LastName' | translate"
                    [placeholder]="'TEAMMEMBERS.AddMemberForm.LastName' | translate"
                    [formControl]="f['lastname']"
                    [value]="f['lastname'].value"
                    [errorMessages]="{
          required: 'TEAMMEMBERS.AddMemberForm.LastNameRequired' | translate
        }" />

            </div>

            <div class="form-group mb-3">
                <div>
                    <label for="_name"
                        class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">{{'TEAMMEMBERS.AddMemberForm.Email'
                        | translate}}
                    </label>
                    <input type="email" id="_email"
                        formControlName="email"
                        [placeholder]="'TEAMMEMBERS.AddMemberForm.Email' | translate"
                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" />
                </div>
            </div>
            <div class="w-full text-center">
                <button type="submit"
                    [disabled]="!profileForm.valid"
                    [ngClass]="{'bg-green-700 hover:bg-green-800' : profileForm.valid,'bg-gray-400': !profileForm.valid}"
                    class="text-white font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center">{{'BUTTON.Save'|translate}}</button>
            </div>
        </form>
    </div>
</app-inno-modal-wrapper>
