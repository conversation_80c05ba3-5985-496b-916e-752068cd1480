// .buttonTabItem {
//   padding-left: 12px;
//   padding-right: 12px;
//   height: 100%;
//   flex-shrink: 0;
//   position: relative;
//   @apply text-text-sm-semibold;
//   @apply text-text-tertiary;
//   @apply bg-bg-primary;
// }

// .buttonTabItem.active {
//   @apply bg-bg-brand-primary;
//   @apply text-text-brand-primary;
// }

// // .buttonTabItem:not(.active):hover {
// //   background-color: #ececec;
// //   transition: all .3s;
// // }

// .buttonTabItem:not(:last-child)::before {
//   content: "";
//   position: absolute;
//   height: 100%;
//   width: 1px;
//   @apply bg-border-primary;
//   top: 0;
//   right: 0;
// }

// V2
.buttonTabItem {
  padding-left: 12px;
  padding-right: 12px;
  height: 100%;
  flex-shrink: 0;
  position: relative;

  border: 1px solid;
  @apply text-text-sm-semibold;
  @apply text-text-tertiary;
  @apply bg-bg-primary;
  @apply border-border-primary;
}

.buttonTabItem.active {
  @apply bg-bg-brand-primary;
  @apply text-text-brand-primary;
  @apply border-text-brand-primary;
}

.buttonTabItem:hover {
  @apply bg-bg-brand-primary;
  transition: all .3s;
}

.buttonTabItem:first-child {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.buttonTabItem:last-child {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
