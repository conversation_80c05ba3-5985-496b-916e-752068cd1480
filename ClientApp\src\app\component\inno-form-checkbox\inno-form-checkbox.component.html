<div class="flex">
  <label class="flex gap-[8px] cursor-pointer">
    @if (formControl) {
      <input
        type="checkbox"
        [checked]="checked"
        class="customCheckboxHTML"
        [formControl]="formControl"
        (change)="handleChange($event)"
      />
    } @else {
      <input
        type="checkbox"
        [checked]="checked"
        class="customCheckboxHTML"
        (change)="handleChange($event)"
      />
    }

    <div class="text-text-sm-regular text-text-primary">
      <ng-content></ng-content>
    </div>
  </label>
</div>
