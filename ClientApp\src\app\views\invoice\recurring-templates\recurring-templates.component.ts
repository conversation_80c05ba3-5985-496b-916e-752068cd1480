import { NewInvoiceComponent } from './../dialog/new-invoice/new-invoice.component';
import { BreadcrumComponent } from 'app/component/breadcrum/breadcrum.component';
import { Component, inject } from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { NewInvoiceDialog } from '../../../service/dialog/new-invoice.dialog';

@Component({
  selector: 'app-recurring-templates',
  standalone: true,
  imports: [BreadcrumComponent, MatDialogModule],
  templateUrl: './recurring-templates.component.html',
  styleUrl: './recurring-templates.component.scss'
})
export class RecurringTemplatesComponent {
  constructor(private newInvoiceDialog: NewInvoiceDialog) { }

  private dialog = inject(MatDialog)
  NewInvoice() {
    const dialogRef = this.newInvoiceDialog.open({});

    dialogRef.then((c) => {
      c.afterClosed().subscribe(result => {
      });
    });
  }
}
