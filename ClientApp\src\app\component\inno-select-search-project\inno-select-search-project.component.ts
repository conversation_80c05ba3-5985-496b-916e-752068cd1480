import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { Subscription } from 'rxjs';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { AddProjectDialog } from '../../service/dialog/add-project.dialog';

@Component({
  selector: 'app-inno-select-search-project',
  templateUrl: './inno-select-search-project.component.html',
  styleUrls: ['./inno-select-search-project.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    AvatarModule
  ]
})
export class InnoSelectSearchProjectComponent implements OnDestroy, OnInit {

  @Input() public templateTrigger: TemplateRef<any> | null = null
  @Input() public defaultTextSearch: string = ''
  @Input() public isShowCreateButton: boolean = true;
  @Input() public isOnlySelectProject?: boolean = false;
  @Input() public isTimetracking?: boolean = false;
  @Input() public value: any
  @Input() public placeholderSearch?: string
  @Output() public onSelect = new EventEmitter<IFilterDropdownOption>();
  @Output() public onGetInfoSelectedValue = new EventEmitter<IFilterDropdownOption>();

  public listOptionPreview: IFilterDropdownOption[] = []
  public listOptionOriginal: IFilterDropdownOption[] = []
  public isLoading: boolean = false

  private dropdownOptionService = inject(DropdownOptionsService)
  protected unsubscribe: Subscription[] = [];

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor(private addProjectDialog: AddProjectDialog) { }

  ngOnInit(): void {
    if (this.value) this.loadData(true)

  }

  async loadData(isGetInfoSelectedValue?: boolean) {

    this.isLoading = true
    const dropdownOption = this.isTimetracking == true ? await this.dropdownOptionService.getDropdownOptionsProjectAndClientTimeTracking() : await this.dropdownOptionService.getDropdownOptionsProjectAndClient()

    this.listOptionOriginal = dropdownOption
    this.handleSearch(this.defaultTextSearch)
    if (isGetInfoSelectedValue && this.value) {
      const selectedValue = this.listOptionOriginal.find(x => x.value == this.value)
      this.onGetInfoSelectedValue.emit(selectedValue)
    }
    this.isLoading = false
  }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal
      .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)

    const result: any[] = [];
    this.listOptionPreview.forEach(element => {
      let projectAdd = false;
      let clientAdd = false;
      this.listOptionOriginal.filter(x => x.metadata.type == 'project').forEach((item: any) => {
        if (element.value == item.metadata.objectClient.id) {
          if (!projectAdd) {
            result.push(element)
            projectAdd = true;
          }
          result.push(item)
        }
        else {
          let checkClient = this.listOptionPreview.find(x => x.metadata?.type == 'client')
          if (!projectAdd && !checkClient) {
            let client = this.listOptionOriginal.find(x => x.metadata?.client?.id == element.metadata?.objectClient?.id)
            let check = result.find(x => x.value == element.metadata?.objectClient?.id)
            if (!check) {
              clientAdd = true;
              result.push(client)
            }
            if (clientAdd || check) {
              result.push(element)
              projectAdd = true;

            }
          }
        }

      });
    });

    this.listOptionPreview = result
  }

  handleCreateNewProject() {
    this.addProjectDialog.open(null);
    this.handleCloseSearchResult()
  }

  private handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  isProjectOption(item: IFilterDropdownOption) {
    return item.metadata?.type == 'project'
  }

  handleChooseOption(item: IFilterDropdownOption) {
    if (this.isOnlySelectProject && item.metadata?.type != 'project') return

    if (item.value == this.value) return

    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
