<div class="filter-bar-content">
    @for(item of dates; track item; let i = $index){
    <button class="btn btn-secondary btn-filter-date" (click)="selectDate(item)"
        [ngClass]="{'active': checkIsActive(item?.date)}">
        <div>
            <div class="flex flex-row gap-2">
                <p class="fs-6 fw-bold">{{ item?.date | date:'EEE' }}</p>
                <p class="center">{{ item?.date | date:'dd' }}</p>
            </div>
            <p [ngClass]="{'text-right': item.isPrimary}">{{ item?.content
                }}</p>
        </div>

    </button>
    }
</div>
