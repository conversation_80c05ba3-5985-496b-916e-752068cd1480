import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';

@Injectable({ providedIn: 'root' })
export class AddPaymentDialog extends AsyncDialog<any> {
  async open(data): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/payment/payments/dialog/add-payment/add-payment.component'
    );

    return this.matDialog.open(
      importedModuleFile.AddPaymentComponent.getComponent(),
      {
        panelClass: 'custom_dialog',
        data,
        scrollStrategy: new NoopScrollStrategy(),
        disableClose: true
      }
    );
  }
}
