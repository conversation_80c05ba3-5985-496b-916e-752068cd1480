import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { GetAllUserBusinessQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { UserBusiness } from '../dto/interface/userBusiness.interface';
import { Observable } from 'rxjs';
import { User } from '../dto/interface/user.interface';
import { ResultAddMember } from 'app/dto/interface/resultAddMember.interface';
import { RequestStatus } from 'app/dto/interface/RequestStatus.interface';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class UserBusinessService {
  private http = inject(HttpClient)
  private router = inject(Router);
  constructor() { }

  CreateUserBusiness(payload: any): Observable<UserBusiness[]> {
    return this.http.post<UserBusiness[]>(UrlApi + '/Business/user-business', payload);
  }
  GetUserBusiness(): Observable<UserBusiness> {
    return this.http.get<UserBusiness>(UrlApi + '/Business/user-business').pipe(val => val);
  }
  GetBusinessById(businessId: string): Observable<UserBusiness> {
    return this.http.get<UserBusiness>(UrlApi + `/Business/user-business-byid?businessId=${businessId}`);
  }
  GetInfoCompany(): Observable<UserBusiness> {
    return this.http.get<UserBusiness>(UrlApi + `/Business/GetInfoCompany`);
  }
  GetAllUserBusiness(params: GetAllUserBusinessQueryParam): Observable<UserBusiness[]> {
    const query = { ...params };
    Object.keys(query).forEach(key => (query[key] === null || query[key] === undefined) && delete query[key]);
    return this.http.get<UserBusiness[]>(UrlApi + `/Business/GetAllUserBusiness`, { params: query });
  }
  AddMemberBusiness(payload: any): Observable<ResultAddMember> {
    return this.http.post<ResultAddMember>(UrlApi + '/Business/AddMemberBusiness', payload);
  }
  DeleteMemberInBusiness(id: string): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + `/Business/DeleteMemberInBusiness?memberId=${id}`, null);
  }

  userBusinessById(UserId: string): Observable<UserBusiness> {
    return this.http.get<UserBusiness>(UrlApi + `/Business/userBusinessById?UserId=${UserId}`);
  }
  UpdateRoleMember(UserId: string, role: string): Observable<UserBusiness> {
    return this.http.get<UserBusiness>(UrlApi + `/Business/UpdateRoleMember?UserId=${UserId}&role=${role}`);
  }

  UpdateStatus(payload: RequestStatus): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + `/Business/UpdateStatus`, payload);
  }

  SendMailAddMember(payload: User): Observable<object> {
    return this.http.post(UrlApi + `/Business/SendMailAddMember`, payload);

  }

}
