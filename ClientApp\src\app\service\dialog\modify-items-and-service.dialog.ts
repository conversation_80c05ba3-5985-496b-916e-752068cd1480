import { Injectable } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { AsyncDialog } from './dialogService';
import { NoopScrollStrategy } from '@angular/cdk/overlay';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';

export interface IModifyItemsAndServiceDialog {
  mode: ItemAndServiceViewEnum,
  serviceInfo?: any
  isShowProject?: boolean
}

@Injectable({ providedIn: 'root' })
export class ModifyItemsAndServiceDialog extends AsyncDialog<any> {
  async open(data: IModifyItemsAndServiceDialog): Promise<MatDialogRef<any>> {
    const importedModuleFile = await import(
      '../../views/item-services/dialog-modify-item-service/dialog-modify-item-service.component'
    );

    return this.matDialog.open(
      importedModuleFile.DialogModifyItemServiceComponent.getComponent(),
      {
        disableClose: true,
        panelClass: 'custom_dialog',
        data,
        width: '500px',
        scrollStrategy: new NoopScrollStrategy(),
      }
    );
  }
}
