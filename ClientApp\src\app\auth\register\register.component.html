<app-auth-layout>
  <div class="w-full max-w-md space-y-8 animate-fade-up">
    <div class="text-center">
      <p class="text-headline-lg-bold">{{'REGISTER.TitleCreateAccount' |
        translate}} </p>
      <p class="mt-2 text-text-sm-regular text-text-secondary">
        {{'REGISTER.Describe' | translate}}
      </p>
    </div>

    <div class="mt-10">
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <div class="flex flex-col gap-[16px]">
          <div class="w-full grid md:grid-cols-2 gap-[10px]">
            <div class="w-full">
              <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
                {{'REGISTER.REGISTER_FORM.FirstName' | translate}}
              </p>
              <div class="relative">
                <i
                  class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">person</i>
                <app-inno-form-input
                  [placeholder]="'REGISTER.REGISTER_FORM.EnterFirstName' | translate"
                  inputClassName="!h-[46px] pl-[40px]"
                  [formControl]="f['firstname']"
                  [value]="f['firstname'].value"
                  [errorMessages]="{ required: 'REGISTER.REGISTER_FORM.FirstNameRequired' | translate }" />
              </div>
            </div>

            <div class="w-full">
              <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
                {{'REGISTER.REGISTER_FORM.LastName' | translate}}
              </p>
              <div class="relative">
                <i
                  class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">person</i>
                <app-inno-form-input
                  [placeholder]="'REGISTER.REGISTER_FORM.EnterLastName' | translate"
                  inputClassName="!h-[46px] pl-[40px]"
                  [formControl]="f['lastname']"
                  [value]="f['lastname'].value"
                  [errorMessages]="{ required: 'REGISTER.REGISTER_FORM.LastNameRequired' | translate  }" />
              </div>
            </div>
          </div>

          <div class="w-full">
            <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
              {{'REGISTER.REGISTER_FORM.Email' | translate}}
            </p>
            <div class="relative">
              <i
                class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">email</i>
              <app-inno-form-input
                type="email"
                [placeholder]="'REGISTER.REGISTER_FORM.EnterEmail' | translate"
                inputClassName="!h-[46px] pl-[40px]"
                [formControl]="f['email']"
                [value]="f['email'].value"
                [errorMessages]="{
                  required: 'REGISTER.REGISTER_FORM.EmailRequired' | translate,
                  email: 'REGISTER.REGISTER_FORM.InvalidEmail' | translate
                }" />
            </div>
          </div>

          <div class="w-full">
            <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
              {{'REGISTER.REGISTER_FORM.Password' | translate}}
            </p>
            <div class="relative">
              <i
                class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">lock</i>
              <app-inno-form-input
                [type]="isShowPassword ? 'text' : 'password'"
                [placeholder]="'REGISTER.REGISTER_FORM.EnterPassword' | translate"
                inputClassName="!h-[46px] px-[40px]"
                [formControl]="f['password']"
                [value]="f['password'].value"
                [errorMessages]="{
                  required: 'REGISTER.REGISTER_FORM.PasswordRequired' | translate,
                  minlength: 'REGISTER.REGISTER_FORM.PasswordMinLength' | translate
                }" />
              <i (click)="handleToggleShowPassword()"
                class="material-icons absolute top-[12px] right-[10px] z-10 text-text-tertiary !text-[22px] cursor-pointer">
                {{ isShowPassword ? 'visibility_off' : 'visibility' }}
              </i>
            </div>
          </div>

          <div class="w-full">
            <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
              {{'REGISTER.REGISTER_FORM.ConfirmPassword' | translate}}
            </p>
            <div class="relative">
              <i
                class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">lock</i>
              <app-inno-form-input
                [type]="isShowPassword ? 'text' : 'password'"
                [placeholder]="'REGISTER.REGISTER_FORM.EnterPasswordAgain' | translate"
                inputClassName="!h-[46px] px-[40px]"
                [formControl]="f['confirmPassword']"
                [value]="f['confirmPassword'].value"
                [errorMessages]="{
                  required:'REGISTER.REGISTER_FORM.PasswordRequired' | translate,
                  confirmPasswordValidator:'REGISTER.REGISTER_FORM.ConfirmPasswordValidator' | translate,
                }" />
              <i (click)="handleToggleShowPassword()"
                class="material-icons absolute top-[12px] right-[10px] z-10 text-text-tertiary !text-[22px] cursor-pointer">
                {{ isShowPassword ? 'visibility_off' : 'visibility' }}
              </i>
            </div>
          </div>

          @if(businessId) {
          <div class="w-full">
            <p class="text-text-secondary text-text-sm-semibold !mb-[5px]">
              {{'REGISTER.REGISTER_FORM.Company' | translate}}
            </p>
            <div class="relative">
              <i
                class="material-icons absolute top-[12px] left-[10px] z-10 text-text-tertiary !text-[22px]">business</i>
              <app-inno-form-input
                placeholder="Enter company"
                inputClassName="!h-[46px] pl-[40px]"
                [formControl]="f['company']"
                [value]="f['company'].value" />
            </div>
          </div>
          }

          <div class="w-full">
            <app-inno-form-checkbox
              [formControl]="f['agree']"> {{'REGISTER.REGISTER_FORM.AgreeTo' |
              translate}}
            </app-inno-form-checkbox>
          </div>
        </div>

        <button type="submit"
          class="button-size-md button-primary w-full justify-center mt-[34px]">
          {{'REGISTER.REGISTER_FORM.CreateButton' | translate}}
        </button>
      </form>

      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-border-primary"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-text-tertiary">
              {{'REGISTER.REGISTER_FORM.OrContinue' | translate}}</span>
          </div>
        </div>
      </div>
      <div class="w-full mt-6">
        <app-login-social></app-login-social>
      </div>

      <div class="mt-6 text-center text-text-sm-regular">
        {{'REGISTER.REGISTER_FORM.AlreadyAccount' | translate}}
        <a routerLink="/sign-in"
          class="font-semibold text-text-brand-primary">
          {{'REGISTER.REGISTER_FORM.SignIn' | translate}}</a>
      </div>
    </div>
  </div>
</app-auth-layout>
