import { SpinnerService } from 'app/service/spinner.service';
import { Component, DestroyRef, inject } from '@angular/core';
import { ConfirmPasswordValidator } from '../register/confirm-password.validator';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { AuthenticationService } from '../service/authentication.service';
import { ToastService } from 'app/service/toast.service';
import { SharedModule } from 'app/module/shared.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [SharedModule,
    MatFormFieldModule,
    RouterModule],
  templateUrl: './reset-password.component.html',
  styleUrl: './reset-password.component.scss'
})
export class ResetPasswordComponent {
  public showPassword!: boolean;
  public resetForm!: UntypedFormGroup;
  with_button!: number;
  token!: string;
  destroyRef = inject(DestroyRef);
  private router = inject(Router)
  private active_route = inject(ActivatedRoute)
  private auth_services = inject(AuthenticationService)
  private spinnerService = inject(SpinnerService)
  private formBuilder = inject(UntypedFormBuilder)
  private _toastService = inject(ToastService)

  constructor(
  ) {

    this.resetForm = this.formBuilder.group({
      password: ["", Validators.compose([Validators.required, Validators.minLength(6)])],
      confirmPassword: ["", Validators.compose([Validators.required])],
    },
      {
        validator: ConfirmPasswordValidator("password", "confirmPassword")
      }
    );

  }
  togglePassword() {
    const passwordInput = document.getElementById("password") as HTMLInputElement;
    const showIcon = document.getElementById("showIcon");
    const hideIcon = document.getElementById("hideIcon");

    if (passwordInput?.type === "password") {
      passwordInput.type = "text";
      showIcon?.classList.remove("!hidden");
      hideIcon?.classList.add("!hidden");
    } else {
      passwordInput.type = "password";
      showIcon?.classList.add("!hidden");
      hideIcon?.classList.remove("!hidden");
    }
  }
  ngOnInit() {
    this.token = this.auth_services.getParamsToken()!;
    //if (!this.auth_services.isJwtExpiredToken(this.token)) {
    //  this.router.navigate(["/login"]);
    //  this._toastService.showError('', 'User.Reset.Password.Token.Invalid');
    //}
  }


  get f() {
    return this.resetForm.controls;
  }
  onSubmit() {
    //if (!this.auth_services.isJwtExpiredToken(this.token)) {
    //  this.router.navigate(["/login"]);
    //  this._toastService.showError('', 'User.Reset.Password.Token.Invalid');
    //}
    //else {

    this.spinnerService.show();
    let payload = {
      token: decodeURIComponent(this.token).replace(/\s/g, "+"),
      pass: this.resetForm.controls["password"].value,
    }
    this.auth_services.ChangePassWithToken(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        this.spinnerService.hide();
        this._toastService.showSuccess('', 'User.Reset.Password.Sucessfully');
        this.router.navigate(['/login']);
      }
      else {
        this.spinnerService.hide();
        this._toastService.showError('', 'User.Reset.Password.Token.Invalid');
      }
    }

    )
  }

}
