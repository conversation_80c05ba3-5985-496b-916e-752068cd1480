import { CommonModule } from '@angular/common';
import { Component, Input, TemplateRef } from '@angular/core';

@Component({
  selector: 'app-breadcrum',
  standalone: true,
  imports: [ CommonModule ],
  templateUrl: './breadcrum.component.html',
  styleUrl: './breadcrum.component.scss'
})
export class BreadcrumComponent {
  @Input("actionTemplate") public actionTemplate!: TemplateRef<any>;
  @Input('title') public title: string = '';
}
