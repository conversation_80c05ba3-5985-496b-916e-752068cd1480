<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
    <div class="w-full max-w-[300px]">
        <app-inno-input-search [value]="search"
            (onChange)="handleSearch($event)" />
    </div>

</div>
@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
    <app-inno-spin size="lg" />
</div>
}@else {
<div class="w-full mt-[12px]"
    [ngClass]="{ 'mb-28':_storeService.getIsRunning() }">
    <ejs-grid
        #grid
        class="customTable"
        [dataSource]="dataSource"
        [allowSelection]="true"
        [sortSettings]='sortOptions'
        (actionBegin)="onActionBegin($event)"
        [allowSorting]="true"
        [selectionSettings]="selectionOptions">
        <e-columns>
            <!-- <e-column type="checkbox" width="30"></e-column> -->
            <e-column
                [headerText]="'ITEMS_SERVICES.GIRD.ServiceName' | translate"
                width="200" field="serviceName">
                <ng-template #template let-data>
                    <p class="text-text-md-regular text-text-primary">
                        {{data.serviceName}}</p>
                </ng-template>
            </e-column>
            <e-column
                [headerText]="'ITEMS_SERVICES.GIRD.Description' | translate"
                width="200" field="description">
                <ng-template #template let-data>
                    <p
                        class="text-text-md-regular text-text-primary whitespace-pre-wrap">
                        {{data.description?? ''}}</p>
                </ng-template>
            </e-column>
            <e-column [headerText]="'ITEMS_SERVICES.GIRD.Taxes' | translate"
                width="200">
                <ng-template #template let-data>
                    <p
                        class="text-text-md-regular text-text-primary whitespace-pre-wrap">
                        {{ getNameSelectedTaxes(data?.taxes) }}</p>
                </ng-template>
            </e-column>
            <e-column [headerText]="'ITEMS_SERVICES.GIRD.Rate' | translate"
                width="140" field="rate">
                <ng-template #template let-data>
                    <p class="text-text-md-regular text-text-primary">
                        {{data.rate?? ''}}</p>
                </ng-template>
            </e-column>

            <e-column headerText width="100">
                <ng-template #template let-data>
                    <app-inno-table-action
                        (onEdit)="handleEdit(data)"
                        (onDelete)="handleDelete(data)"
                        (onArchive)="handleArchive(data)" />
                </ng-template>
            </e-column>
        </e-columns>
    </ejs-grid>
    <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
</div>
}
