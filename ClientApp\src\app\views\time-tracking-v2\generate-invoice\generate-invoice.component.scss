p {
  margin-bottom: 0;
}

.selected {
  @apply bg-bg-brand-primary;
}

.selected .txtTitle {
  @apply text-text-sm-semibold;
}

.selected .txtTitle,
.selected .txtDescription {
  @apply text-text-brand-primary
}

.selectProjectTableLayout {
  width: 100%;
  display: grid;
  grid-template-columns: minmax(200px, 1fr) 200px 130px 100px 120px;
}

.addBorderBottom {
  border-bottom: 1px solid;
  padding-top: 8px;
  padding-bottom: 8px;
  @apply border-border-primary
}

.selectProjectTableLayout .addBorderBottom:not(:last-child) {
  padding-right: 8px;
}