<div class="flex min-h-dvh bg-white">
  <div class="hidden md:block md:w-1/2 p-10 bg-bg-brand-strong-hover">
    <div class="w-full h-full flex flex-col justify-between gap-[10px]">
      <div class="grow flex flex-col">
        <div class="w-full">
          <img src="../../../assets/img/logo_white.png" alt="logo"
            class="h-[30px] object-contain" />
        </div>
        <div class="grow flex flex-col justify-center items-center pb-20">
          <div class="flex items-center gap-4 mb-6 justify-center">
            <i class="material-icons text-white !text-[80px]">browse_gallery</i>
            <i
              class="material-icons text-white !text-[80px]">stacked_bar_chart</i>
          </div>
          <h2
            class="text-4xl font-display font-semibold tracking-tight text-white text-center">
            {{'LOGIN.TimeTrackingMade' | translate }}
          </h2>
          <p
            class="mt-4 text-white/80 leading-relaxed max-w-md text-center mx-auto">
            {{'LOGIN.Describe' | translate }}
          </p>
        </div>
      </div>
      <div class="w-full">
        <p class="text-text-brand-tertiary text-text-sm-regular font-semibold">
          © 2025 InnoLogiciel Inc.
        </p>
      </div>
    </div>
  </div>

  <div class="w-full md:w-1/2 flex items-center justify-center py-8 container">
    <ng-content></ng-content>
  </div>
</div>
