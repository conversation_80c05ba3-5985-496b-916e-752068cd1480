<app-inno-popover
  position="bottom-start"
  [content]="templateSearchProject"
  [isClickOnContentToClose]="false"
  [isClearPadding]="true"
  (onOpen)="loadData()">

  @if(templateTrigger) {
  <ng-container target *ngTemplateOutlet="templateTrigger;"></ng-container>
  } @else {
  <button target class="dropdown-invisible flex gap-[4px]">
    Select user<img class="w-[16px] translate-y-[2px]"
      src="../../../assets/img/icon/ic_arrow_down_gray.svg" alt="Icon">
  </button>
  }

  <ng-template #templateSearchProject>
    <div class="min-w-[320px]">
      <app-inno-input-search-result
        [placeholder]="'COMMON.Search'|translate"
        (onChange)="handleSearch($event)"
        [data]="listOptionPreview"
        [isNotFound]="!listOptionPreview.length"
        [isEmptyData]="!listOptionOriginal.length"
        [isLoading]="isLoading"
        [optionTemplate]="optionTemplate"
        [defaultValue]="defaultTextSearch"
        [footerTemplate]="isShowCreateUserButton ? buttonCreateNew : null">
        <ng-template #optionTemplate let-item>
          <div
            (click)="handleChooseOption(item)"
            [class.selected]="item.value === value"
            class="w-full flex p-[8px] items-center gap-[10px] hover:bg-gray-100 rounded-md cursor-pointer">
            @if(item.label!='')
            {
            <ngx-avatars
              [size]="32"
              bgColor="{{_storeService.getBgColor(item.label.slice(0,1))}}"
              [name]="item.label" />
            }@else{
            <ngx-avatars
              [size]="32"
              bgColor="{{_storeService.getBgColor(item.metadata.email.slice(0,1))}}"
              [name]="item.metadata.email" />
            }

            <div class="w-full">
              <p class="line-clamp-1 text-text-primary text-text-sm-regular">
                @if(item.label!='')
                {
                {{ item.label }}
                }@else{
                {{ item.metadata.email }}
                }

              </p>
              @if(item.description) {
              <p class="line-clamp-1 text-text-tertiary text-text-xs-regular">
                {{ item.description }}
              </p>
              }
            </div>
          </div>
        </ng-template>
        <ng-template #buttonCreateNew>
          <button (click)="handleCreateNew()"
            class="p-[12px] gap-[12px] text-text-brand-primary text-text-sm-semibold w-full flex items-center hover:bg-bg-brand-primary rounded-md cursor-pointer">
            <img src="../../../assets/img/icon/ic_add_green.svg" alt="Icon">
            Create a new client
          </button>
        </ng-template>
      </app-inno-input-search-result>
    </div>
  </ng-template>
</app-inno-popover>
