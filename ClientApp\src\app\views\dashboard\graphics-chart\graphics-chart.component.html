
<div class="flex items-center justify-between p-4">
    <h4
        class="text-gray-500 font-bold">{{'DASHBOARD.TitleChartGraphics'|translate}}</h4>
</div>
<div style="display: grid;">
    <div class="container overflow-auto">
        <ngx-charts-line-chart
            [results]="lineChartProps.results"
            [view]="lineChartProps.view"
            [autoScale]="lineChartProps.autoScale"
            [timeline]="lineChartProps.timeline"
            [gradient]="lineChartProps.gradient"
            [xAxis]="lineChartProps.xAxis"
            [yAxis]="lineChartProps.yAxis"
            [legend]="lineChartProps.legend"
            [showXAxisLabel]="lineChartProps.showXAxisLabel"
            [showYAxisLabel]="lineChartProps.showYAxisLabel"
            [xAxisLabel]="lineChartProps.xAxisLabel"
            [yAxisLabel]="lineChartProps.yAxisLabel">
        </ngx-charts-line-chart>
    </div>

</div>