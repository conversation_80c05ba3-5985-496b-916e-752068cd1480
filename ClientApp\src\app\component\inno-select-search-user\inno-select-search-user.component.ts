import { StoreService } from 'app/service/store.service';
import { Component, EventEmitter, inject, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { Subscription } from 'rxjs';
import { IFilterDropdownOption, IOptionFilterProjectOrClient } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';

@Component({
  selector: 'app-inno-select-search-user',
  templateUrl: './inno-select-search-user.component.html',
  styleUrls: ['./inno-select-search-user.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    AvatarModule
  ]
})
export class InnoSelectSearchUserComponent implements OnInit, OnDestroy {

  @Input() public templateTrigger: TemplateRef<any> | null = null
  @Input() public value: any
  @Input() public isAll?: boolean
  @Input() public defaultTextSearch: string = ''
  @Input() public isShowCreateUserButton: boolean = false

  @Output() public onSelect = new EventEmitter<IFilterDropdownOption>();
  @Output() public onGetInfoSelectedValue = new EventEmitter<IFilterDropdownOption>();

  public listOptionPreview: IFilterDropdownOption[] = []
  public listOptionOriginal: IFilterDropdownOption[] = []
  public isLoading: boolean = false

  private dropdownOptionService = inject(DropdownOptionsService)
  public _storeService = inject(StoreService)
  private unsubscribe: Subscription[] = [];

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor() { }

  ngOnInit(): void {
    if (this.value) this.loadData(true)
  }

  async loadData(isGetInfoSelectedValue?: boolean) {

    this.isLoading = true
    const dropdownOption = await this.dropdownOptionService.getDropdownOptionsUser()
    this.listOptionOriginal = dropdownOption
    if (this.isAll) {
      let item = {
        label: "All",
        value: "all",
        metadata: {}
      }
      this.listOptionOriginal.unshift(item)
    }
    this.handleSearch(this.defaultTextSearch)
    if (isGetInfoSelectedValue && this.value) {
      const selectedValue = this.listOptionOriginal.find(x => x.value == this.value)
      this.onGetInfoSelectedValue.emit(selectedValue)
    }
    this.isLoading = false
  }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal
      .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)
    const result: any[] = [];
    this.listOptionPreview.forEach(element => {
      result.push(element);
      this.listOptionOriginal.filter(x => x.metadata.type == 'project').forEach((item: any) => {
        if (element.value == item.metadata.objectClient.id) {
          result.push(item)
        }

      });
    });
    this.listOptionPreview = result


  }

  private handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  handleChooseOption(item: IOptionFilterProjectOrClient) {
    if (item.value == this.value) return

    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  handleCreateNew() {
    this.handleCloseSearchResult()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
