p {
  margin-bottom: 0;
}

.paginationWrapper {
  width: 100%;
  display: flex;
}

.myPagination {
  display: flex;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid #EDF0F6;
  overflow: hidden;
}

.paginationButton {
  border: none;
  outline: none;
  background-color: transparent;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  line-height: 16px;
  color: #0F182E;
}

.paginationButton.active {
  background-color: #0089EF !important;
  color: white !important;
}

.paginationButton.ellipsis {
  cursor: default;
}

.paginationButton:not(.ellipsis):hover {
  background-color: #EDF0F6;
}

.paginationButton img {
  width: 12px;
  height: 12px;
  object-fit: contain;
}

.paginationButton:not(:last-child) {
  border-right: 1px solid #EDF0F6;
}
