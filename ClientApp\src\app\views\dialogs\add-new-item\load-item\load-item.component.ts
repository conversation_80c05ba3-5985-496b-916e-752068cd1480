import { SortGird } from './../../../../dto/interface/SortGird.interface';
import { StoreService } from 'app/service/store.service';
import { InnoFormInputComponent } from './../../../../component/inno-form-input/inno-form-input.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { ItemService } from 'app/service/item.service';
import { Item } from 'app/dto/interface/Item.interface';
import { SharedModule } from 'app/module/shared.module';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { Component, DestroyRef, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { getNameTaxes } from 'app/utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-load-item',
  standalone: true,
  imports: [PagerModule, InnoModalWrapperComponent, InnoEmptyDataComponent, InnoFormInputComponent, InnoModalFooterComponent, InnoFormCheckboxComponent, SharedModule, FormatNumberPipe],
  templateUrl: './load-item.component.html',
  styleUrl: './load-item.component.scss'
})
export class LoadItemComponent implements OnInit {
  @Output() cancel: EventEmitter<any> = new EventEmitter<any>();
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  public getNameSelectedTaxes = getNameTaxes
  public currentPage: number = 1
  public totalPages = 1;
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  public listItem: Item[] = []
  public listIndexInvoiceSelected: number[] = []
  public sort: SortGird
  sortDirection: 'Ascending' | 'Descending' = 'Ascending';
  sortColumn: string = ''
  public _storeService = inject(StoreService)
  private itemService = inject(ItemService)
  private destroyRef = inject(DestroyRef)
  constructor() {

  }
  handleSort(sortDirection: string) {
    const sort = JSON.stringify(this.sort)
    const page = this.currentPage
    const textSearch = ""
    switch (sortDirection) {
      case 'Descending':
        this.LoadAllItem({ page, textSearch, sort });
        break;
      case 'Ascending':
        this.LoadAllItem({ page, textSearch, sort });
        break;
      default:
        break;
    }
  }
  sortDates(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'Ascending' ? 'Descending' : 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    } else {
      this.sortColumn = column;
      this.sortDirection = 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
      this.handleSort(this.sortDirection)
    }
  }
  LoadAllItem(args?: { page?: number, textSearch?: string, sort?: any }) {

    const payload: Parameter = {
      Page: args?.page ?? 1,
      Search: args?.textSearch ?? '',
      PageSize: this.pageSizesDefault,
      Filter: { Sort: args.sort },

    }
    this.itemService.GetAllItem(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.totalPages = res.totalRecords
        this.listItem = res.data.map(item => ({ ...item, isServices: false }));

      }
    });
  }


  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexInvoiceSelected = this.listItem.map((_item, index) => index)
    } else {
      this.listIndexInvoiceSelected = []
    }
  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      const page = this.currentPage
      const textSearch = ""
      this.pageSizesDefault = event.newProp.pageSize
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllItem({ page, textSearch });
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      const page = this.currentPage
      const textSearch = ""
      this.sort ? this.handleSort(this.sortDirection) : this.LoadAllItem({ page, textSearch });
    }
  }
  ngOnInit(): void {
    this.sortDirection = "Ascending"
    this.sortColumn = 'createdAt'
    this.sort = {
      columnName: this.sortColumn,
      direction: this.sortDirection
    }
    this.handleSort(this.sortDirection)
  }
  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected
  }
  handleQtyIndex($event, index: number) {
    this.listItem[index]["qty"] = $event
  }


  handleCancel() {
    this.cancel.emit()
  }

  handleSubmit() {
    const listInvoiceItemSelected = this.listItem.filter((_item, index) => this.listIndexInvoiceSelected.includes(index))
    this.submit.emit(listInvoiceItemSelected)
  }
}
