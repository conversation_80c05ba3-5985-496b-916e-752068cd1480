<div class="w-full flex flex-col relative" [class.error]="hasError()">
  @if(label) {
  <label [class.required]="isRequired"
    class="text-text-secondary text-text-sm-semibold mb-[2px]">{{ label
    }}</label>
  }

  @if (formControl) {
  <input
    #inputElement
    [type]="type"
    [placeholder]="placeholder"
    [formControl]="formControl"
    [autocomplete]="autocomplete"
    [pattern]="pattern"
    [mask]="mask"
    (change)="handleChange($event)"
    class="w-full h-[40px] rounded-md border-2 border-border-primary px-[12px] text-text-md-regular placeholder-text-placeholder"
    [class]="inputClassName"
    />

  <app-inno-error-message [message]="getErrorMessage()" />
  } @else {
  <input
    #inputElement
    [type]="type"
    [pattern]="pattern"
    [mask]="null"
    [autocomplete]="autocomplete"
    (change)="handleChange($event)"
    [placeholder]="placeholder"
    [value]="value"
    (blur)="removeLeadingZeros($event)"
    [ngClass]="{
      'border-2 border-border-primary': !isTable,
      'border-none  text-text-sm-regular': isTable
    }"
    class="w-full h-[40px] rounded-md  px-[12px] text-text-md-regular placeholder-text-placeholder"
    [class]="inputClassName"
    />
  }
</div>
