import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
} from '@angular/core';
import {
  FormControl,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR
} from '@angular/forms';
import { SharedModule } from '../../module/shared.module';
import { InnoErrorMMessageComponent } from '../inno-error-message/inno-error-message.component';

@Component({
  selector: 'app-inno-form-textarea',
  templateUrl: './inno-form-textarea.component.html',
  styleUrls: ['./inno-form-textarea.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoErrorMMessageComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormTextareaComponent),
      multi: true
    },
  ]
})
export class InnoFormTextareaComponent implements ControlValueAccessor {

  @Input() class?: string = '';
  @Input() isAbleResize?: boolean = false;
  @Input() isRequired?: boolean;
  @Input() label?: string = '';
  @Input() placeholder?: string = '';
  @Input() value?: string = '';
  @Output() onChange = new EventEmitter<string>();

  @Input() formControl?: FormControl;
  @Input() errorMessages?: { [key: string]: string };

  constructor() {}

  registerOnChange(fn: (value: string) => void): void {}

  registerOnTouched(fn: () => void): void {}

  setDisabledState(isDisabled: boolean): void {}

  writeValue(value: string): void {}

  hasError() {
    return (this.formControl?.invalid && (this.formControl.dirty || this.formControl.touched));
  }

  getErrorMessage(): string {
    if (!this.hasError()) return '';

    if (this.formControl?.errors && this.errorMessages) {
      for (const errorType in this.formControl.errors) {
        if (this.errorMessages[errorType]) {
          return this.errorMessages[errorType];
        }
      }
    }
    return '';
  }

  handleChange(event: any): void {
    this.onChange.emit(event?.target?.value ?? '');
  }
}
