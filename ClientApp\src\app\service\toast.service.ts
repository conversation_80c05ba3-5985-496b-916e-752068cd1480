import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ToastService {
  public target: BehaviorSubject<string>;
  public visibility: BehaviorSubject<{ [key: string]: object }[]>;
  constructor(private translateService: TranslateService) {
    this.visibility = new BehaviorSubject<{ [key: string]: object; }[]>([]);
    this.target = new BehaviorSubject('');
  }

  showSuccess(titre: string, description?: string, target?: string) {
    if (titre === '') titre = 'TOAST.Success';
    if (description === '') {
      description = this.translateService.instant(description);
    }
    const toast: { [key: string]: Object }[] = [
      {
        title: this.translateService.instant(titre),
        content: description ?? '',
        type: 'success'
      }
    ];
    if (target && target !== '') this.target.next(target);
    this.visibility.next(toast);
  }

  showWarning(titre: string, description?: string, target?: string) {
    if (titre === '') titre = 'Toast.TitleWarning.Text';
    if (description) {
      description = this.translateService.instant(description);
    }
    const toast: { [key: string]: Object }[] = [
      {
        title: this.translateService.instant(titre),
        content: description ?? '',
        type: 'warning'
      }
    ];
    if (target && target !== '') this.target.next(target);
    this.visibility.next(toast);
  }

  showError(titre: string, description?: string, target?: string) {
    if (titre === '') titre = 'Toast.TitleError.Text';
    if (description) {
      description = this.translateService.instant(description);
    }
    const toast: { [key: string]: Object }[] = [
      {
        title: this.translateService.instant(titre),
        content: description ?? '',
        type: 'error'
      }
    ];
    if (target && target !== '') this.target.next(target);
    this.visibility.next(toast);
  }

  showInfo(titre: string, description?: string, target?: string) {
    if (titre === '') titre = 'Toast.TitleInfo.Text';
    if (description) {
      description = this.translateService.instant(description);
    }
    const toast: { [key: string]: Object }[] = [
      {
        title: this.translateService.instant(titre),
        content: description ?? '',
        type: 'info'
      }
    ];
    if (target && target !== '') this.target.next(target);
    this.visibility.next(toast);
  }
}
