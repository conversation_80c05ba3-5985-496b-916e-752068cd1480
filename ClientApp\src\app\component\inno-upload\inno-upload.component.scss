.drop-zone {
  @apply border-border-secondary;
  @apply rounded-xs;
  @apply bg-bg-secondary;
  height: 100px;
  border: 1px dashed;
  padding: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.drop-zone:hover,
.drop-zone.active {
  border-style: solid;
  @apply border-border-brand;
  @apply bg-bg-brand-primary;
}

.drop-zone:hover .textTitle {
  @apply text-border-brand;
}

.previewImageWrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  @apply rounded-xs;
}

.previewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  @apply rounded-xs;
}

.buttonRemove {
  width: 20px;
  height: 20px;
  z-index: 2;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  right: 0;
  position: absolute;
  top: -5px;
  right: -5px;
  @apply bg-text-danger;

  img {
    filter: brightness(0) invert(1);
  }
}
