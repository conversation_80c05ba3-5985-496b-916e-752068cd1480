import { AuthenticationService } from 'app/auth/service/authentication.service';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { Component, DestroyRef, inject, OnDestroy, OnInit, ViewChild, viewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { NewInvoiceComponent } from '../dialog/new-invoice/new-invoice.component';
import { MatDialog } from '@angular/material/dialog';
import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { debounceHandler } from 'app/helpers/common.helper';
import { InvoiceManagementComponent } from './invoice-management/invoice-management.component';
import { InvoiceSendToMeComponent } from './invoice-send-to-me/invoice-send-to-me.component';
import { Subscription } from 'rxjs';
import { DataService } from 'app/service/data.service';
import { InvoiceViewEnum } from 'app/enum/invoice.enum';
import { StoreService } from 'app/service/store.service';
import { InvoiceService } from 'app/service/invoice.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { CalculationResponse } from 'app/dto/interface/projectResponse.interface';
import { Role } from 'app/enum/role.enum';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-invoice',
  standalone: true,
  providers: [],
  imports: [
    SharedModule,
    InnoTabsComponent,
    InnoInputSearchComponent,
    InvoiceManagementComponent,
    InvoiceSendToMeComponent,
    FormatNumberPipe,
    DecimalPipe
  ],
  templateUrl: './invoice.component.html',
  styleUrl: './invoice.component.scss'
})
export class InvoiceComponent implements OnInit, OnDestroy {
  @ViewChild('gridManagement') tab1: InvoiceManagementComponent;
  @ViewChild('gridSendToMe') tab2: InvoiceSendToMeComponent;

  public Role = Role
  public totalDraft: number = 0;
  public invoiceView = InvoiceViewEnum;
  public currentTypeView?: InvoiceViewEnum;
  public calculationInvoice: CalculationResponse | null = null
  public textSearch: string = '';
  public listTypeView = [
  ]

  private router = inject(Router);
  private dialog = inject(MatDialog)
  private _subscriptions: Subscription[] = [];
  private dataService = inject(DataService)
  public _storeService = inject(StoreService)
  public translate = inject(TranslateService)
  public authenticationService = inject(AuthenticationService)
  private _invoiceService = inject(InvoiceService)
  private destroyRef = inject(DestroyRef);

  ngOnInit(): void {
    this.listTypeView = [
      { label: this.translate.instant('INVOICES.Tabs.Created'), value: InvoiceViewEnum.Created_Tab },
      { label: this.translate.instant('INVOICES.Tabs.SentToMe'), value: InvoiceViewEnum.Sent_To_Me_Tab },
    ]
    this._subscriptions.push(
      this.dataService.GetInvoiceFilter().subscribe((data) => {
        this.currentTypeView = data?.typeView
        if (this.currentTypeView == InvoiceViewEnum.Created_Tab) {
          this.CalculateInvoice();
          return
        }
        this.CalculationInvoiceSendToMe();

      })
    )
  }
  reloadCalculation($event) {
    if (this.currentTypeView == InvoiceViewEnum.Created_Tab) {
      this.CalculateInvoice();
      return
    }
    this.CalculationInvoiceSendToMe();
  }
  CalculateInvoice() {
    this._invoiceService.CalculationInvoice(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.calculationInvoice = res;
      }
    });
  }
  CalculationInvoiceSendToMe() {
    this._invoiceService.CalculationInvoiceSendToMe(0).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.calculationInvoice = res;
      }
    });
  }


  NewInvoice() {
    this.dialog.open(NewInvoiceComponent, {
      width: "80vw",
      maxWidth: "100%",
      maxHeight: "100%",
      panelClass: 'custom_dialog',
      disableClose: true
    }).afterClosed().subscribe((res) => {
      if (res) {
        if (this.currentTypeView == InvoiceViewEnum.Created_Tab) {
          this.tab1.ReloadData();
        } else {
          this.tab2.ReloadData();
        }
      }
    });
  }
  removeQueryParams() {
    this.router.navigate([], {
      queryParams: {},
      replaceUrl: true,
    });
  }
  handleChangeTypeView(_typeView: InvoiceViewEnum) {
    this.removeQueryParams();
    const currentTypeView = this.dataService.GetInvoiceFilterValue().typeView
    if (currentTypeView === _typeView) return
    this.dataService.SetNewInvoiceFilter({ typeView: _typeView })
  }

  handleSearch = debounceHandler((textSearch = '') => {
    this.dataService.SetNewInvoiceFilter({ textSearch })
  }, 1000)

  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
