import { InnobookModalWrapperComponent } from 'app/core/innobook-modal-wrapper/innobook-modal-wrapper.component';
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-add-permission',
  standalone: true,
  imports: [InnobookModalWrapperComponent],
  templateUrl: './add-permission.component.html',
  styleUrl: './add-permission.component.scss'
})
export class AddPermissionComponent {

  static getComponent(): typeof AddPermissionComponent {
    return AddPermissionComponent;
  }
  constructor(public dialogRef: MatDialogRef<AddPermissionComponent>) {

  }

  closeDialog() {
    this.dialogRef.close();
  }
}
