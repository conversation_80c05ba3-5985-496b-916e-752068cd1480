import { Component, EventEmitter, Output } from '@angular/core';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { SharedModule } from 'app/module/shared.module';
import { MatTooltip } from '@angular/material/tooltip';

@Component({
  selector: 'app-inno-table-action',
  templateUrl: './inno-table-action.component.html',
  styleUrls: ['./inno-table-action.component.scss'],
  standalone: true,
  imports: [SharedModule, MatTooltip, InnoPopoverComponent]
})
export class InnoTableActionComponent {

  @Output() public onEdit = new EventEmitter<void>();
  @Output() public onResume = new EventEmitter<void>();
  @Output() public onDelete = new EventEmitter<void>();
  @Output() public onArchive = new EventEmitter<void>();
  @Output() public onDowload = new EventEmitter<void>();
  constructor() { }

  handleResume() {
    this.onResume.emit();
  }
  handleEdit() {
    this.onEdit.emit();
  }

  handleDelete() {
    this.onDelete.emit();
  }

  handleArchive() {
    this.onArchive.emit();
  }
  handleDowload() {
    this.onDowload.emit();
  }

}
