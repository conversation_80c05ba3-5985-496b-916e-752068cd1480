<app-innobook-modal-wrapper (onClose)="closeDialog()">

    <div class="input-content">
        <form [formGroup]="businessForm" (ngSubmit)="onSubmit()">
            <div class="mb-4">
                <h6 class="fw-bold m-0 text-center">Create your new
                    business</h6>
            </div>

            <div class="mb-6">
                <label for="business"
                    class="block mb-2 text-sm font-medium text-gray-900 text-start">Business
                    Name
                </label>
                <input formControlName="business" type="text" id="business"
                    class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5" />
                @if (
                (f['business'].dirty || f['business'].touched) &&
                f['business'].hasError('required')
                ) {
                <mat-error class="matError">Business is required</mat-error>
                }
            </div>
            <div class="mb-6">
                <label for="business"
                    class="block mb-2 text-sm font-medium text-gray-900 text-start">Email Business
                    
                </label>
                <input type="email" id="_email"
                formControlName="email"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                placeholder="Email" required />
        @if (
        (f['email'].dirty || f['email'].touched) &&
        f['email'].hasError('required')
        ) {
        <mat-error class="matError">email is required</mat-error>
        }
        @if (
        (f['email'].dirty || f['email'].touched) &&
        f['email'].hasError('email')
        ) {
        <mat-error class="matError">Invalid email</mat-error>
        }

            </div>
            <div class="mb-6">
                <label for="countries"
                    class="block mb-2 text-sm font-medium text-gray-900 text-start">Country
                </label>
                <select formControlName="country" id="country"
                    class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">

                    @for(country of countries; track country; let i = $index )
                    {
                    <option [value]="country.name">
                        {{ country.name }}
                    </option>
                    }

                </select>
                @if (
                (f['country'].dirty || f['country'].touched) &&
                f['country'].hasError('required')
                ) {
                <mat-error class="matError">Country is required</mat-error>
                }
            </div>

            <div class="mb-6">
                <label for="note"
                    class="block mb-2 text-sm font-medium text-gray-900  text-start">What
                    do you do for your
                    clients?
                </label>
                <textarea id="message" rows="3"
                    formControlName="note"
                    class="block p-2.5 w-full text-sm text-gray-900 bg-gray-100 rounded-lg border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="What do you do for your clients?"></textarea>

            </div>
            <div class="mt-3 flex justify-center">
                <button type="submit" [disabled]="!businessForm.valid"
                    class="text-white  font-medium rounded-lg text-sm w-full sm:w-auto px-5 py-2.5 text-center"
                    [ngClass]="{'bg-green-700 hover:bg-green-800' : businessForm.valid,'bg-gray-400': !businessForm.valid}">Save
                    Changes</button>
            </div>
        </form>
    </div>
</app-innobook-modal-wrapper>