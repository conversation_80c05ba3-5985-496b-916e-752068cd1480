import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoErrorMMessageComponent } from 'app/component/inno-error-message/inno-error-message.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { StoreService } from 'app/service/store.service';
import { UserBusinessService } from 'app/service/user-business.service';
import { User } from 'app/dto/interface/user.interface';
import { ToastService } from 'app/service/toast.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { SpinnerService } from 'app/service/spinner.service';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SharedModule } from 'app/module/shared.module';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-edit-profile-team-members',
  standalone: true,
  imports: [RouterModule, InnoModalWrapperComponent, SharedModule, InnoFormInputComponent, InnoErrorMMessageComponent, MatFormFieldModule],
  templateUrl: './edit-profile-team-members.component.html',
  styleUrl: './edit-profile-team-members.component.scss'
})
export class EditProfileTeamMembersComponent implements OnInit {
  _id!: string;
  public profileForm!: UntypedFormGroup;

  private spiner_services = inject(SpinnerService)
  private auth_services = inject(AuthenticationService)
  router = inject(Router);
  private _toastService = inject(ToastService)
  destroyRef = inject(DestroyRef);
  private userbusiness_services = inject(UserBusinessService)
  private formBuilder = inject(UntypedFormBuilder)
  protected activatedRoute = inject(ActivatedRoute);
  public _storeService = inject(StoreService)
  private translate = inject(TranslateService)
  constructor(private dialogRef: MatDialogRef<EditProfileTeamMembersComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,) {
    this.profileForm = this.formBuilder.group({
      firstname: ["", Validators.compose([Validators.required])],
      lastname: ["", Validators.compose([Validators.required])],
      email: [{ value: "", disabled: true }, Validators.compose([Validators.required, Validators.email])],
    },

    );
  }

  handleClose() {
    this.dialogRef.close();
  }
  static getComponent(): typeof EditProfileTeamMembersComponent {
    return EditProfileTeamMembersComponent;
  }
  get f() {
    return this.profileForm.controls as Record<string, FormControl>;;
  }
  ngOnInit(): void {
    this.GetInforUser(this.data);
  }
  _handleData(_data: any) {
    this.profileForm.controls["firstname"].setValue(_data.firstName)
    this.profileForm.controls["lastname"].setValue(_data.lastName)
    this.profileForm.controls["email"].setValue(_data.email)
  }

  GetInforUser(_id: string) {
    this.userbusiness_services.userBusinessById(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this._handleData(res.user)
      }

    })
  }
  onSubmit() {
    this.spiner_services.show();
    let payload: User = {
      firstName: this.profileForm.controls["firstname"].value,
      lastName: this.profileForm.controls["lastname"].value,
      email: this.profileForm.controls["email"].value,
    }
    this.auth_services.UpdateUserProfileByEmail(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.spiner_services.hide();
        this._toastService.showSuccess(this.translate.instant("TOAST.Update"), this.translate.instant("TOAST.Success"))
        this.dialogRef.close(res);

      }
    }
    )
  }
}
