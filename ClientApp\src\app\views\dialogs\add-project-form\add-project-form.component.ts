import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { ModifyTaxesDialog } from './../../../service/dialog/modify-taxes.dialog';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoFormProjectTypeComponent } from './../../../component/inno-form-project-type/inno-form-project-type.component';
import { ToastService } from 'app/service/toast.service';
import { ProjectService } from './../../../service/project.service';
import { StoreService } from './../../../service/store.service';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { Component, DestroyRef, Inject, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AvatarModule } from 'ngx-avatars';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { SharedModule } from 'app/module/shared.module';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { InnoSelectSearchUserComponent } from 'app/component/inno-select-search-user/inno-select-search-user.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { MatTooltip } from '@angular/material/tooltip';
import { Project } from '../../../dto/interface/project.interface';
import { TimetrackingService } from 'app/service/timetracking.service';
import { PROJECTTYPE } from 'app/utils/project-type';
import { ProjectTypeEnum } from 'app/enum/projectType.enum';
import { AddServiceForProjectDialog } from 'app/service/dialog/add-service-for-project.dialog';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
import { ModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-add-project-form',
  standalone: true,
  imports: [
    MatTooltip,
    SharedModule,
    AvatarModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormInputComponent,
    InnoFormTextareaComponent,
    InnoSelectSearchProjectComponent,
    InnoSelectSearchUserComponent,
    InnoFormSelectSearchComponent,
    InnoFormDatepickerComponent,
    InnoFormProjectTypeComponent,
    InnoSelectSearchUserComponent,
    InnoTableActionComponent,
    InnoModalFooterComponent,
    InnoFormCheckboxComponent,
    FormatNumberPipe
  ],
  templateUrl: './add-project-form.component.html',
  styleUrl: './add-project-form.component.scss'
})
export class AddProjectFormComponent implements OnInit, OnDestroy {
  public clientOptions: IFilterDropdownOption[] = []
  public projectType: any[] = PROJECTTYPE
  @ViewChild('selectProjectTypeElement') selectProjectTypeElement!: InnoFormProjectTypeComponent;
  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;
  public listService: any[] = [];
  public listChooseUser: any[] = [];
  public listRemoveMember: any[] = [];
  public showErrorMember: boolean = false
  public hourly = ProjectTypeEnum.Hourly
  public previewBillable: boolean = true;
  public createdAt: any;
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes

  public newprojectForm!: UntypedFormGroup;
  private formBuilder = inject(UntypedFormBuilder)
  private destroyRef = inject(DestroyRef);
  public _storeService = inject(StoreService)
  private _toastService = inject(ToastService)
  private _projectService = inject(ProjectService)
  public auth_services = inject(AuthenticationService)
  public timetrackingService = inject(TimetrackingService)
  private dropdownOptionService = inject(DropdownOptionsService)
  private modifyTaxesDialog = inject(ModifyTaxesDialog)
  private translate = inject(TranslateService)
  private addServiceForProjectDialog = inject(AddServiceForProjectDialog)
  private modifyEditItemAndServiceDialog = inject(ModifyItemsAndServiceDialog)
  static getComponent(): typeof AddProjectFormComponent {
    return AddProjectFormComponent;
  }
  constructor(public dialogRef: MatDialogRef<AddProjectFormComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.newprojectForm = this.formBuilder.group({
      option: [
        this.data == null ? 1 : this.data?.option == 0 ? 1 : this.data?.option,
      ],
      projectname: [
        '',
        Validators.compose([Validators.required])],
      clientId: [
        null,
        Validators.compose([Validators.required])],
      endDate: [
        null],
      description: [''],
      total: [''],
      flat: [''],
      hourly: [''],
      service: [''],
    });

  }

  ngOnInit(): void {
    this.dropdownOptionService.getDropdownOptionsProjectAndClient({ isOnlyClient: true })
      .then((clientOptions) => {
        this.clientOptions = clientOptions
      })

    if (!this.data) {
      this.auth_services.GetUser().pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          if (res) {
            let item = {
              userId: res.id,
              status: 0,
              lastName: res.lastName,
              email: res.email,
              firstName: res.firstName
            }
            this.listChooseUser.push(item);
          }
        }
      })
    }
    else {
      this.GetProjectById()
    }
  }
  handleDataEdit(data: Project) {
    this.f['projectname'].setValue(data.projectName)
    this.f['clientId'].setValue(data.clientId)
    this.f['description'].setValue(data.description)
    this.f['endDate'].setValue(data.endDate)
    this.f['flat'].setValue(data.flatRate || 0)
    this.f['total'].setValue(data.totalHours || 0)
    this.f['hourly'].setValue(data.hourlyRate || 0)
    this.f['option'].setValue(data.option || 1)
  }
  GetProjectById() {
    this.listChooseUser = [];
    this._projectService.GetProjectById(this.data)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        if (res) {
          this.handleDataEdit(res)
          res?.members?.forEach((element: any) => {
            let dt = {
              id: element.id,
              status: 0,
              userId: element.user.id,
              email: element.user.email,
              lastName: element.user.lastName,
              firstName: element.user.firstName
            }
            this.listChooseUser.push(dt)
          });
          if (res?.services) {
            this.listService = res?.services

          }
          this.previewBillable = res?.billable ?? false
          this.createdAt = res.createdAt
        }
      })
  }
  handleClose() {
    this.dialogRef.close();
  }

  handleSubmitForm() {
    if (this.newprojectForm.invalid) {
      this.markAllControlsAsTouched();
      return;
    }
    if (this.f['endDate'].value) {
      const endDate = new Date(this.f['endDate'].value);
      if (isNaN(endDate.getTime())) {
        this.markAllControlsAsTouched();
        return;
      }
    }

    const payload: Project = {
      projectName: this.f['projectname'].value,
      clientId: this.f['clientId'].value,
      description: this.f['description'].value,
      endDate: this.f['endDate'].value,
      flatRate: this.f['flat'].value || 0,
      totalHours: this.f['total'].value || 0,
      hourlyRate: this.f['hourly'].value || 0,
      members: this.listChooseUser,
      option: this.f['option'].value || 1,
      services: this.listService.map(item => ({
        ...item,
        taxes: item.taxes.some(tax => tax.companyTax)
          ? item.taxes.map(({ companyTax, ...rest }) => rest)
          : item.taxes.filter(tax => tax.selected)
      })),
      billable: this.previewBillable
    }

    if (this.data) {
      payload['id'] = this.data
      payload['createdAt'] = this.createdAt
      this._projectService.UpdateProject(payload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe(res => {
          if (res) {
            this._toastService.showSuccess(this.translate.instant("TOAST.Update"), this.translate.instant("TOAST.Success"))
            this.dialogRef.close(res);
          }
          else {
            this._toastService.showError(this.translate.instant("TOAST.Fail"),)
          }
        })
    } else {
      this._projectService.CreateProject(payload)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe({
          next: (res) => {
            if (res) {
              this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"))
              this.dialogRef.close(res);
            }
          }
        });
    }
  }

  handleSelectClient(item: IFilterDropdownOption) {
    this.newprojectForm.controls["clientId"].setValue(item.value)
    this.selectSearchClientElement.handleCloseSearchResult()
  }
  handleSelectProjectType(item: any) {
    this.newprojectForm.controls["option"].setValue(item.value)
    this.selectProjectTypeElement.handleCloseSearchResult()
  }


  handleCancel() {
    this.dialogRef.close();
  }


  handleDeleteInvoiceItem(i: number) {
    this.listService.splice(i, 1);
  }


  get f() {
    return this.newprojectForm.controls as Record<string, FormControl>;;
  }

  Remove(i: number) {
    if (this.data) {
      this.listChooseUser[i].status = 1
      return;
    }
    this.listChooseUser.splice(i, 1);


  }


  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  handleSelectUser(item: IFilterDropdownOption) {
    let isExist = this.listChooseUser.find(x => x.userId == item.value)
    if (isExist) {
      this._toastService.showInfo(this.translate.instant("COMMON.UserExist"), " ")
      return
    }

    const dt = {
      userId: item.value,
      status: 0,
      lastName: item.metadata?.lastName,
      firstName: item.metadata?.firstName,
      email: item.metadata?.email,
      projectId: this.data
    }
    this.listChooseUser.push(dt)
  }

  handleAddService() {
    const dialogRef = this.addServiceForProjectDialog.open({
      projectId: this.data,
      listService: this.listService.filter(x => x.isNewItem)
    });
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          return;
        }
        if (res) {
          this.listService = this.listService.concat(res);
        }

      })
    });
  }
  handleModifyTaxes(item: any, index: number) {
    const itemInvoiceOld = structuredClone(this.listService);
    let taxes = [];
    item.forEach(itemtax => {
      if (itemtax.companyTax) {
        itemtax.companyTax.selected = true;
        taxes.push(itemtax.companyTax)
      }
      else {
        taxes.push(itemtax)
      }
    });

    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          this.listService = itemInvoiceOld
          return;
        }
        const currentPaymentInvoice = this.listService
        itemInvoiceOld.forEach((item: any, indexOld: number) => {
          if (index != indexOld) {
            currentPaymentInvoice[indexOld].taxes = item.taxes.filter(x => x.selected)
          }
        });


        currentPaymentInvoice[index].taxes = res.taxes.filter(x => x.selected)
        this.listService = currentPaymentInvoice.slice()
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.listService
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });

        }
      })
    });
  }

  handleEditService(item: any) {
    const dialogRef = this.modifyEditItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Service,
      isShowProject: true,
      serviceInfo: item
    });
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res: any) => {
        if (res) {
          this.GetProjectById()
        }
      });
    });
  }
  handleChangeBillable(value: any) {
    const billable = value
    this.previewBillable = billable
  }
  ngOnDestroy(): void {
    this.listChooseUser = [];
  }
}
