import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'phoneMask',
    standalone: true
})
export class PhoneMaskPipe implements PipeTransform {
    transform(value: string | number): string {
        if (!value) return '';

        let phone = value.toString().replace(/\D/g, '');

        if (phone.length === 10) {
            return `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;
        }

        return value.toString();
    }
}
