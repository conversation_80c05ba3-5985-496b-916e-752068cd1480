import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SharedModule } from '../../module/shared.module';
@Component({
  selector: 'app-inno-modal-wrapper',
  templateUrl: './inno-modal-wrapper.component.html',
  styleUrls: ['./inno-modal-wrapper.component.scss'],
  standalone: true,
  imports: [SharedModule]
})

export class InnoModalWrapperComponent {

  @Input() public title?: string;
  @Output() public onClose = new EventEmitter<void>();

  constructor() {}

  handleClose(): void {
    this.onClose.emit();
  }
}

