import { PaginatedResponse } from './../dto/interface/paginatedResponse.interface';
import { environment } from 'environments/environment';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CompanyTax } from 'app/dto/interface/CompanyTax.interface';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class CompanyTaxService {
  private http = inject(HttpClient)
  constructor() { }
  Create(payload: any): Observable<CompanyTax> {
    return this.http.post<CompanyTax>(UrlApi + '/CompanyTax/Create', payload);
  }
  GetAllCompanyTax(params: Parameter): Observable<PaginatedResponse<CompanyTax>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<CompanyTax>>(UrlApi + `/CompanyTax/GetAllCompanyTax`, { params: query });
  }


}
